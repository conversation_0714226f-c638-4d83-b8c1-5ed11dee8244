<?php

namespace App\Modules\Settings\Domain\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Modules\Shared\Domain\Traits\HasUUID;
use App\Modules\Shared\Domain\Traits\HasOrganizationId;
use App\Modules\Organizations\Domain\Models\Organization;

class OrganizationSetting extends Model
{
    use HasFactory, HasUUID, HasOrganizationId;

    protected $table = 'organization_settings';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'organization_id',
        'key',
        'value',
    ];

    protected $casts = [
        'value' => 'json',
    ];

    // Relationships
    public function organization()
    {
        return $this->belongsTo(Organization::class, 'organization_id');
    }
}
