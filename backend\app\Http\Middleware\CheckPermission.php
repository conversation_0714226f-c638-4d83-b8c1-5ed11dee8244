<?php

namespace App\Http\Middleware;

use App\Modules\RolesPermissions\Domain\Services\AuthorizationService;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckPermission
{
    public function __construct(private readonly AuthorizationService $authz)
    {
    }

    public function handle(Request $request, Closure $next, string $permission): Response
    {
        $user = $request->user();
        if (!$user) {
            return response()->json(['message' => 'Unauthenticated'], 401);
        }

        if (!$this->authz->userHasPermission($user, $permission)) {
            return response()->json(['message' => 'Forbidden: missing permission'], 403);
        }

        return $next($request);
    }
}
