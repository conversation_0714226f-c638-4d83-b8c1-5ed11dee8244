# Core Hierarchy Structure - Complete Implementation Guide

## 📊 Three-Level Mandatory Hierarchy

Your ERP system has **3 mandatory hierarchy levels** with complete role-based access control:

```
┌─────────────────────────────────────────────────────────────┐
│                    LEVEL 1: PORTAL                          │
│                   (System-Wide Admin)                       │
│                                                             │
│  • Scope: Global (no organization filtering)              │
│  • Roles: System Admin, System Auditor, System Support    │
│  • Users: <EMAIL>, <EMAIL>, etc.      │
│  • Mandatory: YES (1 per system)                          │
│  • Parent: None (root)                                    │
└──────────────────────┬──────────────────────────────────────┘
                       │
                       ▼
┌─────────────────────────────────────────────────────────────┐
│               LEVEL 2: ORGANIZATION                         │
│              (Tenant/Company Level)                         │
│                                                             │
│  • Scope: organization_id (NOT NULL in users table)       │
│  • Roles: Org Admin, Org Manager, Org Member             │
│  • Users: All organization members (7+ users)            │
│  • Mandatory: YES (every user must belong to one)        │
│  • Parent: None (parent_id = NULL)                       │
│  • Constraint: users.organization_id NOT NULL            │
└──────────────────────┬──────────────────────────────────────┘
                       │
        ┌──────────────┼──────────────┐
        │              │              │
        ▼              ▼              ▼
┌──────────────┐ ┌──────────────┐ ┌──────────────┐
│ LEVEL 3:     │ │ LEVEL 3:     │ │ LEVEL 3:     │
│ SUB-ORG 1    │ │ SUB-ORG 2    │ │ SUB-ORG 3    │
│ (Optional)   │ │ (Optional)   │ │ (Optional)   │
│              │ │              │ │              │
│ Sales Div    │ │ Tech Div     │ │ HR Div       │
│              │ │              │ │              │
│ Scope:       │ │ Scope:       │ │ Scope:       │
│ org-001      │ │ org-001      │ │ org-001      │
│ (inherited)  │ │ (inherited)  │ │ (inherited)  │
│              │ │              │ │              │
│ Roles:       │ │ Roles:       │ │ Roles:       │
│ • Admin      │ │ • Admin      │ │ • Admin      │
│ • Manager    │ │ • Manager    │ │ • Manager    │
│ • Member     │ │ • Member     │ │ • Member     │
│              │ │              │ │              │
│ Users: 3     │ │ Users: 3     │ │ Users: 3     │
│              │ │              │ │              │
│ Mandatory:   │ │ Mandatory:   │ │ Mandatory:   │
│ NO (Optional)│ │ NO (Optional)│ │ NO (Optional)│
│              │ │              │ │              │
│ Parent:      │ │ Parent:      │ │ Parent:      │
│ org-001      │ │ org-001      │ │ org-001      │
│ (parent_id)  │ │ (parent_id)  │ │ (parent_id)  │
└──────────────┘ └──────────────┘ └──────────────┘
```

---

## 🔍 Level Details

### LEVEL 1: PORTAL (System Administration)

**Purpose**: System-wide administration and control

**Database Representation**:
```sql
organizations table:
├─ id: UUID
├─ parent_id: NULL (root level)
├─ type: 'portal'
├─ name: 'System Portal'
└─ status: 'active'
```

**Roles Available**:
```
1. System Administrator
   └─ Full system access
   └─ Manage all organizations
   └─ System settings
   └─ User: <EMAIL>

2. System Auditor
   └─ View-only access
   └─ Audit logs
   └─ Cannot modify
   └─ User: <EMAIL>

3. System Support
   └─ Support operations
   └─ User assistance
   └─ Limited system access
   └─ User: <EMAIL>
```

**Key Characteristics**:
- ✅ Mandatory: YES (1 per system)
- ✅ Scope: Global (no organization_id)
- ✅ Users: System administrators only
- ✅ Nesting: Cannot be nested

---

### LEVEL 2: ORGANIZATION (Root Organization)

**Purpose**: Tenant/company-level organization

**Database Representation**:
```sql
organizations table:
├─ id: UUID (e.g., org-001)
├─ parent_id: NULL (root level)
├─ type: 'organization'
├─ name: 'Acme Corporation'
├─ code: 'ACME-001'
└─ status: 'active'

users table:
├─ organization_id: org-001 (NOT NULL - MANDATORY)
└─ All users belong to this organization
```

**Roles Available**:
```
1. Organization Administrator
   └─ Manage entire organization
   └─ Create/manage users
   └─ Manage roles & permissions
   └─ Organization settings
   └─ User: <EMAIL>

2. Organization Manager
   └─ Manage teams & projects
   └─ Manage sub-organizations
   └─ Resource allocation
   └─ Cannot manage org settings
   └─ User: <EMAIL>

3. Organization Member
   └─ Basic operations
   └─ Access assigned resources
   └─ Limited permissions
   └─ Cannot manage organization
   └─ Users: <EMAIL>, <EMAIL>, etc.
```

**Key Characteristics**:
- ✅ Mandatory: YES (every user must belong to one)
- ✅ Scope: organization_id (NOT NULL in users table)
- ✅ Users: All organization members
- ✅ Nesting: Cannot be nested (parent_id = NULL)
- ✅ Constraint: users.organization_id NOT NULL (database enforced)

**Users at This Level**:
```
Organization: Acme Corporation (org-001)
├─ <EMAIL> (Org Administrator)
├─ <EMAIL> (Org Manager)
├─ <EMAIL> (Org Member)
├─ <EMAIL> (Org Member)
├─ <EMAIL> (Org Member)
├─ <EMAIL> (Org Member)
└─ <EMAIL> (Org Member)
```

---

### LEVEL 3: SUB-ORGANIZATION (Nested Organization)

**Purpose**: Optional nested organizations within parent

**Database Representation**:
```sql
organizations table:
├─ id: UUID (e.g., org-002)
├─ parent_id: org-001 (references parent organization)
├─ type: 'sub-organization'
├─ name: 'Acme Sales Division'
├─ code: 'ACME-SALES-001'
└─ status: 'active'

users table:
├─ organization_id: org-001 (same as parent - INHERITED)
└─ All sub-org users still belong to parent organization

role_assignments table:
├─ user_id: user-005
├─ role_id: sub-org-admin-role
├─ organization_id: org-001 (parent org)
├─ scope: 'organization'
└─ scope_id: org-002 (sub-org ID)
```

**Roles Available**:
```
1. Sub-Organization Administrator
   └─ Manage sub-organization
   └─ Manage sub-org users
   └─ Manage sub-org roles
   └─ Sub-org settings
   └─ User: <EMAIL>

2. Sub-Organization Manager
   └─ Manage sub-org operations
   └─ Resource allocation
   └─ Cannot manage org settings
   └─ User: <EMAIL>

3. Sub-Organization Member
   └─ Basic operations
   └─ Access assigned resources
   └─ Limited permissions
   └─ User: <EMAIL>
```

**Key Characteristics**:
- ✅ Mandatory: NO (optional, can be created as needed)
- ✅ Scope: organization_id (inherited from parent)
- ✅ Users: Sub-org members (still belong to parent org)
- ✅ Nesting: Can be nested infinitely (parent_id = UUID)
- ✅ Constraint: organizations.parent_id NULLABLE (optional)

**Multiple Sub-Organizations Example**:
```
Organization: Acme Corporation (org-001)
├─ Sub-Organization 1: Sales Division (org-002)
│  ├─ <EMAIL> (Sub-Org Admin)
│  ├─ <EMAIL> (Sub-Org Manager)
│  └─ <EMAIL> (Sub-Org Member)
│
├─ Sub-Organization 2: Tech Division (org-003)
│  ├─ <EMAIL> (Sub-Org Admin)
│  ├─ <EMAIL> (Sub-Org Manager)
│  └─ <EMAIL> (Sub-Org Member)
│
└─ Sub-Organization 3: HR Division (org-004)
   ├─ <EMAIL> (Sub-Org Admin)
   ├─ <EMAIL> (Sub-Org Manager)
   └─ <EMAIL> (Sub-Org Member)
```

**Infinite Nesting Example**:
```
Organization: Acme Corporation (org-001)
└─ Sub-Organization 1: Sales Division (org-002)
   └─ Sub-Organization 1.1: North Region (org-005)
      └─ Sub-Organization 1.1.1: New York Office (org-006)
         └─ Sub-Organization *******: Sales Team (org-007)
```

---

## 🔐 Authorization & Scoping

### How Scoping Works

**Organization-Level Role** (Entire Organization):
```php
RoleAssignment::create([
    'user_id' => 'alice-uuid',
    'role_id' => 'org-admin-role',
    'organization_id' => 'org-001',
    'scope' => 'organization',
    'scope_id' => null,  // NULL = entire organization
]);
// Result: alice is admin of entire Acme Corporation
```

**Sub-Organization-Level Role** (Specific Sub-Org):
```php
RoleAssignment::create([
    'user_id' => 'eve-uuid',
    'role_id' => 'sub-org-admin-role',
    'organization_id' => 'org-001',  // Parent org (MANDATORY)
    'scope' => 'organization',
    'scope_id' => 'org-002',  // Sub-org ID
]);
// Result: eve is admin of Sales Division only
```

**Optional Scope** (Department/Team/User):
```php
RoleAssignment::create([
    'user_id' => 'user-uuid',
    'role_id' => 'manager-role',
    'organization_id' => 'org-001',
    'scope' => 'department',  // Optional scope
    'scope_id' => 'dept-001',  // Optional scope_id
]);
// Result: user is manager of specific department only
```

---

## 📊 Complete User Distribution

| Level | Organization | Role | User | Scope | scope_id |
|-------|--------------|------|------|-------|----------|
| 1 | Portal | System Admin | <EMAIL> | global | - |
| 1 | Portal | System Auditor | <EMAIL> | global | - |
| 1 | Portal | System Support | <EMAIL> | global | - |
| 2 | org-001 | Org Admin | <EMAIL> | organization | NULL |
| 2 | org-001 | Org Manager | <EMAIL> | organization | NULL |
| 2 | org-001 | Org Member | <EMAIL> | organization | NULL |
| 2 | org-001 | Org Member | <EMAIL> | organization | NULL |
| 2 | org-001 | Org Member | <EMAIL> | organization | NULL |
| 2 | org-001 | Org Member | <EMAIL> | organization | NULL |
| 2 | org-001 | Org Member | <EMAIL> | organization | NULL |
| 3 | org-001 | Sub-Org Admin | <EMAIL> | organization | org-002 |
| 3 | org-001 | Sub-Org Manager | <EMAIL> | organization | org-002 |
| 3 | org-001 | Sub-Org Member | <EMAIL> | organization | org-002 |
| 3 | org-001 | Sub-Org Admin | <EMAIL> | organization | org-003 |
| 3 | org-001 | Sub-Org Manager | <EMAIL> | organization | org-003 |
| 3 | org-001 | Sub-Org Member | <EMAIL> | organization | org-003 |
| 3 | org-001 | Sub-Org Admin | <EMAIL> | organization | org-004 |
| 3 | org-001 | Sub-Org Manager | <EMAIL> | organization | org-004 |
| 3 | org-001 | Sub-Org Member | <EMAIL> | organization | org-004 |

**Total Users**: 19  
**Total Roles**: 9 (3 per level)

---

## 🔑 Database Constraints

### Mandatory Constraint (Level 2)
```sql
ALTER TABLE users 
ADD CONSTRAINT users_organization_id_not_null 
CHECK (organization_id IS NOT NULL);
```
**Effect**: Every user MUST belong to an organization

### Optional Constraint (Level 3)
```sql
ALTER TABLE organizations 
ADD CONSTRAINT organizations_parent_id_nullable 
CHECK (parent_id IS NULL OR parent_id IS NOT NULL);
```
**Effect**: Sub-organizations are optional (parent_id can be NULL)

### Optional Constraint (Optional Levels)
```sql
ALTER TABLE role_assignments 
ADD CONSTRAINT role_assignments_scope_id_nullable 
CHECK (scope_id IS NULL OR scope_id IS NOT NULL);
```
**Effect**: Department/Team/User scopes are optional

---

## 🎯 Implementation Checklist

### Setup Portal (Level 1)
- [ ] Create portal organization
  ```php
  Organization::create([
      'type' => 'portal',
      'name' => 'System Portal',
      'code' => 'PORTAL-001',
  ]);
  ```
- [ ] Create system roles
  ```php
  Role::create(['name' => 'System Administrator', 'slug' => 'system-admin']);
  Role::create(['name' => 'System Auditor', 'slug' => 'system-auditor']);
  Role::create(['name' => 'System Support', 'slug' => 'system-support']);
  ```
- [ ] Create system users
  ```php
  User::create(['email' => '<EMAIL>', 'organization_id' => 'portal-id']);
  ```

### Setup Organization (Level 2)
- [ ] Create root organization
  ```php
  Organization::create([
      'type' => 'organization',
      'name' => 'Acme Corporation',
      'code' => 'ACME-001',
  ]);
  ```
- [ ] Create organization roles
  ```php
  Role::create(['name' => 'Organization Admin', 'slug' => 'org-admin']);
  Role::create(['name' => 'Organization Manager', 'slug' => 'org-manager']);
  Role::create(['name' => 'Organization Member', 'slug' => 'org-member']);
  ```
- [ ] Create organization users
  ```php
  User::create(['email' => '<EMAIL>', 'organization_id' => 'org-001']);
  ```

### Setup Sub-Organizations (Level 3 - Optional)
- [ ] Create sub-organizations
  ```php
  Organization::create([
      'parent_id' => 'org-001',
      'type' => 'sub-organization',
      'name' => 'Sales Division',
      'code' => 'ACME-SALES-001',
  ]);
  ```
- [ ] Create sub-org roles
  ```php
  Role::create(['name' => 'Sub-Org Admin', 'slug' => 'sub-org-admin']);
  ```
- [ ] Assign users to sub-orgs
  ```php
  RoleAssignment::create([
      'user_id' => 'eve-uuid',
      'role_id' => 'sub-org-admin-role',
      'organization_id' => 'org-001',
      'scope' => 'organization',
      'scope_id' => 'org-002',
  ]);
  ```

---

## 📈 Scaling Considerations

### Single Organization
```
Portal
└─ Organization (Acme Corp)
   └─ 7 users
   └─ 3 roles
   └─ 10 permissions
```

### Multiple Organizations
```
Portal
├─ Organization 1 (Acme Corp)
│  └─ 7 users
├─ Organization 2 (TechCorp)
│  └─ 5 users
└─ Organization 3 (FinanceInc)
   └─ 8 users
```

### Complex Hierarchy
```
Portal
└─ Organization (Acme Corp)
   ├─ Sub-Org 1 (Sales)
   │  ├─ Sub-Sub-Org 1.1 (North)
   │  └─ Sub-Sub-Org 1.2 (South)
   ├─ Sub-Org 2 (Tech)
   │  ├─ Sub-Sub-Org 2.1 (Backend)
   │  └─ Sub-Sub-Org 2.2 (Frontend)
   └─ Sub-Org 3 (HR)
```

---

## ✅ Verification Summary

| Aspect | Status | Evidence |
|--------|--------|----------|
| Portal is mandatory | ✅ YES | System-wide admin layer |
| Organization is mandatory | ✅ YES | users.organization_id NOT NULL |
| Sub-Organization is optional | ✅ YES | organizations.parent_id NULLABLE |
| Department is optional | ✅ YES | role_assignments.scope_id NULLABLE |
| Team is optional | ✅ YES | role_assignments.scope_id NULLABLE |
| User scope is optional | ✅ YES | role_assignments.scope_id NULLABLE |

---

## 📚 Related Documentation

- **CORE_HIERARCHY_STRUCTURE.md** - Detailed analysis with code examples
- **CORE_HIERARCHY_VISUAL.txt** - Complete ASCII diagrams
- **CORE_HIERARCHY_SUMMARY.md** - Executive summary
- **HIERARCHY_LEVELS_DIAGRAM.md** - Full 6-level hierarchy reference
- **HIERARCHY_DATA_MODEL.md** - Database schema details

---

**Document Generated**: October 29, 2025  
**Status**: ✅ VERIFIED - 3 Mandatory Levels Confirmed  
**Scope**: Complete implementation guide for core hierarchy
