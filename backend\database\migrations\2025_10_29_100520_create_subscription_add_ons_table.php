<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscription_add_ons', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('subscription_id')->comment('Parent subscription');
            $table->enum('add_on_type', [
                'user',
                'sub_org',
                'storage',
                'hierarchy_level',
                'module',
                'api_calls'
            ])->comment('Type of add-on');
            $table->integer('quantity')->default(1)->comment('Number of units');
            $table->decimal('unit_price', 10, 2)->comment('Price per unit per month');
            $table->decimal('total_price', 10, 2)->comment('Total price (quantity × unit_price)');
            $table->timestamp('starts_at')->comment('When add-on became active');
            $table->timestamp('ends_at')->nullable()->comment('When add-on expires (null = active)');
            $table->json('metadata')->nullable()->comment('Additional info like module name');
            $table->timestamps();
            $table->softDeletes();
            
            // Indexes
            $table->index('subscription_id');
            $table->index('add_on_type');
            $table->index('starts_at');
            $table->index(['subscription_id', 'add_on_type']);
            
            // Foreign key
            $table->foreign('subscription_id')
                  ->references('id')
                  ->on('subscriptions')
                  ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscription_add_ons');
    }
};
