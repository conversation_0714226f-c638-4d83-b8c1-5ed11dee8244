<?php

namespace Tests\Feature\Integration;

use Tests\TestCase;
use Tests\Support\CreatesOrgUser;
use Illuminate\Foundation\Testing\RefreshDatabase;

class WebhooksApiTest extends TestCase
{
    use RefreshDatabase, CreatesOrgUser;

    public function test_webhooks_crud_and_events(): void
    {
        $org = $this->createOrganization();
        $user = $this->createUserInOrg($org);
        $this->actingAs($user);

        // Create
        $id = $this->postJson('/api/v1/webhooks', [
            'url' => 'https://example.com/webhook',
            'events' => ['payment.processed'],
            'is_active' => true,
            'retry_count' => 3,
            'timeout' => 10,
        ])->assertStatus(201)->json('id');

        // Index
        $this->getJson('/api/v1/webhooks')->assertStatus(200);
        // Show
        $this->getJson('/api/v1/webhooks/'.$id)->assertStatus(200);
        // Update
        $this->patchJson('/api/v1/webhooks/'.$id, ['timeout' => 15])->assertStatus(200);
        // Events
        $this->getJson('/api/v1/webhooks/'.$id.'/events')->assertStatus(200);
        // Destroy
        $this->deleteJson('/api/v1/webhooks/'.$id)->assertStatus(200)->assertJsonFragment(['deleted' => true]);
    }
}
