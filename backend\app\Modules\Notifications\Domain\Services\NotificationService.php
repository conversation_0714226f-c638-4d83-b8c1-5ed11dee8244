<?php

namespace App\Modules\Notifications\Domain\Services;

use App\Modules\Shared\Domain\Services\BaseService;
use App\Modules\Notifications\Domain\Models\Notification;
use App\Modules\Notifications\Domain\Models\NotificationTemplate;
use App\Modules\Notifications\Domain\Repositories\NotificationRepository;
use App\Modules\Users\Domain\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;

class NotificationService extends BaseService
{
    public function __construct(private readonly NotificationRepository $notifications)
    {
    }

    /**
     * Send email notification
     */
    public function sendEmail(User $user, string $subject, string $body, array $data = []): Notification
    {
        return DB::transaction(function () use ($user, $subject, $body, $data) {
            $notification = $this->notifications->create([
                'organization_id' => $user->organization_id,
                'user_id' => $user->id,
                'type' => 'email',
                'channel' => 'email',
                'subject' => $subject,
                'message' => $body,
                'data' => $data,
                'status' => 'pending',
                'sent_at' => null,
            ]);

            // Queue email sending
            try {
                Mail::raw($body, function ($message) use ($user, $subject) {
                    $message->to($user->email)->subject($subject);
                });
                $notification->update([
                    'status' => 'sent',
                    'sent_at' => now(),
                ]);
                $this->logAction('Email notification sent', [
                    'notification_id' => $notification->id,
                    'user_id' => $user->id,
                ]);
            } catch (\Exception $e) {
                $notification->update([
                    'status' => 'failed',
                    'error_message' => $e->getMessage(),
                ]);
                $this->logError('Email notification failed', [
                    'notification_id' => $notification->id,
                    'error' => $e->getMessage(),
                ]);
            }

            return $notification;
        });
    }

    /**
     * Send SMS notification
     */
    public function sendSms(User $user, string $message, array $data = []): Notification
    {
        return DB::transaction(function () use ($user, $message, $data) {
            $notification = $this->notifications->create([
                'organization_id' => $user->organization_id,
                'user_id' => $user->id,
                'type' => 'sms',
                'channel' => 'sms',
'message' => $message,
                'data' => $data,
                'status' => 'pending',
                'sent_at' => null,
            ]);

            try {
                // TODO: Implement actual SMS sending
                $notification->update([
                    'status' => 'sent',
                    'sent_at' => now(),
                ]);
                $this->logAction('SMS notification sent', [
                    'notification_id' => $notification->id,
                    'user_id' => $user->id,
                ]);
            } catch (\Exception $e) {
                $notification->update([
                    'status' => 'failed',
                    'error_message' => $e->getMessage(),
                ]);
                $this->logError('SMS notification failed', [
                    'notification_id' => $notification->id,
                    'error' => $e->getMessage(),
                ]);
            }

            return $notification;
        });
    }

    /**
     * Send in-app notification
     */
    public function sendInApp(User $user, string $title, string $message, array $data = []): Notification
    {
        return DB::transaction(function () use ($user, $title, $message, $data) {
            return $this->notifications->create([
                'organization_id' => $user->organization_id,
                'user_id' => $user->id,
                'type' => 'in_app',
                'channel' => 'in_app',
                'subject' => $title,
'message' => $message,
                'data' => $data,
                'status' => 'sent',
                'sent_at' => now(),
            ]);
        });
    }

    /**
     * Get notifications for user
     */
    public function getNotifications(User $user, array $filters = []): Collection
    {
        $query = $this->notifications->query()
            ->where('organization_id', $user->organization_id)
            ->where('user_id', $user->id);

        if (isset($filters['type'])) {
            $query->where('type', $filters['type']);
        }

        if (isset($filters['read'])) {
            if ($filters['read']) {
                $query->whereNotNull('read_at');
            } else {
                $query->whereNull('read_at');
            }
        }

        return $query->orderByDesc('created_at')->get();
    }

    /**
     * Mark notification as read
     */
    public function markAsRead(Notification $notification): Notification
    {
        $notification->update(['read_at' => now()]);
        return $notification;
    }

    /**
     * Get notification template
     */
    public function getNotificationTemplate(string $key): ?NotificationTemplate
    {
        return NotificationTemplate::where('key', $key)
            ->where('organization_id', $this->getCurrentOrganizationId())
            ->first();
    }

    /**
     * Send notification from template
     */
    public function sendFromTemplate(User $user, string $templateKey, array $variables = []): Notification
    {
        $template = $this->getNotificationTemplate($templateKey);

        if (!$template) {
            throw new \Exception("Template '{$templateKey}' not found");
        }

        $subject = $this->replaceVariables($template->subject, $variables);
        $body = $this->replaceVariables($template->body, $variables);

        return match ($template->channel) {
            'email' => $this->sendEmail($user, $subject, $body, $variables),
            'sms' => $this->sendSms($user, $body, $variables),
            'in_app' => $this->sendInApp($user, $subject, $body, $variables),
            default => throw new \Exception("Unknown channel: {$template->channel}"),
        };
    }

    /**
     * Replace variables in template
     */
    private function replaceVariables(string $text, array $variables): string
    {
        foreach ($variables as $key => $value) {
            $text = str_replace("{{$key}}", $value, $text);
        }
        return $text;
    }
}
