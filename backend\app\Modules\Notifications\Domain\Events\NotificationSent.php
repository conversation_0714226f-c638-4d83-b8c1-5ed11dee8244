<?php

namespace App\Modules\Notifications\Domain\Events;

use App\Modules\Shared\Domain\Events\DomainEvent;

class NotificationSent extends DomainEvent
{
    public function __construct(public readonly string $notificationId, ?string $organizationId = null, ?string $userId = null, array $payload = [])
    {
        parent::__construct($organizationId, $userId, array_merge($payload, ['notification_id' => $notificationId]));
    }
}
