{"hash": "0a3889e5", "configHash": "507613dd", "lockfileHash": "c5dc960f", "browserHash": "f1b1d1db", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "b3acf702", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "aca725de", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "523ba3d2", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "07c3826b", "needsInterop": true}, "@coreui/icons": {"src": "../../@coreui/icons/dist/esm/index.js", "file": "@coreui_icons.js", "fileHash": "de13cb2f", "needsInterop": false}, "@coreui/icons-react": {"src": "../../@coreui/icons-react/dist/index.esm.js", "file": "@coreui_icons-react.js", "fileHash": "45690bed", "needsInterop": false}, "@coreui/react": {"src": "../../@coreui/react/dist/esm/index.js", "file": "@coreui_react.js", "fileHash": "9d8a18dc", "needsInterop": false}, "@coreui/react-chartjs": {"src": "../../@coreui/react-chartjs/dist/index.esm.js", "file": "@coreui_react-chartjs.js", "fileHash": "b7a34a6f", "needsInterop": false}, "@coreui/utils": {"src": "../../@coreui/utils/dist/esm/index.js", "file": "@coreui_utils.js", "fileHash": "83fe609f", "needsInterop": false}, "@mui/icons-material": {"src": "../../@mui/icons-material/esm/index.js", "file": "@mui_icons-material.js", "fileHash": "47bda246", "needsInterop": false}, "@mui/x-tree-view/SimpleTreeView": {"src": "../../@mui/x-tree-view/esm/SimpleTreeView/index.js", "file": "@mui_x-tree-view_SimpleTreeView.js", "fileHash": "84e37c52", "needsInterop": false}, "@mui/x-tree-view/TreeItem": {"src": "../../@mui/x-tree-view/esm/TreeItem/index.js", "file": "@mui_x-tree-view_TreeItem.js", "fileHash": "acee159d", "needsInterop": false}, "@reduxjs/toolkit": {"src": "../../@reduxjs/toolkit/dist/redux-toolkit.esm.js", "file": "@reduxjs_toolkit.js", "fileHash": "5a4a0f95", "needsInterop": false}, "classnames": {"src": "../../classnames/index.js", "file": "classnames.js", "fileHash": "246314d5", "needsInterop": true}, "core-js": {"src": "../../core-js/index.js", "file": "core-js.js", "fileHash": "02722658", "needsInterop": true}, "export-to-csv": {"src": "../../export-to-csv/output/index.js", "file": "export-to-csv.js", "fileHash": "47c6bdbd", "needsInterop": false}, "prop-types": {"src": "../../prop-types/index.js", "file": "prop-types.js", "fileHash": "d34dca09", "needsInterop": true}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "76d1a6b3", "needsInterop": true}, "react-redux": {"src": "../../react-redux/dist/react-redux.mjs", "file": "react-redux.js", "fileHash": "2864a827", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "14ce9b8a", "needsInterop": false}, "redux": {"src": "../../redux/dist/redux.mjs", "file": "redux.js", "fileHash": "92242cf6", "needsInterop": false}, "simplebar-react": {"src": "../../simplebar-react/dist/index.mjs", "file": "simplebar-react.js", "fileHash": "290646ab", "needsInterop": false}}, "chunks": {"chunk-OCZ3BSLF": {"file": "chunk-OCZ3BSLF.js"}, "chunk-F5X6INBZ": {"file": "chunk-F5X6INBZ.js"}, "chunk-ZWL2U2UW": {"file": "chunk-ZWL2U2UW.js"}, "chunk-ZWOIUA3G": {"file": "chunk-ZWOIUA3G.js"}, "chunk-FXVZLN7E": {"file": "chunk-FXVZLN7E.js"}, "chunk-ZLBVQ7QF": {"file": "chunk-ZLBVQ7QF.js"}, "chunk-KMU3Z7QX": {"file": "chunk-KMU3Z7QX.js"}, "chunk-G3PMV62Z": {"file": "chunk-G3PMV62Z.js"}}}