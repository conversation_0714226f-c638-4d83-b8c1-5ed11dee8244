<?php

namespace App\Modules\Users\Domain\Services;

use App\Modules\Shared\Domain\Services\BaseService;
use App\Modules\Users\Domain\Models\User;
use App\Modules\Users\Domain\Repositories\UserRepository;
use Illuminate\Database\Eloquent\Collection;

class UserService extends BaseService
{
    public function __construct(private readonly UserRepository $users)
    {
    }

    public function createUser(array $data): User
    {
        return $this->users->create($data);
    }

    public function updateUser(User $user, array $data): User
    {
        return $this->users->update($user, $data);
    }

    public function deleteUser(User $user): bool
    {
        return $this->users->delete($user);
    }

    public function getUserById(string $id): ?User
    {
        return $this->users->findById($id);
    }

    public function getUserByEmail(string $email): ?User
    {
        return User::query()->where('email', $email)->first();
    }

    public function listUsers(array $filters = []): Collection
    {
        return $this->users->all($filters);
    }
}
