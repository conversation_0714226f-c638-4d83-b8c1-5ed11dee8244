<?php

namespace Tests\Feature\Integration;

use Tests\TestCase;
use App\Modules\Users\Domain\Models\User;
use App\Modules\Organizations\Domain\Models\Organization;
use App\Modules\Integration\Domain\Models\Webhook;
use Illuminate\Foundation\Testing\RefreshDatabase;

class WebhookActionsTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Organization $org;

    protected function setUp(): void
    {
        parent::setUp();
        $this->org = Organization::factory()->create();
        $this->user = User::factory()->create(['organization_id' => $this->org->id]);
    }

    public function test_can_update_webhook()
    {
        $webhook = Webhook::factory()->create(['organization_id' => $this->org->id]);
        $response = $this->actingAs($this->user)->patchJson("/api/v1/webhooks/{$webhook->id}", [
            'url' => 'https://example.com/webhook-updated',
            'is_active' => false,
        ]);
        $response->assertStatus(200);
        $webhook->refresh();
        $this->assertEquals('https://example.com/webhook-updated', $webhook->url);
    }

    public function test_can_get_webhook_events()
    {
        $webhook = Webhook::factory()->create(['organization_id' => $this->org->id]);
        $response = $this->actingAs($this->user)->getJson("/api/v1/webhooks/{$webhook->id}/events");
        $response->assertStatus(200);
    }

    public function test_can_filter_webhook_events()
    {
        $webhook = Webhook::factory()->create(['organization_id' => $this->org->id]);
        $response = $this->actingAs($this->user)->getJson("/api/v1/webhooks/{$webhook->id}/events?status=delivered");
        $response->assertStatus(200);
    }

    public function test_can_list_webhooks()
    {
        Webhook::factory()->count(3)->create(['organization_id' => $this->org->id]);
        $response = $this->actingAs($this->user)->getJson('/api/v1/webhooks');
        $response->assertStatus(200);
    }

    public function test_can_show_webhook()
    {
        $webhook = Webhook::factory()->create(['organization_id' => $this->org->id]);
        $response = $this->actingAs($this->user)->getJson("/api/v1/webhooks/{$webhook->id}");
        $response->assertStatus(200);
    }

    public function test_can_delete_webhook()
    {
        $webhook = Webhook::factory()->create(['organization_id' => $this->org->id]);
        $response = $this->actingAs($this->user)->deleteJson("/api/v1/webhooks/{$webhook->id}");
        $response->assertStatus(200);
        $this->assertNull(Webhook::find($webhook->id));
    }

    public function test_cannot_access_other_org_webhook()
    {
        $other_org = Organization::factory()->create();
        $webhook = Webhook::factory()->create(['organization_id' => $other_org->id]);
        $response = $this->actingAs($this->user)->getJson("/api/v1/webhooks/{$webhook->id}");
        $response->assertStatus(404);
    }
}
