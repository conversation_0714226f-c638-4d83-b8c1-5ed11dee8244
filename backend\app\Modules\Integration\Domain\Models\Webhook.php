<?php

namespace App\Modules\Integration\Domain\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Modules\Shared\Domain\Traits\HasUUID;
use App\Modules\Shared\Domain\Traits\HasOrganizationId;
use App\Modules\Organizations\Domain\Models\Organization;

class Webhook extends Model
{
    use HasFactory, SoftDeletes, HasUUID, HasOrganizationId;

    protected $table = 'webhooks';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'organization_id',
        'name',
        'url',
        'events',
        'secret',
        'is_active',
        'retry_count',
        'metadata',
    ];

    protected $casts = [
        'events' => 'array',
        'metadata' => 'array',
        'is_active' => 'boolean',
        'retry_count' => 'integer',
    ];

    // Relationships
    public function organization()
    {
        return $this->belongsTo(Organization::class, 'organization_id');
    }

    public function events()
    {
        return $this->hasMany(WebhookEvent::class, 'webhook_id');
    }
}
