<?php

namespace App\Modules\RolesPermissions\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateRoleRequest extends FormRequest
{
    public function authorize(): bool { return true; }
    public function rules(): array
    {
        return [
            'name' => ['sometimes','string','max:100'],
            'slug' => ['sometimes','string','max:100'],
            'description' => ['sometimes','nullable','string','max:500'],
            'level' => ['sometimes','integer','min:0'],
        ];
    }
}
