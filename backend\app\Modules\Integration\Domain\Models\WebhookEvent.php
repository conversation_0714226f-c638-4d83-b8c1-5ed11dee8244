<?php

namespace App\Modules\Integration\Domain\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Modules\Shared\Domain\Traits\HasUUID;
use App\Modules\Shared\Domain\Traits\HasOrganizationId;
use App\Modules\Organizations\Domain\Models\Organization;

class WebhookEvent extends Model
{
    use HasFactory, HasUUID, HasOrganizationId;

    protected $table = 'webhook_events';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'organization_id',
        'webhook_id',
        'event_type',
        'payload',
        'status',
        'attempts',
        'response_code',
        'response_body',
        'error_message',
        'delivered_at',
    ];

    protected $casts = [
        'payload' => 'array',
        'response_body' => 'array',
        'delivered_at' => 'datetime',
        'attempts' => 'integer',
    ];

    // Relationships
    public function organization()
    {
        return $this->belongsTo(Organization::class, 'organization_id');
    }

    public function webhook()
    {
        return $this->belongsTo(Webhook::class, 'webhook_id');
    }
}
