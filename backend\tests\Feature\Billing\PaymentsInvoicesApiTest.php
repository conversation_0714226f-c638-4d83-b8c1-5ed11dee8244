<?php

namespace Tests\Feature\Billing;

use Tests\TestCase;
use Tests\Support\CreatesOrgUser;
use Illuminate\Foundation\Testing\RefreshDatabase;

class PaymentsInvoicesApiTest extends TestCase
{
    use RefreshDatabase, CreatesOrgUser;

    public function test_payments_and_invoices_endpoints(): void
    {
        $org = $this->createOrganization();
        $user = $this->createUserInOrg($org);
        $this->actingAs($user);
        $this->grantPermission($user, 'payments.create');
        $this->grantPermission($user, 'payments.view');
        $this->grantPermission($user, 'invoices.create');
        $this->grantPermission($user, 'invoices.send');

        $plan = $this->createPlan($org, ['name' => 'Basic', 'slug' => 'basic', 'price' => 10.00]);
        $sub = \App\Modules\Billing\Domain\Models\Subscription::create([
            'organization_id' => $org->id,
            'plan_id' => $plan->id,
            'status' => 'active',
            'starts_at' => now(),
            'price' => 10.00,
            'billing_period' => 'monthly',
        ]);

        // Payment create
        $this->postJson('/api/v1/payments', [
            'organization_id' => $org->id,
            'subscription_id' => $sub->id,
            'amount' => 10,
            'payment_method_id' => \App\Modules\Billing\Domain\Models\PaymentMethod::create([
                'organization_id' => $org->id,
                'type' => 'card',
                'details' => ['last4' => '4242']
            ])->id,
        ])->assertStatus(201);

        // Invoices
        $invoiceId = $this->postJson('/api/v1/invoices', [
            'organization_id' => $org->id,
            'subscription_id' => $sub->id,
            'items' => [
                ['description' => 'Service', 'amount' => 10],
            ],
        ])->assertStatus(201)->json('id');

        $this->getJson('/api/v1/invoices/'.$invoiceId)->assertStatus(200);
        $this->getJson('/api/v1/invoices/'.$invoiceId.'/pdf')->assertStatus(200);
        $this->postJson('/api/v1/invoices/'.$invoiceId.'/send')->assertStatus(200);

        // Payments index
        $this->getJson('/api/v1/payments')->assertStatus(200);
    }
}
