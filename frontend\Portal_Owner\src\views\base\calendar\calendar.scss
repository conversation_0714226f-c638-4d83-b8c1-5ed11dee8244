/* Calendar styling to resemble CoreUI PRO calendar */
.cui-calendar.react-calendar {
  border: 1px solid var(--cui-border-color, rgba(0,0,0,.125));
  border-radius: var(--cui-border-radius, .375rem);
  background-color: var(--cui-body-bg, #fff);
  color: var(--cui-body-color, #1b1b1b);
  font-family: inherit;

  .react-calendar__navigation {
    display: flex;
    gap: .25rem;
    margin-bottom: .5rem;

    button {
      border: 1px solid var(--cui-border-color, rgba(0,0,0,.125));
      background-color: var(--cui-tertiary-bg, #f8f9fa);
      color: inherit;
      padding: .375rem .5rem;
      border-radius: .375rem;

      &:hover { background-color: var(--cui-secondary-bg, #eef2f6); }
    }
  }

  .react-calendar__month-view__weekdays {
    abbr { text-decoration: none; color: var(--cui-secondary-color, #6c757d); font-weight: 500; }
  }

  .react-calendar__tile {
    border-radius: .375rem;
    padding: .5rem .25rem;

    &:enabled:hover { background-color: var(--cui-secondary-bg, #eef2f6); }
  }

  .react-calendar__tile--now {
    background: var(--cui-warning-bg-subtle, #fff3cd);
  }

  .react-calendar__tile--active,
  .react-calendar__tile--rangeStart,
  .react-calendar__tile--rangeEnd,
  .react-calendar__tile--rangeBothEnds {
    background: var(--cui-primary, #0d6efd);
    color: #fff;
  }

  .react-calendar__tile--range { background: var(--cui-primary-bg-subtle, #cfe2ff); }

  .react-calendar__weekNumbers .react-calendar__tile { color: var(--cui-secondary-color, #6c757d); }
}

/* Right-to-left support for Arabic/Hebrew locales */
.rtl { direction: rtl; }