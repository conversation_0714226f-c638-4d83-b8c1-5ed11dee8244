<?php

namespace Database\Factories\Modules\Billing\Domain\Models;

use App\Modules\Billing\Domain\Models\Invoice;
use App\Modules\Organizations\Domain\Models\Organization;
use Illuminate\Database\Eloquent\Factories\Factory;

class InvoiceFactory extends Factory
{
    protected $model = Invoice::class;

    public function definition(): array
    {
        return [
            'id' => $this->faker->uuid(),
            'organization_id' => Organization::factory(),
            'subscription_id' => null,
            'invoice_number' => 'INV-' . $this->faker->unique()->numerify('######'),
            'status' => 'sent',
            'subtotal' => 100.00,
            'tax' => 10.00,
            'discount' => 0.00,
            'total' => 110.00,
            'currency' => 'USD',
            'issue_date' => now()->toDateString(),
            'due_date' => now()->addDays(7)->toDateString(),
            'sent_at' => now(),
            'paid_at' => null,
            'line_items' => [],
            'notes' => null,
            'metadata' => [],
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}
