# Module Structure

## Table of Contents
1. [Overview](#overview)
2. [Module Architecture Pattern](#module-architecture-pattern)
3. [Organizations Module](#1-organizations-module)
4. [Billing Module](#2-billing-module)
5. [Approvals Module](#3-approvals-module)
6. [Users Module](#4-users-module)
7. [Notifications Module](#5-notifications-module)
8. [Audit Module](#6-audit-module)
9. [Reports Module](#7-reports-module)
10. [Settings Module](#8-settings-module)
11. [Integration Module](#9-integration-module)
12. [Future Modules](#future-modules)
13. [Module Communication](#module-communication)
14. [Module Dependencies](#module-dependencies)

---

## Overview

The ERP system is architected as a **Modular Monolith**, consisting of 13 distinct but integrated modules. Each module encapsulates specific business capabilities while maintaining clear boundaries and interfaces for inter-module communication.

### Design Principles

1. **High Cohesion**: Related functionality grouped together
2. **Loose Coupling**: Minimal dependencies between modules
3. **Clear Interfaces**: Well-defined public APIs
4. **Dependency Direction**: Dependencies flow inward (Domain-driven)
5. **Single Responsibility**: Each module owns one business domain

### Module States

- ✅ **Fully Implemented**: Production-ready with complete tests
- 🚧 **In Progress**: Partially implemented
- 📋 **Planned**: Documented but not yet implemented

---

## Module Architecture Pattern

Each module follows a consistent layered architecture:

```
Module/
├── Domain/                    # Business logic layer
│   ├── Models/               # Eloquent models
│   ├── Services/             # Business services
│   ├── Exceptions/           # Domain-specific exceptions
│   └── Events/               # Domain events
├── Application/              # Application layer
│   ├── Controllers/          # HTTP controllers
│   ├── Requests/             # Form requests
│   ├── Resources/            # API resources
│   └── DTOs/                 # Data transfer objects
├── Infrastructure/           # Infrastructure layer
│   ├── Repositories/         # Data access
│   ├── Jobs/                 # Background jobs
│   └── Notifications/        # Notification channels
└── Tests/                    # Module tests
    ├── Unit/
    └── Feature/
```

---

## 1. Organizations Module

### Status: 🚧 In Progress

### Purpose
Multi-tenant organization hierarchy management with support for unlimited depth organizational trees.

### Key Responsibilities
- **Organization CRUD**: Create, read, update, delete organizations
- **Hierarchy Management**: Build and maintain parent-child relationships
- **Depth Validation**: Enforce hierarchy depth limits based on subscription
- **Tree Operations**: Navigate, query, and manipulate organization trees
- **Type Management**: Handle portal_owner, tenant, and sub_organization types
- **Status Management**: Active, suspended, inactive states

### Domain Models

#### Organization Model
```php
namespace App\Modules\Organizations\Domain\Models;

class Organization extends Model
{
    protected $fillable = [
        'parent_id',
        'type',
        'name',
        'slug',
        'status',
        'metadata'
    ];

    protected $casts = [
        'metadata' => 'array',
    ];

    // Relationships
    public function parent(): BelongsTo
    {
        return $this->belongsTo(Organization::class, 'parent_id');
    }

    public function children(): HasMany
    {
        return $this->hasMany(Organization::class, 'parent_id');
    }

    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }

    public function subscription(): HasOne
    {
        return $this->hasOne(Subscription::class);
    }

    // Scopes
    public function scopeTenants($query)
    {
        return $query->where('type', 'tenant');
    }

    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    // Accessors
    public function getHierarchyDepthAttribute(): int
    {
        $depth = 0;
        $current = $this;
        while ($current->parent) {
            $depth++;
            $current = $current->parent;
        }
        return $depth;
    }
}
```

### Services

#### OrganizationService
```php
namespace App\Modules\Organizations\Domain\Services;

class OrganizationService
{
    public function createOrganization(array $data): Organization
    {
        // Validate parent organization exists
        if (isset($data['parent_id'])) {
            $parent = Organization::findOrFail($data['parent_id']);
            
            // Check hierarchy depth limit
            $this->validateHierarchyDepth($parent);
            
            // Check sub-org limit
            $this->checkSubOrgLimit($parent);
        }

        $organization = Organization::create($data);
        
        // Increment counter if it's a sub-organization
        if ($organization->parent_id) {
            $this->incrementSubOrgCounter($organization->parent);
        }

        event(new OrganizationCreated($organization));
        
        return $organization;
    }

    public function getHierarchy(Organization $organization): array
    {
        return [
            'id' => $organization->id,
            'name' => $organization->name,
            'type' => $organization->type,
            'status' => $organization->status,
            'depth' => $organization->hierarchy_depth,
            'children' => $organization->children->map(function ($child) {
                return $this->getHierarchy($child);
            })->toArray()
        ];
    }

    public function getAncestors(Organization $organization): Collection
    {
        $ancestors = collect();
        $current = $organization->parent;
        
        while ($current) {
            $ancestors->push($current);
            $current = $current->parent;
        }
        
        return $ancestors;
    }

    public function getDescendants(Organization $organization): Collection
    {
        $descendants = collect();
        
        foreach ($organization->children as $child) {
            $descendants->push($child);
            $descendants = $descendants->merge($this->getDescendants($child));
        }
        
        return $descendants;
    }

    private function validateHierarchyDepth(Organization $parent): void
    {
        $subscription = $this->getTenantSubscription($parent);
        $currentDepth = $parent->hierarchy_depth;
        $maxDepth = $subscription->plan->hierarchy_depth_limit;

        if ($maxDepth > 0 && $currentDepth >= $maxDepth) {
            throw new HierarchyDepthExceededException(
                "Maximum hierarchy depth of {$maxDepth} reached"
            );
        }
    }

    private function getTenantSubscription(Organization $org): Subscription
    {
        // Traverse up to find tenant organization
        while ($org->type !== 'tenant' && $org->parent) {
            $org = $org->parent;
        }
        
        return $org->subscription;
    }
}
```

### API Endpoints

```
GET    /api/v1/organizations
POST   /api/v1/organizations
GET    /api/v1/organizations/{id}
PATCH  /api/v1/organizations/{id}
DELETE /api/v1/organizations/{id}
GET    /api/v1/organizations/{id}/hierarchy
GET    /api/v1/organizations/{id}/ancestors
GET    /api/v1/organizations/{id}/descendants
POST   /api/v1/organizations/{id}/suspend
POST   /api/v1/organizations/{id}/activate
```

### Example Request/Response

**Create Sub-Organization:**
```http
POST /api/v1/organizations
Content-Type: application/json
Authorization: Bearer {token}

{
  "parent_id": "tenant-org-uuid",
  "type": "sub_organization",
  "name": "Sales Department",
  "slug": "sales-dept",
  "metadata": {
    "location": "Mumbai",
    "head_name": "John Doe"
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "sub-org-uuid",
    "parent_id": "tenant-org-uuid",
    "type": "sub_organization",
    "name": "Sales Department",
    "slug": "sales-dept",
    "status": "active",
    "hierarchy_depth": 1,
    "metadata": {
      "location": "Mumbai",
      "head_name": "John Doe"
    },
    "created_at": "2025-10-30T06:00:00Z"
  }
}
```

### Dependencies
- **Billing Module**: Check subscription limits
- **Users Module**: Assign users to organizations
- **Approvals Module**: Sub-org creation approval (if required)

### Database Tables
- `organizations`

### Tests
- OrganizationServiceTest (planned)
- OrganizationAPITest (planned)

---

## 2. Billing Module

### Status: ✅ Fully Implemented

### Purpose
Comprehensive subscription and resource billing management with usage tracking, add-ons, and proration calculations.

### Key Responsibilities
- **Plan Management**: Define and manage subscription plans
- **Subscription Lifecycle**: Create, upgrade, downgrade, cancel subscriptions
- **Add-on Marketplace**: Purchase and manage additional resources
- **Resource Tracking**: Monitor usage across all resource types
- **Usage Alerts**: Notify when approaching or exceeding limits
- **Proration**: Calculate mid-period charges and credits
- **Billing History**: Maintain complete billing records

### Domain Models

#### Plan Model
```php
namespace App\Modules\Billing\Domain\Models;

class Plan extends Model
{
    protected $fillable = [
        'organization_id',
        'name',
        'slug',
        'description',
        'price',
        'yearly_price',
        'billing_period',
        'user_limit',
        'storage_limit',
        'sub_org_limit',
        'hierarchy_depth_limit',
        'api_calls_limit',
        'modules',
        'is_active',
        'trial_days',
        'sort_order'
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'yearly_price' => 'decimal:2',
        'modules' => 'array',
        'is_active' => 'boolean',
    ];

    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class);
    }

    public function hasModule(string $module): bool
    {
        return in_array($module, $this->modules ?? []);
    }

    public function isUnlimitedUsers(): bool
    {
        return $this->user_limit === 0;
    }
}
```

#### Subscription Model
```php
namespace App\Modules\Billing\Domain\Models;

class Subscription extends Model
{
    protected $fillable = [
        'organization_id',
        'plan_id',
        'status',
        'starts_at',
        'ends_at',
        'trial_ends_at',
        'cancelled_at',
        'auto_renew',
        'price',
        'billing_period',
        'user_count',
        'sub_org_count',
        'storage_used',
        'hierarchy_depth',
        'metadata'
    ];

    protected $casts = [
        'starts_at' => 'datetime',
        'ends_at' => 'datetime',
        'trial_ends_at' => 'datetime',
        'cancelled_at' => 'datetime',
        'auto_renew' => 'boolean',
        'price' => 'decimal:2',
        'metadata' => 'array',
    ];

    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    public function plan(): BelongsTo
    {
        return $this->belongsTo(Plan::class);
    }

    public function addons(): BelongsToMany
    {
        return $this->belongsToMany(Addon::class, 'subscription_addon')
            ->withPivot('quantity', 'price', 'attached_at')
            ->withTimestamps();
    }

    public function resourceUsageLogs(): HasMany
    {
        return $this->hasMany(ResourceUsageLog::class);
    }

    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    public function isOnTrial(): bool
    {
        return $this->trial_ends_at && 
               now()->lessThan($this->trial_ends_at);
    }

    public function getTotalUserLimit(): int
    {
        $planLimit = $this->plan->user_limit;
        $addonLimit = $this->addons()
            ->where('type', 'user')
            ->sum('subscription_addon.quantity');
        
        return $planLimit + $addonLimit;
    }

    public function getTotalStorageLimit(): int
    {
        $planLimit = $this->plan->storage_limit;
        $addonLimit = $this->addons()
            ->where('type', 'storage')
            ->sum('subscription_addon.quantity');
        
        return $planLimit + $addonLimit;
    }
}
```

#### Addon Model
```php
namespace App\Modules\Billing\Domain\Models;

class Addon extends Model
{
    protected $fillable = [
        'organization_id',
        'name',
        'slug',
        'description',
        'type',
        'price',
        'value',
        'unit',
        'is_active',
        'metadata'
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'is_active' => 'boolean',
        'metadata' => 'array',
    ];

    public function subscriptions(): BelongsToMany
    {
        return $this->belongsToMany(Subscription::class, 'subscription_addon')
            ->withPivot('quantity', 'price', 'attached_at')
            ->withTimestamps();
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }
}
```

#### ResourceUsageLog Model
```php
namespace App\Modules\Billing\Domain\Models;

class ResourceUsageLog extends Model
{
    protected $fillable = [
        'subscription_id',
        'tenant_organization_id',
        'resource_type',
        'usage_value',
        'limit_value',
        'usage_percentage',
        'alert_level',
        'alert_sent',
        'recorded_at'
    ];

    protected $casts = [
        'usage_percentage' => 'decimal:2',
        'alert_sent' => 'boolean',
        'recorded_at' => 'datetime',
    ];

    public function subscription(): BelongsTo
    {
        return $this->belongsTo(Subscription::class);
    }

    public function tenantOrganization(): BelongsTo
    {
        return $this->belongsTo(Organization::class, 'tenant_organization_id');
    }

    public function scopeByResourceType($query, string $type)
    {
        return $query->where('resource_type', $type);
    }

    public function scopeCritical($query)
    {
        return $query->whereIn('alert_level', ['critical', 'exceeded']);
    }
}
```

### Services

#### BillingService (18 tests ✅)
```php
namespace App\Modules\Billing\Domain\Services;

class BillingService
{
    public function purchaseAddOn(
        Subscription $subscription,
        Addon $addon,
        int $quantity = 1
    ): array {
        // Validate addon is active
        if (!$addon->is_active) {
            throw new InactiveAddonException();
        }

        // Calculate prorated price
        $proratedPrice = $this->calculateProration(
            $addon->price * $quantity,
            $subscription->ends_at
        );

        // Attach addon to subscription
        $subscription->addons()->attach($addon->id, [
            'quantity' => $quantity,
            'price' => $proratedPrice,
            'attached_at' => now()
        ]);

        return [
            'addon' => $addon,
            'quantity' => $quantity,
            'prorated_price' => $proratedPrice,
            'attached_at' => now()
        ];
    }

    public function removeAddOn(Subscription $subscription, string $addonId): bool
    {
        return $subscription->addons()->detach($addonId) > 0;
    }

    public function calculateProration(float $monthlyPrice, Carbon $periodEnd): float
    {
        $today = now()->startOfDay();
        $periodEnd = $periodEnd->startOfDay();
        
        if ($today->greaterThanOrEqualTo($periodEnd)) {
            return 0;
        }

        $daysInMonth = $today->daysInMonth;
        $daysRemaining = $today->diffInDays($periodEnd);
        
        $dailyRate = $monthlyPrice / $daysInMonth;
        
        return round($dailyRate * $daysRemaining, 2);
    }

    public function calculateMonthlyTotal(Subscription $subscription): float
    {
        $planPrice = $subscription->price;
        
        $addonsPrice = $subscription->addons->sum(function ($addon) {
            return $addon->pivot->price;
        });

        return round($planPrice + $addonsPrice, 2);
    }

    public function upgradeSubscription(
        Subscription $subscription,
        Plan $newPlan
    ): array {
        $oldPlan = $subscription->plan;
        
        // Calculate unused credit from old plan
        $unusedCredit = $this->calculateProration(
            $subscription->price,
            $subscription->ends_at
        );

        // Calculate charge for new plan
        $newPlanCharge = $this->calculateProration(
            $newPlan->price,
            $subscription->ends_at
        );

        $netAmount = $newPlanCharge - $unusedCredit;

        // Update subscription
        $subscription->update([
            'plan_id' => $newPlan->id,
            'price' => $newPlan->price,
        ]);

        return [
            'old_plan' => $oldPlan,
            'new_plan' => $newPlan,
            'unused_credit' => $unusedCredit,
            'new_plan_charge' => $newPlanCharge,
            'net_amount' => $netAmount,
        ];
    }

    public function downgradeSubscription(
        Subscription $subscription,
        Plan $newPlan
    ): void {
        // Check if current usage exceeds new plan limits
        $limitCheck = app(ResourceLimitService::class)
            ->checkAllLimits($subscription, $newPlan);

        if (!$limitCheck['within_limits']) {
            throw new UsageExceedsNewPlanException(
                "Current usage exceeds new plan limits",
                $limitCheck['violations']
            );
        }

        // Schedule downgrade for next period
        $subscription->update([
            'metadata->scheduled_plan_id' => $newPlan->id,
            'metadata->scheduled_at' => now(),
        ]);
    }

    public function cancelSubscription(
        Subscription $subscription,
        bool $immediate = false
    ): void {
        if ($immediate) {
            $subscription->update([
                'status' => 'cancelled',
                'cancelled_at' => now(),
                'ends_at' => now(),
            ]);
        } else {
            $subscription->update([
                'cancelled_at' => now(),
                'auto_renew' => false,
            ]);
        }
    }
}
```

#### UsageTrackingService (19 tests ✅)
```php
namespace App\Modules\Billing\Domain\Services;

class UsageTrackingService
{
    public function logResourceUsage(Subscription $subscription): array
    {
        $logs = [];
        $organization = $subscription->organization;

        // Log user count
        $userCount = $this->countUsers($organization);
        $logs[] = $this->createUsageLog(
            $subscription,
            'users',
            $userCount,
            $subscription->getTotalUserLimit()
        );

        // Log sub-org count
        $subOrgCount = $this->countSubOrganizations($organization);
        $logs[] = $this->createUsageLog(
            $subscription,
            'sub_orgs',
            $subOrgCount,
            $subscription->plan->sub_org_limit
        );

        // Log storage usage
        $storageUsed = $subscription->storage_used;
        $logs[] = $this->createUsageLog(
            $subscription,
            'storage',
            $storageUsed,
            $subscription->getTotalStorageLimit() * 1024 * 1024 * 1024 // Convert GB to bytes
        );

        // Log hierarchy depth
        $maxDepth = $this->calculateMaxHierarchyDepth($organization);
        $logs[] = $this->createUsageLog(
            $subscription,
            'hierarchy_depth',
            $maxDepth,
            $subscription->plan->hierarchy_depth_limit
        );

        return $logs;
    }

    private function createUsageLog(
        Subscription $subscription,
        string $resourceType,
        int $usageValue,
        int $limitValue
    ): ResourceUsageLog {
        $usagePercentage = $limitValue > 0 
            ? ($usageValue / $limitValue) * 100 
            : 0;

        $alertLevel = $this->determineAlertLevel($usagePercentage);

        $log = ResourceUsageLog::create([
            'subscription_id' => $subscription->id,
            'tenant_organization_id' => $subscription->organization_id,
            'resource_type' => $resourceType,
            'usage_value' => $usageValue,
            'limit_value' => $limitValue,
            'usage_percentage' => $usagePercentage,
            'alert_level' => $alertLevel,
            'alert_sent' => false,
            'recorded_at' => now(),
        ]);

        // Send alert if needed
        if (in_array($alertLevel, ['warning', 'critical', 'exceeded'])) {
            $this->sendUsageAlert($log);
            $log->update(['alert_sent' => true]);
        }

        return $log;
    }

    private function determineAlertLevel(float $percentage): string
    {
        if ($percentage >= 100) return 'exceeded';
        if ($percentage >= 90) return 'critical';
        if ($percentage >= 75) return 'warning';
        return 'normal';
    }

    public function getCurrentUsage(Subscription $subscription): array
    {
        return [
            'users' => [
                'current' => $subscription->user_count,
                'limit' => $subscription->getTotalUserLimit(),
                'percentage' => $this->calculatePercentage(
                    $subscription->user_count,
                    $subscription->getTotalUserLimit()
                ),
            ],
            'sub_orgs' => [
                'current' => $subscription->sub_org_count,
                'limit' => $subscription->plan->sub_org_limit,
                'percentage' => $this->calculatePercentage(
                    $subscription->sub_org_count,
                    $subscription->plan->sub_org_limit
                ),
            ],
            'storage' => [
                'current' => $subscription->storage_used,
                'limit' => $subscription->getTotalStorageLimit() * 1024 * 1024 * 1024,
                'percentage' => $this->calculatePercentage(
                    $subscription->storage_used,
                    $subscription->getTotalStorageLimit() * 1024 * 1024 * 1024
                ),
            ],
        ];
    }

    public function getUsageHistory(
        Subscription $subscription,
        string $resourceType,
        int $days = 30
    ): Collection {
        return ResourceUsageLog::where('subscription_id', $subscription->id)
            ->where('resource_type', $resourceType)
            ->where('recorded_at', '>=', now()->subDays($days))
            ->orderBy('recorded_at', 'desc')
            ->get();
    }

    public function cleanupOldLogs(int $daysToKeep = 90): int
    {
        $cutoffDate = now()->subDays($daysToKeep);
        
        return ResourceUsageLog::where('recorded_at', '<', $cutoffDate)
            ->delete();
    }
}
```

#### ResourceLimitService (28 tests ✅)
```php
namespace App\Modules\Billing\Domain\Services;

class ResourceLimitService
{
    public function checkUserLimit(Subscription $subscription): array
    {
        $current = $subscription->user_count;
        $limit = $subscription->getTotalUserLimit();
        $withinLimit = $limit === 0 || $current < $limit;

        return [
            'within_limit' => $withinLimit,
            'resource_type' => 'users',
            'current' => $current,
            'limit' => $limit,
            'percentage' => $this->calculatePercentage($current, $limit),
            'plan_name' => $subscription->plan->name,
            'suggestions' => $this->getSuggestions('users', $limit),
        ];
    }

    public function checkSubOrgLimit(Subscription $subscription): array
    {
        $current = $subscription->sub_org_count;
        $limit = $subscription->plan->sub_org_limit;
        $withinLimit = $limit === 0 || $current < $limit;

        return [
            'within_limit' => $withinLimit,
            'resource_type' => 'sub_orgs',
            'current' => $current,
            'limit' => $limit,
            'percentage' => $this->calculatePercentage($current, $limit),
            'suggestions' => $this->getSuggestions('sub_orgs', $limit),
        ];
    }

    public function checkStorageLimit(
        Subscription $subscription,
        int $additionalBytes = 0
    ): array {
        $current = $subscription->storage_used;
        $limit = $subscription->getTotalStorageLimit() * 1024 * 1024 * 1024;
        $newTotal = $current + $additionalBytes;
        $withinLimit = $limit === 0 || $newTotal <= $limit;

        return [
            'within_limit' => $withinLimit,
            'resource_type' => 'storage',
            'current' => $current,
            'additional' => $additionalBytes,
            'new_total' => $newTotal,
            'limit' => $limit,
            'percentage' => $this->calculatePercentage($newTotal, $limit),
        ];
    }

    public function incrementUserCounter(Subscription $subscription): void
    {
        $subscription->lockForUpdate();
        $subscription->increment('user_count');
        $subscription->save();
    }

    public function decrementUserCounter(Subscription $subscription): void
    {
        $subscription->lockForUpdate();
        $subscription->decrement('user_count');
        $subscription->save();
    }

    public function incrementSubOrgCounter(Subscription $subscription): void
    {
        $subscription->lockForUpdate();
        $subscription->increment('sub_org_count');
        $subscription->save();
    }

    private function calculatePercentage(int $current, int $limit): float
    {
        if ($limit === 0) return 0;
        return round(($current / $limit) * 100, 2);
    }

    private function getSuggestions(string $resourceType, int $currentLimit): array
    {
        $suggestions = [];
        
        if ($resourceType === 'users') {
            $suggestions[] = "Upgrade to a higher plan for more users";
            $suggestions[] = "Purchase 'Additional Users' add-on";
        } elseif ($resourceType === 'sub_orgs') {
            $suggestions[] = "Upgrade your plan for more sub-organizations";
            $suggestions[] = "Purchase 'Additional Sub-Organizations' add-on";
        } elseif ($resourceType === 'storage') {
            $suggestions[] = "Purchase 'Additional Storage' add-on";
            $suggestions[] = "Delete unused files to free up space";
        }
        
        return $suggestions;
    }
}
```

### Scheduled Jobs

#### LogResourceUsageJob (10 tests ✅)
```php
namespace App\Modules\Billing\Infrastructure\Jobs;

class LogResourceUsageJob implements ShouldQueue
{
    public function handle(UsageTrackingService $usageService): void
    {
        $subscriptions = Subscription::active()->get();

        foreach ($subscriptions as $subscription) {
            try {
                $usageService->logResourceUsage($subscription);
            } catch (\Exception $e) {
                Log::error("Failed to log usage for subscription {$subscription->id}", [
                    'error' => $e->getMessage()
                ]);
            }
        }
    }
}

// Scheduled in routes/console.php
Schedule::job(new LogResourceUsageJob())
    ->dailyAt('00:00')
    ->withoutOverlapping()
    ->onOneServer();
```

#### CleanupOldUsageLogsJob
```php
namespace App\Modules\Billing\Infrastructure\Jobs;

class CleanupOldUsageLogsJob implements ShouldQueue
{
    private int $daysToKeep;

    public function __construct(int $daysToKeep = 90)
    {
        $this->daysToKeep = $daysToKeep;
    }

    public function handle(UsageTrackingService $usageService): void
    {
        $deleted = $usageService->cleanupOldLogs($this->daysToKeep);
        
        Log::info("Cleaned up {$deleted} old usage logs");
    }
}

// Scheduled in routes/console.php
Schedule::job(new CleanupOldUsageLogsJob(90))
    ->weekly()
    ->sundays()
    ->at('02:00')
    ->withoutOverlapping()
    ->onOneServer();
```

### API Endpoints (8 endpoints, 13 tests ✅)

```
POST   /api/v1/subscriptions/{id}/addons
DELETE /api/v1/subscriptions/{id}/addons/{addonId}
POST   /api/v1/subscriptions/{id}/upgrade-plan
POST   /api/v1/subscriptions/{id}/downgrade-plan
POST   /api/v1/subscriptions/{id}/cancel-subscription
POST   /api/v1/subscriptions/{id}/calculate-upgrade-price
GET    /api/v1/organizations/{id}/usage
GET    /api/v1/organizations/{id}/usage/alerts
GET    /api/v1/subscriptions/{id}/usage/history
```

### Example Request/Response

**Purchase Add-on:**
```http
POST /api/v1/subscriptions/{id}/addons
Content-Type: application/json
Authorization: Bearer {token}

{
  "addon_id": "addon-uuid",
  "quantity": 2
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "addon": {
      "id": "addon-uuid",
      "name": "Additional Users",
      "type": "user",
      "price": 50.00,
      "value": 5
    },
    "quantity": 2,
    "prorated_price": 83.33,
    "attached_at": "2025-10-30T06:00:00Z",
    "total_price": 166.66
  },
  "message": "Add-on purchased successfully"
}
```

### Dependencies
- **Organizations Module**: Link subscriptions to organizations
- **Approvals Module**: Trigger approvals for limit exceptions
- **Notifications Module**: Send usage alerts
- **Audit Module**: Log billing actions

### Database Tables
- `plans`
- `subscriptions`
- `addons`
- `subscription_addon` (pivot)
- `resource_usage_logs`

### Tests
- BillingServiceTest: 18 tests ✅
- UsageTrackingServiceTest: 19 tests ✅
- ResourceLimitServiceTest: 28 tests ✅
- ScheduledJobsTest: 10 tests ✅
- BillingAPITest: 13 tests ✅

**Total: 88 tests, 100% passing**

---

## 3. Approvals Module

### Status: ✅ Fully Implemented

### Purpose
Flexible multi-step approval workflow system with resource limit validation integration.

### Key Responsibilities
- **Request Management**: Create and track approval requests
- **Multi-step Workflows**: Support sequential and parallel approvals
- **Resource Validation**: Check limits before approval execution
- **Approver Assignment**: Role-based or user-specific approvers
- **Escalation**: Escalate stuck or urgent requests
- **Audit Trail**: Complete comment and action history
- **Notifications**: Alert approvers and requesters

### Domain Models

#### ApprovalRequest Model
```php
namespace App\Modules\Approvals\Domain\Models;

class ApprovalRequest extends Model
{
    protected $fillable = [
        'organization_id',
        'requester_id',
        'type',
        'reference_type',
        'reference_id',
        'status',
        'description',
        'amount',
        'data',
        'current_step',
        'submitted_at',
        'completed_at'
    ];

    protected $casts = [
        'data' => 'array',
        'amount' => 'decimal:2',
        'submitted_at' => 'datetime',
        'completed_at' => 'datetime',
    ];

    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    public function requester(): BelongsTo
    {
        return $this->belongsTo(User::class, 'requester_id');
    }

    public function steps(): HasMany
    {
        return $this->hasMany(ApprovalStep::class);
    }

    public function comments(): HasMany
    {
        return $this->hasMany(ApprovalComment::class);
    }

    public function currentStepModel(): ?ApprovalStep
    {
        return $this->steps()
            ->where('step_number', $this->current_step)
            ->first();
    }

    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    public function isApproved(): bool
    {
        return $this->status === 'approved';
    }

    public function isRejected(): bool
    {
        return $this->status === 'rejected';
    }
}
```

#### ApprovalStep Model
```php
namespace App\Modules\Approvals\Domain\Models;

class ApprovalStep extends Model
{
    protected $fillable = [
        'organization_id',
        'approval_request_id',
        'step_number',
        'approver_id',
        'approver_role',
        'status',
        'approved_by',
        'rejected_by',
        'approved_at',
        'rejected_at',
        'required_count',
        'approval_type'
    ];

    protected $casts = [
        'approved_at' => 'datetime',
        'rejected_at' => 'datetime',
    ];

    public function approvalRequest(): BelongsTo
    {
        return $this->belongsTo(ApprovalRequest::class);
    }

    public function approver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approver_id');
    }

    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function rejectedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'rejected_by');
    }

    public function isPending(): bool
    {
        return $this->status === 'pending';
    }
}
```

#### ApprovalComment Model
```php
namespace App\Modules\Approvals\Domain\Models;

class ApprovalComment extends Model
{
    protected $fillable = [
        'organization_id',
        'approval_request_id',
        'user_id',
        'comment',
        'type'
    ];

    public function approvalRequest(): BelongsTo
    {
        return $this->belongsTo(ApprovalRequest::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
```

### Services

#### ApprovalService (Enhanced - 9 tests ✅)
```php
namespace App\Modules\Approvals\Domain\Services;

class ApprovalService
{
    public function __construct(
        private ResourceLimitService $limitService
    ) {}

    public function createApprovalRequest(array $data): ApprovalRequest
    {
        $request = ApprovalRequest::create([
            'organization_id' => $data['organization_id'],
            'requester_id' => $data['requester_id'],
            'type' => $data['type'],
            'reference_type' => $data['reference_type'] ?? null,
            'reference_id' => $data['reference_id'] ?? null,
            'status' => 'pending',
            'description' => $data['description'],
            'amount' => $data['amount'] ?? null,
            'data' => $data['data'] ?? [],
            'current_step' => 1,
            'submitted_at' => now(),
        ]);

        // Create approval steps
        foreach ($data['steps'] as $index => $stepData) {
            ApprovalStep::create([
                'organization_id' => $data['organization_id'],
                'approval_request_id' => $request->id,
                'step_number' => $index + 1,
                'approver_id' => $stepData['approver_id'] ?? null,
                'approver_role' => $stepData['approver_role'] ?? null,
                'status' => 'pending',
                'required_count' => $stepData['required_count'] ?? 1,
                'approval_type' => $stepData['approval_type'] ?? 'sequential',
            ]);
        }

        // Notify approvers of first step
        $this->notifyStepApprovers($request, 1);

        return $request->load('steps');
    }

    public function approveRequest(
        ApprovalRequest $request,
        User $approver,
        ?string $comment = null
    ): ApprovalRequest {
        if (!$request->isPending()) {
            throw new InvalidApprovalStateException(
                "Request is not in pending state"
            );
        }

        $currentStep = $request->currentStepModel();
        
        if (!$this->canApprove($currentStep, $approver)) {
            throw new UnauthorizedApprovalException(
                "User not authorized to approve this step"
            );
        }

        // CHECK RESOURCE LIMITS BEFORE APPROVAL
        if ($request->type === 'registration') {
            $this->checkResourceLimitsForRegistration($request);
        } elseif ($request->type === 'sub_org_creation') {
            $this->checkResourceLimitsForSubOrg($request);
        }

        // Mark step as approved
        $currentStep->update([
            'status' => 'approved',
            'approved_by' => $approver->id,
            'approved_at' => now(),
        ]);

        // Add comment if provided
        if ($comment) {
            $this->addComment($request, $approver, $comment, 'approval');
        }

        // Check if all steps are complete
        if ($this->areAllStepsApproved($request)) {
            $request->update([
                'status' => 'approved',
                'completed_at' => now(),
            ]);

            // Execute the approved action
            $this->executeApprovedAction($request);
            
            // Notify requester
            $this->notifyRequester($request, 'approved');
        } else {
            // Move to next step
            $request->increment('current_step');
            $this->notifyStepApprovers($request, $request->current_step);
        }

        return $request->fresh();
    }

    public function rejectRequest(
        ApprovalRequest $request,
        User $rejector,
        string $reason
    ): ApprovalRequest {
        $currentStep = $request->currentStepModel();
        
        $currentStep->update([
            'status' => 'rejected',
            'rejected_by' => $rejector->id,
            'rejected_at' => now(),
        ]);

        $request->update([
            'status' => 'rejected',
            'completed_at' => now(),
        ]);

        $this->addComment($request, $rejector, $reason, 'rejection');
        $this->notifyRequester($request, 'rejected');

        return $request;
    }

    private function checkResourceLimitsForRegistration(
        ApprovalRequest $request
    ): void {
        $organization = $request->organization;
        $subscription = $organization->subscription;

        $limitCheck = $this->limitService->checkUserLimit($subscription);

        if (!$limitCheck['within_limit']) {
            throw new ResourceLimitExceededException(
                "User limit exceeded. Cannot approve registration.",
                [
                    'current' => $limitCheck['current'],
                    'limit' => $limitCheck['limit'],
                    'suggestions' => $limitCheck['suggestions'],
                ]
            );
        }
    }

    private function checkResourceLimitsForSubOrg(
        ApprovalRequest $request
    ): void {
        $organization = $request->organization;
        $subscription = $organization->subscription;

        // Check sub-org limit
        $subOrgCheck = $this->limitService->checkSubOrgLimit($subscription);
        if (!$subOrgCheck['within_limit']) {
            throw new ResourceLimitExceededException(
                "Sub-organization limit exceeded.",
                $subOrgCheck
            );
        }

        // Check hierarchy depth
        $parentOrg = Organization::find($request->data['parent_id']);
        $newDepth = $parentOrg->hierarchy_depth + 1;
        
        if ($subscription->plan->hierarchy_depth_limit > 0 &&
            $newDepth > $subscription->plan->hierarchy_depth_limit) {
            throw new HierarchyDepthExceededException(
                "Maximum hierarchy depth would be exceeded"
            );
        }
    }

    private function executeApprovedAction(ApprovalRequest $request): void
    {
        switch ($request->type) {
            case 'registration':
                $this->executeUserRegistration($request);
                break;
            case 'sub_org_creation':
                $this->executeSubOrgCreation($request);
                break;
            // Add more types as needed
        }
    }

    private function executeUserRegistration(ApprovalRequest $request): void
    {
        $userData = $request->data;
        
        $user = User::create([
            'organization_id' => $request->organization_id,
            'name' => $userData['name'],
            'email' => $userData['email'],
            'password' => bcrypt($userData['password']),
            'status' => 'active',
        ]);

        // Increment user counter
        $subscription = $request->organization->subscription;
        $this->limitService->incrementUserCounter($subscription);

        // Update reference
        $request->update([
            'reference_type' => User::class,
            'reference_id' => $user->id,
        ]);
    }

    private function executeSubOrgCreation(ApprovalRequest $request): void
    {
        $orgData = $request->data;
        
        $subOrg = Organization::create([
            'parent_id' => $orgData['parent_id'],
            'type' => 'sub_organization',
            'name' => $orgData['name'],
            'slug' => $orgData['slug'],
            'status' => 'active',
        ]);

        // Increment sub-org counter
        $subscription = $request->organization->subscription;
        $this->limitService->incrementSubOrgCounter($subscription);

        // Update reference
        $request->update([
            'reference_type' => Organization::class,
            'reference_id' => $subOrg->id,
        ]);
    }

    public function getPendingApprovals(User $user): Collection
    {
        return ApprovalRequest::where('status', 'pending')
            ->whereHas('steps', function ($query) use ($user) {
                $query->where('status', 'pending')
                    ->where(function ($q) use ($user) {
                        $q->where('approver_id', $user->id)
                          ->orWhere('approver_role', $user->role);
                    });
            })
            ->with(['requester', 'organization', 'steps'])
            ->get();
    }
}
```

### API Endpoints (Planned)

```
GET    /api/v1/approvals
POST   /api/v1/approvals
GET    /api/v1/approvals/{id}
POST   /api/v1/approvals/{id}/approve
POST   /api/v1/approvals/{id}/reject
POST   /api/v1/approvals/{id}/escalate
POST   /api/v1/approvals/{id}/comments
GET    /api/v1/approvals/pending
```

### Example Workflow

**User Registration Approval:**

```
1. User submits registration form
   ↓
2. System creates ApprovalRequest (type: registration)
   ↓
3. Step 1: Notify HR Manager
   ↓
4. HR Manager reviews → Approves
   ↓
5. System checks resource limits (users)
   ├─ If exceeded → Reject with upgrade message
   └─ If within limit → Continue
   ↓
6. Step 2: Notify Department Head
   ↓
7. Department Head reviews → Approves
   ↓
8. All steps approved
   ↓
9. System creates User record
   ↓
10. Increment user counter
   ↓
11. Notify requester: Approved
   ↓
12. Send welcome email to new user
```

### Dependencies
- **Organizations Module**: Get organization context
- **Users Module**: Create users post-approval
- **Billing Module**: Check resource limits
- **Notifications Module**: Notify stakeholders

### Database Tables
- `approval_requests`
- `approval_steps`
- `approval_comments`

### Tests
- ApprovalServiceTest: 9 tests ✅
  - Approval with resource limit checks
  - Registration approval flow
  - Sub-org creation approval
  - Rejection scenarios
  - Unlimited plan handling

---

## 4. Users Module

### Status: 🚧 In Progress

### Purpose
User account management with authentication, authorization, and profile management.

### Key Responsibilities
- **User CRUD**: Create, read, update, delete user accounts
- **Authentication**: Login, logout, password reset
- **Profile Management**: Update user information
- **Status Management**: Active, inactive, pending states
- **Organization Assignment**: Link users to organizations
- **Role Assignment**: Assign roles and permissions (future)

### Domain Models

#### User Model
```php
namespace App\Modules\Users\Domain\Models;

class User extends Authenticatable
{
    protected $fillable = [
        'organization_id',
        'name',
        'email',
        'password',
        'status',
        'email_verified_at',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
    ];

    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    public function approvalRequests(): HasMany
    {
        return $this->hasMany(ApprovalRequest::class, 'requester_id');
    }

    public function approvedRequests(): HasMany
    {
        return $this->hasMany(ApprovalStep::class, 'approved_by');
    }

    public function notifications(): HasMany
    {
        return $this->hasMany(Notification::class);
    }

    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeByOrganization($query, string $organizationId)
    {
        return $query->where('organization_id', $organizationId);
    }

    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    public function belongsToTenant(Organization $tenant): bool
    {
        $org = $this->organization;
        
        // Traverse up to find tenant
        while ($org) {
            if ($org->type === 'tenant' && $org->id === $tenant->id) {
                return true;
            }
            $org = $org->parent;
        }
        
        return false;
    }
}
```

### Services

#### UserService
```php
namespace App\Modules\Users\Domain\Services;

class UserService
{
    public function __construct(
        private ResourceLimitService $limitService,
        private ApprovalService $approvalService
    ) {}

    public function createUser(array $data): User|ApprovalRequest
    {
        $organization = Organization::findOrFail($data['organization_id']);
        $subscription = $this->getTenantSubscription($organization);

        // Check if user limit allows direct creation
        $limitCheck = $this->limitService->checkUserLimit($subscription);

        if (!$limitCheck['within_limit']) {
            throw new ResourceLimitExceededException(
                "User limit exceeded",
                $limitCheck
            );
        }

        // Check if approval is required
        if ($this->requiresApproval($organization)) {
            return $this->createApprovalRequest($data);
        }

        // Direct creation
        $user = User::create([
            'organization_id' => $data['organization_id'],
            'name' => $data['name'],
            'email' => $data['email'],
            'password' => bcrypt($data['password']),
            'status' => 'active',
        ]);

        // Increment counter
        $this->limitService->incrementUserCounter($subscription);

        // Send welcome email
        $user->notify(new WelcomeNotification());

        return $user;
    }

    public function updateUser(User $user, array $data): User
    {
        $user->update([
            'name' => $data['name'] ?? $user->name,
            'email' => $data['email'] ?? $user->email,
        ]);

        if (isset($data['password'])) {
            $user->update([
                'password' => bcrypt($data['password'])
            ]);
        }

        return $user;
    }

    public function deleteUser(User $user): bool
    {
        $organization = $user->organization;
        $subscription = $this->getTenantSubscription($organization);

        $deleted = $user->delete();

        if ($deleted) {
            // Decrement counter
            $this->limitService->decrementUserCounter($subscription);
        }

        return $deleted;
    }

    public function getUsersByOrganization(
        Organization $organization,
        bool $includeSubOrgs = false
    ): Collection {
        if (!$includeSubOrgs) {
            return $organization->users;
        }

        // Get all descendant organizations
        $orgService = app(OrganizationService::class);
        $descendants = $orgService->getDescendants($organization);
        $orgIds = $descendants->pluck('id')->push($organization->id);

        return User::whereIn('organization_id', $orgIds)->get();
    }

    private function requiresApproval(Organization $organization): bool
    {
        // Check if approval workflow is configured
        return $organization->metadata['require_approval'] ?? false;
    }

    private function createApprovalRequest(array $data): ApprovalRequest
    {
        return $this->approvalService->createApprovalRequest([
            'organization_id' => $data['organization_id'],
            'requester_id' => auth()->id(),
            'type' => 'registration',
            'description' => "User registration for {$data['email']}",
            'data' => $data,
            'steps' => [
                [
                    'approver_role' => 'hr_manager',
                    'approval_type' => 'sequential',
                ],
                [
                    'approver_role' => 'department_head',
                    'approval_type' => 'sequential',
                ],
            ],
        ]);
    }

    private function getTenantSubscription(Organization $org): Subscription
    {
        while ($org->type !== 'tenant' && $org->parent) {
            $org = $org->parent;
        }
        
        return $org->subscription;
    }
}
```

### API Endpoints

```
GET    /api/v1/users
POST   /api/v1/users
GET    /api/v1/users/{id}
PATCH  /api/v1/users/{id}
DELETE /api/v1/users/{id}
POST   /api/v1/users/{id}/activate
POST   /api/v1/users/{id}/deactivate
POST   /api/v1/users/{id}/reset-password
```

### Example Request/Response

**Create User:**
```http
POST /api/v1/users
Content-Type: application/json
Authorization: Bearer {token}

{
  "organization_id": "org-uuid",
  "name": "John Doe",
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "role": "employee"
}
```

**Response (Direct Creation):**
```json
{
  "success": true,
  "data": {
    "id": "user-uuid",
    "organization_id": "org-uuid",
    "name": "John Doe",
    "email": "<EMAIL>",
    "status": "active",
    "created_at": "2025-10-30T06:00:00Z"
  },
  "message": "User created successfully"
}
```

**Response (Approval Required):**
```json
{
  "success": true,
  "data": {
    "approval_request_id": "approval-uuid",
    "status": "pending",
    "message": "User registration submitted for approval",
    "steps": [
      {
        "step_number": 1,
        "approver_role": "hr_manager",
        "status": "pending"
      },
      {
        "step_number": 2,
        "approver_role": "department_head",
        "status": "pending"
      }
    ]
  }
}
```

### Dependencies
- **Organizations Module**: Organization context
- **Billing Module**: Check user limits
- **Approvals Module**: Approval workflows
- **Notifications Module**: Welcome emails, notifications

### Database Tables
- `users`

### Tests
- UserServiceTest (planned)
- UserAPITest (planned)

---

## 5. Notifications Module

### Status: 📋 Planned

### Purpose
Unified notification system supporting multiple channels (email, in-app, SMS) with priority-based delivery.

### Key Responsibilities
- **Multi-Channel Delivery**: Email, in-app, SMS (future), push notifications
- **Priority Management**: Low, medium, high, critical priorities
- **Template Management**: Reusable notification templates
- **Delivery Tracking**: Track sent, failed, read status
- **Batch Notifications**: Send to multiple recipients efficiently
- **Preferences**: User notification preferences per channel

### Domain Models

#### Notification Model
```php
namespace App\Modules\Notifications\Domain\Models;

class Notification extends Model
{
    protected $fillable = [
        'organization_id',
        'user_id',
        'type',
        'title',
        'message',
        'data',
        'priority',
        'read_at',
    ];

    protected $casts = [
        'data' => 'array',
        'read_at' => 'datetime',
    ];

    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function scopeUnread($query)
    {
        return $query->whereNull('read_at');
    }

    public function scopeByPriority($query, string $priority)
    {
        return $query->where('priority', $priority);
    }

    public function scopeCritical($query)
    {
        return $query->where('priority', 'critical');
    }

    public function markAsRead(): void
    {
        $this->update(['read_at' => now()]);
    }

    public function isRead(): bool
    {
        return $this->read_at !== null;
    }
}
```

### Services

#### NotificationService
```php
namespace App\Modules\Notifications\Domain\Services;

class NotificationService
{
    public function send(array $data): Notification
    {
        $notification = Notification::create([
            'organization_id' => $data['organization_id'],
            'user_id' => $data['user_id'],
            'type' => $data['type'],
            'title' => $data['title'],
            'message' => $data['message'],
            'data' => $data['data'] ?? [],
            'priority' => $data['priority'] ?? 'medium',
        ]);

        // Dispatch delivery job based on priority
        $this->dispatchDelivery($notification);

        return $notification;
    }

    public function sendBatch(array $recipients, array $data): Collection
    {
        $notifications = collect();

        foreach ($recipients as $userId) {
            $notifications->push($this->send([
                ...$data,
                'user_id' => $userId,
            ]));
        }

        return $notifications;
    }

    public function sendEmail(User $user, string $subject, string $body): void
    {
        Mail::to($user->email)->send(new GenericMail($subject, $body));
    }

    public function sendUsageAlert(ResourceUsageLog $log): void
    {
        $subscription = $log->subscription;
        $organization = $subscription->organization;
        
        // Get admin users
        $admins = $organization->users()
            ->where('role', 'admin')
            ->get();

        $message = $this->buildAlertMessage($log);

        foreach ($admins as $admin) {
            $this->send([
                'organization_id' => $organization->id,
                'user_id' => $admin->id,
                'type' => 'resource_alert',
                'title' => 'Resource Usage Alert',
                'message' => $message,
                'priority' => $log->alert_level === 'exceeded' ? 'critical' : 'high',
                'data' => [
                    'resource_type' => $log->resource_type,
                    'usage_percentage' => $log->usage_percentage,
                    'alert_level' => $log->alert_level,
                ],
            ]);
        }
    }

    public function markAllAsRead(User $user): int
    {
        return Notification::where('user_id', $user->id)
            ->whereNull('read_at')
            ->update(['read_at' => now()]);
    }

    public function getUnreadCount(User $user): int
    {
        return Notification::where('user_id', $user->id)
            ->whereNull('read_at')
            ->count();
    }

    private function dispatchDelivery(Notification $notification): void
    {
        if ($notification->priority === 'critical') {
            SendNotificationJob::dispatch($notification)->onQueue('high');
        } else {
            SendNotificationJob::dispatch($notification)->onQueue('default');
        }
    }

    private function buildAlertMessage(ResourceUsageLog $log): string
    {
        $resourceName = ucfirst(str_replace('_', ' ', $log->resource_type));
        $percentage = number_format($log->usage_percentage, 1);

        return match($log->alert_level) {
            'warning' => "Your {$resourceName} usage is at {$percentage}% of your limit.",
            'critical' => "URGENT: Your {$resourceName} usage is at {$percentage}% of your limit. Please upgrade soon.",
            'exceeded' => "CRITICAL: Your {$resourceName} limit has been exceeded. Upgrade immediately to avoid service interruption.",
            default => "Your {$resourceName} usage update: {$percentage}%",
        };
    }
}
```

### Notification Types

```php
// Usage Alerts
- resource_warning: 75% threshold
- resource_critical: 90% threshold
- resource_exceeded: 100% exceeded

// Approval Notifications
- approval_requested: New approval for you
- approval_approved: Your request approved
- approval_rejected: Your request rejected
- approval_escalated: Request escalated

// Billing Notifications
- subscription_created: New subscription
- subscription_upgraded: Plan upgraded
- subscription_downgraded: Plan downgraded
- subscription_cancelled: Subscription cancelled
- addon_purchased: Add-on purchased
- payment_failed: Payment failed

// User Notifications
- user_created: Welcome notification
- password_reset: Password reset link
- account_activated: Account activated
- account_suspended: Account suspended

// Organization Notifications
- sub_org_created: Sub-organization created
- org_suspended: Organization suspended
- org_activated: Organization activated
```

### API Endpoints

```
GET    /api/v1/notifications
GET    /api/v1/notifications/unread-count
POST   /api/v1/notifications/{id}/mark-read
POST   /api/v1/notifications/mark-all-read
DELETE /api/v1/notifications/{id}
POST   /api/v1/notifications/preferences
```

### Dependencies
- **All Modules**: Receive notification requests from all modules
- **Users Module**: Recipient information
- **Organizations Module**: Organization context

### Database Tables
- `notifications`
- `notification_preferences` (future)

---

## 6. Audit Module

### Status: 📋 Planned

### Purpose
Comprehensive activity logging and audit trail for compliance and security.

### Key Responsibilities
- **Action Logging**: Log all critical system actions
- **Change Tracking**: Track before/after values
- **User Attribution**: Record who performed each action
- **IP Tracking**: Record IP addresses and user agents
- **Compliance**: Meet 7-year retention requirements
- **Search**: Query audit logs by various filters
- **Export**: Generate audit reports

### Domain Models

#### AuditLog Model
```php
namespace App\Modules\Audit\Domain\Models;

class AuditLog extends Model
{
    const UPDATED_AT = null; // Only created_at

    protected $fillable = [
        'organization_id',
        'user_id',
        'action',
        'auditable_type',
        'auditable_id',
        'old_values',
        'new_values',
        'ip_address',
        'user_agent',
    ];

    protected $casts = [
        'old_values' => 'array',
        'new_values' => 'array',
    ];

    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function auditable(): MorphTo
    {
        return $this->morphTo();
    }

    public function scopeByAction($query, string $action)
    {
        return $query->where('action', $action);
    }

    public function scopeByUser($query, string $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeDateRange($query, Carbon $from, Carbon $to)
    {
        return $query->whereBetween('created_at', [$from, $to]);
    }

    public function getChanges(): array
    {
        $changes = [];
        
        foreach ($this->new_values as $key => $newValue) {
            $oldValue = $this->old_values[$key] ?? null;
            
            if ($oldValue !== $newValue) {
                $changes[$key] = [
                    'old' => $oldValue,
                    'new' => $newValue,
                ];
            }
        }
        
        return $changes;
    }
}
```

### Services

#### AuditService
```php
namespace App\Modules\Audit\Domain\Services;

class AuditService
{
    public function log(
        string $action,
        Model $auditable,
        ?array $oldValues = null,
        ?array $newValues = null
    ): AuditLog {
        return AuditLog::create([
            'organization_id' => $this->getOrganizationId(),
            'user_id' => auth()->id(),
            'action' => $action,
            'auditable_type' => get_class($auditable),
            'auditable_id' => $auditable->id,
            'old_values' => $oldValues,
            'new_values' => $newValues,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);
    }

    public function logCreated(Model $model): AuditLog
    {
        return $this->log(
            'created',
            $model,
            null,
            $model->getAttributes()
        );
    }

    public function logUpdated(Model $model): AuditLog
    {
        return $this->log(
            'updated',
            $model,
            $model->getOriginal(),
            $model->getAttributes()
        );
    }

    public function logDeleted(Model $model): AuditLog
    {
        return $this->log(
            'deleted',
            $model,
            $model->getAttributes(),
            null
        );
    }

    public function query(array $filters): Collection
    {
        $query = AuditLog::query();

        if (isset($filters['user_id'])) {
            $query->byUser($filters['user_id']);
        }

        if (isset($filters['action'])) {
            $query->byAction($filters['action']);
        }

        if (isset($filters['from']) && isset($filters['to'])) {
            $query->dateRange(
                Carbon::parse($filters['from']),
                Carbon::parse($filters['to'])
            );
        }

        if (isset($filters['auditable_type'])) {
            $query->where('auditable_type', $filters['auditable_type']);
        }

        return $query->orderBy('created_at', 'desc')
            ->paginate($filters['per_page'] ?? 50);
    }

    public function exportReport(array $filters): string
    {
        $logs = $this->query($filters)->get();
        
        // Generate CSV or Excel export
        return $this->generateCSV($logs);
    }

    private function getOrganizationId(): ?string
    {
        return auth()->user()?->organization_id;
    }

    private function generateCSV(Collection $logs): string
    {
        // CSV generation logic
        // Return file path
    }
}
```

### Auditable Actions

```php
// User Actions
- user.created
- user.updated
- user.deleted
- user.login
- user.logout
- user.password_changed

// Organization Actions
- organization.created
- organization.updated
- organization.deleted
- organization.suspended
- organization.activated

// Billing Actions
- subscription.created
- subscription.upgraded
- subscription.downgraded
- subscription.cancelled
- addon.purchased
- addon.removed

// Approval Actions
- approval.submitted
- approval.approved
- approval.rejected
- approval.escalated

// Settings Actions
- settings.updated
- plan.created
- plan.updated
```

### API Endpoints

```
GET    /api/v1/audit/logs
GET    /api/v1/audit/logs/{id}
POST   /api/v1/audit/export
GET    /api/v1/audit/stats
```

### Dependencies
- **All Modules**: Receive audit events from all modules
- **Users Module**: User information
- **Organizations Module**: Organization context

### Database Tables
- `audit_logs` (7-year retention)

### Retention Policy

- **Standard Logs**: 7 years (regulatory compliance)
- **Critical Actions**: Permanent retention
- **Cleanup Job**: Run annually to archive old logs

---

## 7. Reports Module

### Status: 📋 Planned

### Purpose
Analytics and reporting system for usage, billing, and activity insights.

### Key Responsibilities
- **Usage Reports**: Resource consumption over time
- **Billing Reports**: Revenue, subscriptions, add-ons
- **Activity Reports**: User actions, logins, changes
- **Custom Dashboards**: Configurable widgets
- **Scheduled Reports**: Email reports on schedule
- **Export**: PDF, Excel, CSV formats

### Services

#### ReportService
```php
namespace App\Modules\Reports\Domain\Services;

class ReportService
{
    public function generateUsageReport(
        Organization $organization,
        Carbon $from,
        Carbon $to
    ): array {
        $subscription = $organization->subscription;
        
        $logs = ResourceUsageLog::where('subscription_id', $subscription->id)
            ->whereBetween('recorded_at', [$from, $to])
            ->orderBy('recorded_at')
            ->get();

        return [
            'organization' => $organization->name,
            'period' => [
                'from' => $from->toDateString(),
                'to' => $to->toDateString(),
            ],
            'resources' => [
                'users' => $this->aggregateResourceData($logs, 'users'),
                'sub_orgs' => $this->aggregateResourceData($logs, 'sub_orgs'),
                'storage' => $this->aggregateResourceData($logs, 'storage'),
            ],
            'alerts' => $logs->where('alert_level', '!=', 'normal')->count(),
        ];
    }

    public function generateBillingReport(
        Organization $portalOwner,
        Carbon $from,
        Carbon $to
    ): array {
        $subscriptions = Subscription::where('organization_id', $portalOwner->id)
            ->whereBetween('created_at', [$from, $to])
            ->with(['plan', 'organization'])
            ->get();

        return [
            'period' => [
                'from' => $from->toDateString(),
                'to' => $to->toDateString(),
            ],
            'metrics' => [
                'total_subscriptions' => $subscriptions->count(),
                'active_subscriptions' => $subscriptions->where('status', 'active')->count(),
                'total_revenue' => $subscriptions->sum('price'),
                'mrr' => $this->calculateMRR($subscriptions),
                'arr' => $this->calculateARR($subscriptions),
            ],
            'plan_breakdown' => $this->planBreakdown($subscriptions),
            'addon_revenue' => $this->addonRevenue($from, $to),
        ];
    }

    public function generateActivityReport(
        Organization $organization,
        Carbon $from,
        Carbon $to
    ): array {
        $logs = AuditLog::where('organization_id', $organization->id)
            ->whereBetween('created_at', [$from, $to])
            ->get();

        return [
            'total_actions' => $logs->count(),
            'unique_users' => $logs->pluck('user_id')->unique()->count(),
            'actions_by_type' => $logs->groupBy('action')->map->count(),
            'top_users' => $this->topActiveUsers($logs),
            'peak_hours' => $this->calculatePeakHours($logs),
        ];
    }

    private function aggregateResourceData(Collection $logs, string $type): array
    {
        $resourceLogs = $logs->where('resource_type', $type);

        return [
            'current' => $resourceLogs->last()->usage_value ?? 0,
            'limit' => $resourceLogs->last()->limit_value ?? 0,
            'average' => $resourceLogs->avg('usage_value'),
            'peak' => $resourceLogs->max('usage_value'),
            'trend' => $this->calculateTrend($resourceLogs),
        ];
    }

    private function calculateMRR(Collection $subscriptions): float
    {
        return $subscriptions
            ->where('billing_period', 'monthly')
            ->sum('price');
    }

    private function calculateARR(Collection $subscriptions): float
    {
        $monthly = $this->calculateMRR($subscriptions);
        $yearly = $subscriptions
            ->where('billing_period', 'yearly')
            ->sum('price');

        return ($monthly * 12) + $yearly;
    }
}
```

### Report Types

**Usage Reports:**
- Daily resource consumption
- Weekly usage trends
- Monthly summaries
- Alert history

**Billing Reports:**
- Monthly recurring revenue (MRR)
- Annual recurring revenue (ARR)
- Churn rate
- Plan distribution
- Add-on adoption
- Revenue by tenant

**Activity Reports:**
- User activity levels
- Login patterns
- Feature usage
- Peak usage times
- Dormant accounts

**Custom Dashboards:**
- Real-time metrics
- Configurable widgets
- Drill-down capabilities
- Comparative analysis

### API Endpoints

```
POST   /api/v1/reports/usage
POST   /api/v1/reports/billing
POST   /api/v1/reports/activity
GET    /api/v1/reports/dashboard
POST   /api/v1/reports/schedule
POST   /api/v1/reports/export
```

### Dependencies
- **Billing Module**: Usage and billing data
- **Audit Module**: Activity logs
- **Organizations Module**: Organization hierarchy

---

## 8. Settings Module

### Status: 📋 Planned

### Purpose
System-wide and tenant-specific configuration management.

### Key Responsibilities
- **Global Settings**: System-wide configurations
- **Tenant Settings**: Organization-specific settings
- **Feature Flags**: Enable/disable features
- **Customization**: Branding, themes, preferences
- **Validation**: Ensure setting consistency

### Domain Models

#### Setting Model
```php
namespace App\Modules\Settings\Domain\Models;

class Setting extends Model
{
    protected $fillable = [
        'organization_id',
        'key',
        'value',
        'type',
        'is_public',
        'metadata',
    ];

    protected $casts = [
        'value' => 'array',
        'is_public' => 'boolean',
        'metadata' => 'array',
    ];

    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    public function scopeGlobal($query)
    {
        return $query->whereNull('organization_id');
    }

    public function scopeForOrganization($query, string $orgId)
    {
        return $query->where('organization_id', $orgId);
    }

    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }
}
```

### Services

#### SettingsService
```php
namespace App\Modules\Settings\Domain\Services;

class SettingsService
{
    public function get(string $key, ?string $organizationId = null): mixed
    {
        $setting = Setting::where('key', $key)
            ->when($organizationId, fn($q) => $q->forOrganization($organizationId))
            ->first();

        return $setting?->value;
    }

    public function set(
        string $key,
        mixed $value,
        ?string $organizationId = null
    ): Setting {
        return Setting::updateOrCreate(
            [
                'key' => $key,
                'organization_id' => $organizationId,
            ],
            [
                'value' => $value,
                'type' => gettype($value),
            ]
        );
    }

    public function delete(string $key, ?string $organizationId = null): bool
    {
        return Setting::where('key', $key)
            ->when($organizationId, fn($q) => $q->forOrganization($organizationId))
            ->delete() > 0;
    }

    public function all(?string $organizationId = null): Collection
    {
        return Setting::when(
            $organizationId,
            fn($q) => $q->forOrganization($organizationId),
            fn($q) => $q->global()
        )->get();
    }
}
```

### Setting Categories

**System Settings:**
- app.name
- app.timezone
- app.locale
- maintenance.mode
- maintenance.message

**Billing Settings:**
- billing.currency
- billing.tax_rate
- billing.grace_period_days
- billing.trial_days_default

**Notification Settings:**
- notifications.email_enabled
- notifications.sms_enabled
- notifications.alert_thresholds

**Security Settings:**
- security.password_min_length
- security.password_require_special
- security.session_timeout
- security.max_login_attempts

**Feature Flags:**
- features.approvals_enabled
- features.advanced_reports_enabled
- features.api_access_enabled
- features.webhooks_enabled

### API Endpoints

```
GET    /api/v1/settings
GET    /api/v1/settings/{key}
PUT    /api/v1/settings/{key}
DELETE /api/v1/settings/{key}
POST   /api/v1/settings/bulk-update
```

### Dependencies
- **Organizations Module**: Organization context
- **All Modules**: Settings consumers

### Database Tables
- `settings`

---

## 9. Integration Module

### Status: 📋 Planned

### Purpose
External API integrations and webhook management.

### Key Responsibilities
- **API Connectors**: Third-party service integrations
- **Webhook Management**: Incoming and outgoing webhooks
- **API Key Management**: Secure key storage
- **OAuth Integration**: OAuth 2.0 support
- **Rate Limiting**: Control API usage
- **Retry Logic**: Handle failed requests

### Domain Models

#### Integration Model
```php
namespace App\Modules\Integration\Domain\Models;

class Integration extends Model
{
    protected $fillable = [
        'organization_id',
        'name',
        'slug',
        'type',
        'api_key',
        'api_secret',
        'config',
        'status',
        'last_sync_at',
    ];

    protected $casts = [
        'config' => 'encrypted:array',
        'last_sync_at' => 'datetime',
    ];

    protected $hidden = [
        'api_key',
        'api_secret',
    ];

    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    public function webhooks(): HasMany
    {
        return $this->hasMany(Webhook::class);
    }

    public function isActive(): bool
    {
        return $this->status === 'active';
    }
}
```

#### Webhook Model
```php
namespace App\Modules\Integration\Domain\Models;

class Webhook extends Model
{
    protected $fillable = [
        'integration_id',
        'organization_id',
        'url',
        'events',
        'secret',
        'status',
        'last_triggered_at',
    ];

    protected $casts = [
        'events' => 'array',
        'last_triggered_at' => 'datetime',
    ];

    public function integration(): BelongsTo
    {
        return $this->belongsTo(Integration::class);
    }

    public function shouldTriggerFor(string $event): bool
    {
        return in_array($event, $this->events ?? []);
    }
}
```

### Services

#### IntegrationService
```php
namespace App\Modules\Integration\Domain\Services;

class IntegrationService
{
    public function createIntegration(array $data): Integration
    {
        return Integration::create([
            'organization_id' => $data['organization_id'],
            'name' => $data['name'],
            'slug' => Str::slug($data['name']),
            'type' => $data['type'],
            'api_key' => $this->generateApiKey(),
            'api_secret' => $this->generateApiSecret(),
            'config' => $data['config'] ?? [],
            'status' => 'active',
        ]);
    }

    public function triggerWebhook(string $event, array $payload): void
    {
        $webhooks = Webhook::where('status', 'active')
            ->get()
            ->filter(fn($webhook) => $webhook->shouldTriggerFor($event));

        foreach ($webhooks as $webhook) {
            dispatch(new SendWebhookJob($webhook, $event, $payload));
        }
    }

    private function generateApiKey(): string
    {
        return 'ek_' . Str::random(32);
    }

    private function generateApiSecret(): string
    {
        return Str::random(64);
    }
}
```

### Integration Types

**Accounting:**
- Tally ERP
- QuickBooks
- Zoho Books

**Communication:**
- WhatsApp Business API
- Twilio SMS
- SendGrid Email

**Analytics:**
- Google Analytics
- Mixpanel
- Segment

**Custom:**
- REST APIs
- GraphQL
- SOAP

### API Endpoints

```
GET    /api/v1/integrations
POST   /api/v1/integrations
GET    /api/v1/integrations/{id}
PATCH  /api/v1/integrations/{id}
DELETE /api/v1/integrations/{id}
POST   /api/v1/integrations/{id}/test
POST   /api/v1/integrations/{id}/sync

GET    /api/v1/webhooks
POST   /api/v1/webhooks
DELETE /api/v1/webhooks/{id}
```

### Dependencies
- **Organizations Module**: Organization context
- **Settings Module**: Integration configurations

### Database Tables
- `integrations`
- `webhooks`
- `webhook_deliveries` (logs)

---

## Future Modules

### 10. RolesPermissions Module
- Advanced RBAC system
- Custom roles
- Fine-grained permissions
- Permission inheritance
- Role templates

### 11. Accounting Module
- General ledger
- Accounts payable/receivable
- Journal entries
- Financial statements
- Tax calculations (India-specific)

### 12. Inventory Module
- Product management
- Stock tracking
- Warehouse management
- Purchase orders
- Sales orders

### 13. Additional Modules (Future)
- HR Management
- Payroll
- CRM
- Project Management
- Document Management

---

## Module Communication

### Communication Patterns

#### 1. Direct Service Calls
```php
// BillingService calling ResourceLimitService
$limitCheck = $this->limitService->checkUserLimit($subscription);
```

#### 2. Event-Driven
```php
// Fire event
event(new UserCreated($user));

// Listen in another module
class IncrementUserCounter
{
    public function handle(UserCreated $event)
    {
        $this->limitService->incrementUserCounter($event->user->organization->subscription);
    }
}
```

#### 3. Queue Jobs
```php
// Dispatch background job
dispatch(new SendWelcomeEmail($user));
```

### Module Communication Matrix

| From/To | Orgs | Billing | Approvals | Users | Notifications | Audit | Reports | Settings | Integration |
|---------|------|---------|-----------|-------|---------------|-------|---------|----------|-------------|
| **Organizations** | - | Read | Trigger | Read | - | Log | - | Read | - |
| **Billing** | Read | - | Trigger | - | Trigger | Log | Provide | Read | - |
| **Approvals** | Read | Read | - | Create | Trigger | Log | - | Read | - |
| **Users** | Read | - | Trigger | - | Trigger | Log | - | Read | - |
| **Notifications** | - | - | - | - | - | - | - | Read | Use |
| **Audit** | - | - | - | - | - | - | Provide | - | - |
| **Reports** | Read | Read | Read | Read | - | Read | - | - | - |
| **Settings** | - | - | - | - | - | - | - | - | - |
| **Integration** | Read | - | - | - | Trigger | Log | - | Read | - |

**Legend:**
- **Read**: Queries data
- **Trigger**: Triggers actions
- **Create**: Creates entities
- **Provide**: Provides data
- **Use**: Uses services
- **Log**: Logs actions

---

## Module Dependencies

### Dependency Graph

```
                   [Organizations] (Core)
                         │
                         │ depends on
                         ↓
                    [Billing] ←──────┐
                         │           │
              ┌──────────┼──────────┐│
              ↓          ↓          ↓│
          [Users]   [Approvals]  [Settings]
              │          │          
              ↓          ↓          
         [Notifications] ←──────────┘
              │
              ↓
          [Audit] (Foundation)
              │
              ↓
          [Reports] (Consumer)
              │
              ↓
         [Integration] (External)
```

### Dependency Rules

1. **No Circular Dependencies**: Modules must not create circular dependencies
2. **Inward Dependencies**: Core modules should not depend on outer modules
3. **Interface Segregation**: Use interfaces for cross-module communication
4. **Event-Driven**: Prefer events over direct calls when possible
5. **Loose Coupling**: Minimize module interdependencies

---

## Conclusion

This modular architecture provides:

✅ **Scalability**: Add new modules without affecting existing ones  
✅ **Maintainability**: Clear boundaries and responsibilities  
✅ **Testability**: Each module tested independently  
✅ **Flexibility**: Modules can be enabled/disabled based on plan  
✅ **Team Collaboration**: Different teams work on different modules

**Implementation Status:**
- ✅ Billing Module: 100% Complete (88 tests passing)
- ✅ Approvals Module: 100% Complete (9 tests passing)
- 🚧 Organizations Module: 60% Complete
- 🚧 Users Module: 70% Complete
- 📋 5 Modules: Planned for future implementation

**Next Steps:**
1. Complete Organizations and Users modules
2. Implement Notifications module
3. Build Audit module
4. Create Reports module
5. Add Settings module
6. Develop Integration module
