<?php

namespace App\Modules\Users\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateProfileRequest extends FormRequest
{
    public function authorize(): bool { return true; }

    public function rules(): array
    {
        return [
            'name' => ['sometimes','string','max:255'],
            'first_name' => ['sometimes','string','max:255'],
            'last_name' => ['sometimes','string','max:255'],
            'phone' => ['sometimes','string','max:20'],
            'avatar' => ['sometimes','string','max:2048'],
            'bio' => ['sometimes','string'],
            'timezone' => ['sometimes','string','max:64'],
            'language' => ['sometimes','string','max:10'],
        ];
    }
}
