<?php

namespace App\Modules\Shared\Domain\Traits;

use Illuminate\Support\Facades\Log;

/**
 * HasAudit Trait
 * 
 * Provides automatic activity logging for models
 * Records create, update, and delete operations
 */
trait HasAudit
{
    /**
     * Boot the trait
     */
    protected static function bootHasAudit(): void
    {
        static::created(function ($model) {
            $model->logActivity('created', $model->toArray());
        });

        static::updated(function ($model) {
            $model->logActivity('updated', [
                'old' => $model->getOriginal(),
                'new' => $model->getChanges()
            ]);
        });

        static::deleted(function ($model) {
            $model->logActivity('deleted', $model->toArray());
        });
    }

    /**
     * Log activity for this model
     */
    protected function logActivity(string $action, array $data): void
    {
        Log::info('Model activity', [
            'model' => static::class,
            'action' => $action,
            'model_id' => $this->getKey(),
            'user_id' => auth()->id(),
            'organization_id' => $this->organization_id ?? auth()->user()?->organization_id,
            'data' => $data,
            'timestamp' => now()->toIso8601String()
        ]);
    }
}
