<?php

namespace Tests\Feature\Organizations;

use Tests\TestCase;
use App\Modules\Users\Domain\Models\User;
use App\Modules\Organizations\Domain\Models\Organization;
use Illuminate\Foundation\Testing\RefreshDatabase;

class OrganizationHierarchyTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Organization $org;

    protected function setUp(): void
    {
        parent::setUp();
        $this->org = Organization::factory()->create();
        $this->user = User::factory()->create(['organization_id' => $this->org->id]);
    }

    public function test_can_get_organization_hierarchy()
    {
        $response = $this->actingAs($this->user)->getJson("/api/v1/organizations/{$this->org->id}/hierarchy");
        $response->assertStatus(200);
    }

    public function test_can_get_organization_members()
    {
        $response = $this->actingAs($this->user)->getJson("/api/v1/organizations/{$this->org->id}/members");
        $response->assertStatus(200);
        // Response may be array or wrapped in data
        $this->assertTrue(is_array($response->json()) || is_array($response->json('data')));
    }

    public function test_hierarchy_returns_valid_structure()
    {
        $response = $this->actingAs($this->user)->getJson("/api/v1/organizations/{$this->org->id}/hierarchy");
        $response->assertStatus(200);
        $response->assertJsonStructure(['*' => ['id', 'name']]);
    }

    public function test_members_includes_user()
    {
        $response = $this->actingAs($this->user)->getJson("/api/v1/organizations/{$this->org->id}/members");
        $response->assertStatus(200);
    }
}
