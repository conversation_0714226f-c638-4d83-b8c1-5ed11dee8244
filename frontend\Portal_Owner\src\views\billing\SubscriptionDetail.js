import React, { useState, useEffect } from 'react'
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom'
import { CCard, CCardBody, CCardHeader, CCol, CRow, CForm, CFormLabel, CFormInput, CFormSelect, CButton, CSpinner, CAlert } from '@coreui/react'
import { billingAPI } from '../../api/billing'

const SubscriptionDetail = () => {
  const { id } = useParams()
  const navigate = useNavigate()
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [formData, setFormData] = useState({ plan_id: '', organization_id: '', status: 'active' })
  const [saving, setSaving] = useState(false)

  useEffect(() => {
    if (id && id !== 'new') {
      loadSubscription()
    } else {
      setLoading(false)
    }
  }, [id])

  const loadSubscription = async () => {
    try {
      setLoading(true)
      const response = await billingAPI.getSubscription(id)
      setFormData(response.data.data)
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to load subscription')
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    try {
      setSaving(true)
      if (id && id !== 'new') {
        await billingAPI.updateSubscription(id, formData)
      } else {
        await billingAPI.createSubscription(formData)
      }
      navigate('/subscriptions')
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to save subscription')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return <CRow><CCol xs={12} className="text-center"><CSpinner color="primary" /></CCol></CRow>
  }

  return (
    <CRow>
      <CCol xs={12} md={8}>
        <CCard>
          <CCardHeader>
            <strong>{id && id !== 'new' ? 'Edit Subscription' : 'Create Subscription'}</strong>
          </CCardHeader>
          <CCardBody>
            {error && <CAlert color="danger">{error}</CAlert>}
            <CForm onSubmit={handleSubmit}>
              <div className="mb-3">
                <CFormLabel htmlFor="organization_id">Organization</CFormLabel>
                <CFormInput id="organization_id" name="organization_id" value={formData.organization_id} onChange={handleChange} required />
              </div>
              <div className="mb-3">
                <CFormLabel htmlFor="plan_id">Plan</CFormLabel>
                <CFormInput id="plan_id" name="plan_id" value={formData.plan_id} onChange={handleChange} required />
              </div>
              <div className="mb-3">
                <CFormLabel htmlFor="status">Status</CFormLabel>
                <CFormSelect id="status" name="status" value={formData.status} onChange={handleChange}>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                  <option value="cancelled">Cancelled</option>
                </CFormSelect>
              </div>
              <div className="d-grid gap-2">
                <CButton type="submit" color="primary" disabled={saving}>
                  {saving ? <CSpinner size="sm" className="me-2" /> : null}
                  {id && id !== 'new' ? 'Update Subscription' : 'Create Subscription'}
                </CButton>
                <CButton type="button" color="secondary" onClick={() => navigate('/subscriptions')}>
                  Cancel
                </CButton>
              </div>
            </CForm>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  )
}

export default SubscriptionDetail
