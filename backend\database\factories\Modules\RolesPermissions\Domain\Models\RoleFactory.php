<?php

namespace Database\Factories\Modules\RolesPermissions\Domain\Models;

use App\Modules\RolesPermissions\Domain\Models\Role;
use App\Modules\Organizations\Domain\Models\Organization;
use Illuminate\Database\Eloquent\Factories\Factory;

class RoleFactory extends Factory
{
    protected $model = Role::class;

    public function definition(): array
    {
        $name = $this->faker->unique()->word();
        return [
            'id' => $this->faker->uuid(),
            'organization_id' => Organization::factory(),
            'name' => $name,
            'slug' => \Illuminate\Support\Str::slug($name),
            'description' => $this->faker->sentence(),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}
