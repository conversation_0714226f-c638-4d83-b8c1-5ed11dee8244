<?php

use Illuminate\Support\Facades\Route;

Route::middleware('auth')->group(function () {
    // Integration - Webhooks
    Route::get('webhooks', [\App\Modules\Integration\Http\Controllers\WebhookController::class, 'index']);
    Route::post('webhooks', [\App\Modules\Integration\Http\Controllers\WebhookController::class, 'store']);
    Route::get('webhooks/{id}', [\App\Modules\Integration\Http\Controllers\WebhookController::class, 'show']);
    Route::patch('webhooks/{id}', [\App\Modules\Integration\Http\Controllers\WebhookController::class, 'update']);
    Route::delete('webhooks/{id}', [\App\Modules\Integration\Http\Controllers\WebhookController::class, 'destroy']);
    Route::get('webhooks/{id}/events', [\App\Modules\Integration\Http\Controllers\WebhookController::class, 'events']);
});
