<?php

namespace App\Modules\Integration\Jobs;

use App\Modules\Integration\Domain\Models\WebhookEvent;
use App\Modules\Integration\Domain\Services\WebhookService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class RetryWebhookEventJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public string $eventId;

    public function __construct(string $eventId)
    {
        $this->eventId = $eventId;
    }

    public function handle(WebhookService $service): void
    {
        $event = WebhookEvent::find($this->eventId);
        if (!$event) {
            return;
        }
        $service->retryEvent($event);
    }
}
