<?php

use Illuminate\Support\Facades\Route;

Route::middleware('auth')->group(function () {
    // Organizations
    Route::get('organizations', [\App\Modules\Organizations\Http\Controllers\OrganizationController::class, 'index']);
    Route::post('organizations', [\App\Modules\Organizations\Http\Controllers\OrganizationController::class, 'store']);
    Route::get('organizations/{id}', [\App\Modules\Organizations\Http\Controllers\OrganizationController::class, 'show']);
    Route::patch('organizations/{id}', [\App\Modules\Organizations\Http\Controllers\OrganizationController::class, 'update']);
    Route::delete('organizations/{id}', [\App\Modules\Organizations\Http\Controllers\OrganizationController::class, 'destroy']);
    Route::get('organizations/{id}/hierarchy', [\App\Modules\Organizations\Http\Controllers\OrganizationController::class, 'hierarchy']);
    Route::get('organizations/{id}/members', [\App\Modules\Organizations\Http\Controllers\OrganizationController::class, 'members']);
});
