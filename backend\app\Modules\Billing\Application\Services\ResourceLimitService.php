<?php

namespace App\Modules\Billing\Application\Services;

use App\Modules\Organizations\Domain\Models\Organization;
use App\Modules\Billing\Domain\Models\Subscription;
use App\Modules\Billing\Domain\Models\SubscriptionAddOn;
use App\Modules\Approvals\Domain\Services\ApprovalService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ResourceLimitService
{
    public function __construct(
        protected ApprovalService $approvalService
    ) {}

    /**
     * Check if user creation is allowed for organization
     */
    public function canCreateUser(Organization $organization): array
    {
        $subscription = $organization->subscription;
        
        if (!$subscription || !$subscription->isActive()) {
            return [
                'allowed' => false,
                'reason' => 'no_active_subscription',
                'message' => 'No active subscription found for this organization.',
            ];
        }

        $plan = $subscription->plan;
        $userLimit = $plan->user_limit;
        
        // Check for unlimited
        if ($userLimit === 0) {
            return [
                'allowed' => true,
                'reason' => 'unlimited',
                'limit' => 0,
                'current' => $subscription->user_count,
            ];
        }

        // Check if adding user would exceed limit
        if ($subscription->user_count >= $userLimit) {
            return [
                'allowed' => false,
                'reason' => 'limit_exceeded',
                'message' => "User limit reached ({$userLimit} users). Please upgrade your plan or request additional users.",
                'limit' => $userLimit,
                'current' => $subscription->user_count,
                'requires_approval' => true,
            ];
        }

        return [
            'allowed' => true,
            'reason' => 'within_limit',
            'limit' => $userLimit,
            'current' => $subscription->user_count,
            'remaining' => $userLimit - $subscription->user_count,
        ];
    }

    /**
     * Check if sub-organization creation is allowed
     */
    public function canCreateSubOrganization(Organization $organization): array
    {
        $subscription = $organization->subscription;
        
        if (!$subscription || !$subscription->isActive()) {
            return [
                'allowed' => false,
                'reason' => 'no_active_subscription',
                'message' => 'No active subscription found for this organization.',
            ];
        }

        $plan = $subscription->plan;
        $subOrgLimit = $plan->sub_org_limit;
        
        // Check for unlimited
        if ($subOrgLimit === 0) {
            return [
                'allowed' => true,
                'reason' => 'unlimited',
                'limit' => 0,
                'current' => $subscription->sub_org_count,
            ];
        }

        // Check if adding sub-org would exceed limit
        if ($subscription->sub_org_count >= $subOrgLimit) {
            return [
                'allowed' => false,
                'reason' => 'limit_exceeded',
                'message' => "Sub-organization limit reached ({$subOrgLimit}). Please upgrade your plan or request additional sub-organizations.",
                'limit' => $subOrgLimit,
                'current' => $subscription->sub_org_count,
                'requires_approval' => true,
            ];
        }

        return [
            'allowed' => true,
            'reason' => 'within_limit',
            'limit' => $subOrgLimit,
            'current' => $subscription->sub_org_count,
            'remaining' => $subOrgLimit - $subscription->sub_org_count,
        ];
    }

    /**
     * Check if storage usage is allowed
     */
    public function canUseStorage(Organization $organization, int $additionalBytes): array
    {
        $subscription = $organization->subscription;
        
        if (!$subscription || !$subscription->isActive()) {
            return [
                'allowed' => false,
                'reason' => 'no_active_subscription',
                'message' => 'No active subscription found for this organization.',
            ];
        }

        $plan = $subscription->plan;
        $storageLimitBytes = $plan->storage_limit * 1024 * 1024 * 1024; // GB to bytes
        
        // Check for unlimited
        if ($plan->storage_limit === 0) {
            return [
                'allowed' => true,
                'reason' => 'unlimited',
                'limit' => 0,
                'current' => $subscription->storage_used,
            ];
        }

        $newTotal = $subscription->storage_used + $additionalBytes;

        // Check if adding storage would exceed limit
        if ($newTotal > $storageLimitBytes) {
            return [
                'allowed' => false,
                'reason' => 'limit_exceeded',
                'message' => "Storage limit would be exceeded. Current: " . $this->formatBytes($subscription->storage_used) . ", Limit: " . $this->formatBytes($storageLimitBytes),
                'limit' => $storageLimitBytes,
                'current' => $subscription->storage_used,
                'requested' => $additionalBytes,
                'requires_approval' => true,
            ];
        }

        return [
            'allowed' => true,
            'reason' => 'within_limit',
            'limit' => $storageLimitBytes,
            'current' => $subscription->storage_used,
            'after_addition' => $newTotal,
            'remaining' => $storageLimitBytes - $newTotal,
        ];
    }

    /**
     * Check if hierarchy depth is allowed
     */
    public function canCreateAtHierarchyDepth(Organization $organization, int $depth): array
    {
        $subscription = $organization->subscription;
        
        if (!$subscription || !$subscription->isActive()) {
            return [
                'allowed' => false,
                'reason' => 'no_active_subscription',
                'message' => 'No active subscription found for this organization.',
            ];
        }

        $plan = $subscription->plan;
        $hierarchyLimit = $plan->hierarchy_depth_limit;
        
        // Check for unlimited
        if ($hierarchyLimit === 0) {
            return [
                'allowed' => true,
                'reason' => 'unlimited',
                'limit' => 0,
                'current' => $subscription->hierarchy_depth,
            ];
        }

        // Check if depth would exceed limit
        if ($depth > $hierarchyLimit) {
            return [
                'allowed' => false,
                'reason' => 'limit_exceeded',
                'message' => "Hierarchy depth limit reached ({$hierarchyLimit} levels). Cannot create organization at level {$depth}.",
                'limit' => $hierarchyLimit,
                'current' => $subscription->hierarchy_depth,
                'requested' => $depth,
                'requires_approval' => true,
            ];
        }

        return [
            'allowed' => true,
            'reason' => 'within_limit',
            'limit' => $hierarchyLimit,
            'current' => $subscription->hierarchy_depth,
            'requested' => $depth,
        ];
    }

    /**
     * Check if module is accessible
     */
    public function canAccessModule(Organization $organization, string $moduleName): array
    {
        $subscription = $organization->subscription;
        
        if (!$subscription || !$subscription->isActive()) {
            return [
                'allowed' => false,
                'reason' => 'no_active_subscription',
                'message' => 'No active subscription found for this organization.',
            ];
        }

        $plan = $subscription->plan;
        
        if (!$plan->hasModule($moduleName)) {
            return [
                'allowed' => false,
                'reason' => 'module_not_included',
                'message' => "Module '{$moduleName}' is not included in your current plan.",
                'available_modules' => $plan->modules,
            ];
        }

        return [
            'allowed' => true,
            'reason' => 'module_included',
            'module' => $moduleName,
        ];
    }

    /**
     * Increment user count for subscription
     */
    public function incrementUserCount(Organization $organization): bool
    {
        $subscription = $organization->subscription;
        
        if (!$subscription) {
            Log::warning('Attempted to increment user count without subscription', [
                'organization_id' => $organization->id,
            ]);
            return false;
        }

        try {
            DB::beginTransaction();
            
            $subscription->increment('user_count');
            
            Log::info('User count incremented', [
                'organization_id' => $organization->id,
                'subscription_id' => $subscription->id,
                'new_count' => $subscription->user_count,
            ]);
            
            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to increment user count', [
                'organization_id' => $organization->id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Decrement user count for subscription
     */
    public function decrementUserCount(Organization $organization): bool
    {
        $subscription = $organization->subscription;
        
        if (!$subscription) {
            return false;
        }

        try {
            DB::beginTransaction();
            
            if ($subscription->user_count > 0) {
                $subscription->decrement('user_count');
                
                Log::info('User count decremented', [
                    'organization_id' => $organization->id,
                    'subscription_id' => $subscription->id,
                    'new_count' => $subscription->user_count,
                ]);
            }
            
            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to decrement user count', [
                'organization_id' => $organization->id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Increment sub-organization count
     */
    public function incrementSubOrgCount(Organization $organization): bool
    {
        $subscription = $organization->subscription;
        
        if (!$subscription) {
            return false;
        }

        try {
            DB::beginTransaction();
            
            $subscription->increment('sub_org_count');
            
            Log::info('Sub-organization count incremented', [
                'organization_id' => $organization->id,
                'subscription_id' => $subscription->id,
                'new_count' => $subscription->sub_org_count,
            ]);
            
            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to increment sub-org count', [
                'organization_id' => $organization->id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Decrement sub-organization count
     */
    public function decrementSubOrgCount(Organization $organization): bool
    {
        $subscription = $organization->subscription;
        
        if (!$subscription) {
            return false;
        }

        try {
            DB::beginTransaction();
            
            if ($subscription->sub_org_count > 0) {
                $subscription->decrement('sub_org_count');
                
                Log::info('Sub-organization count decremented', [
                    'organization_id' => $organization->id,
                    'subscription_id' => $subscription->id,
                    'new_count' => $subscription->sub_org_count,
                ]);
            }
            
            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to decrement sub-org count', [
                'organization_id' => $organization->id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Update storage usage
     */
    public function updateStorageUsage(Organization $organization, int $bytes): bool
    {
        $subscription = $organization->subscription;
        
        if (!$subscription) {
            return false;
        }

        try {
            DB::beginTransaction();
            
            $newTotal = max(0, $subscription->storage_used + $bytes);
            $subscription->update(['storage_used' => $newTotal]);
            
            Log::info('Storage usage updated', [
                'organization_id' => $organization->id,
                'subscription_id' => $subscription->id,
                'change' => $bytes,
                'new_total' => $newTotal,
            ]);
            
            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update storage usage', [
                'organization_id' => $organization->id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Update hierarchy depth
     */
    public function updateHierarchyDepth(Organization $organization, int $depth): bool
    {
        $subscription = $organization->subscription;
        
        if (!$subscription) {
            return false;
        }

        try {
            DB::beginTransaction();
            
            if ($depth > $subscription->hierarchy_depth) {
                $subscription->update(['hierarchy_depth' => $depth]);
                
                Log::info('Hierarchy depth updated', [
                    'organization_id' => $organization->id,
                    'subscription_id' => $subscription->id,
                    'new_depth' => $depth,
                ]);
            }
            
            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update hierarchy depth', [
                'organization_id' => $organization->id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Get current usage summary for organization
     */
    public function getUsageSummary(Organization $organization): array
    {
        $subscription = $organization->subscription;
        
        if (!$subscription || !$subscription->isActive()) {
            return [
                'has_subscription' => false,
                'message' => 'No active subscription found.',
            ];
        }

        $plan = $subscription->plan;

        return [
            'has_subscription' => true,
            'plan' => [
                'name' => $plan->name,
                'slug' => $plan->slug,
            ],
            'users' => [
                'current' => $subscription->user_count,
                'limit' => $plan->user_limit,
                'unlimited' => $plan->user_limit === 0,
                'percentage' => $subscription->getUserUsagePercentage(),
                'remaining' => $plan->user_limit > 0 ? $plan->user_limit - $subscription->user_count : null,
            ],
            'sub_organizations' => [
                'current' => $subscription->sub_org_count,
                'limit' => $plan->sub_org_limit,
                'unlimited' => $plan->sub_org_limit === 0,
                'percentage' => $subscription->getSubOrgUsagePercentage(),
                'remaining' => $plan->sub_org_limit > 0 ? $plan->sub_org_limit - $subscription->sub_org_count : null,
            ],
            'storage' => [
                'current' => $subscription->storage_used,
                'current_formatted' => $this->formatBytes($subscription->storage_used),
                'limit' => $plan->storage_limit * 1024 * 1024 * 1024,
                'limit_formatted' => $plan->storage_limit . ' GB',
                'unlimited' => $plan->storage_limit === 0,
                'percentage' => $subscription->getStorageUsagePercentage(),
            ],
            'hierarchy' => [
                'current' => $subscription->hierarchy_depth,
                'limit' => $plan->hierarchy_depth_limit,
                'unlimited' => $plan->hierarchy_depth_limit === 0,
            ],
            'modules' => $plan->modules,
        ];
    }

    /**
     * Format bytes to human-readable format
     */
    protected function formatBytes(int $bytes, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
