import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { CCard, CCardBody, CCardHeader, CCol, CRow, CButton, CTable, CTableBody, CTableDataCell, CTableHead, CTableHeaderCell, CTableRow, CSpinner, CAlert, CBadge } from '@coreui/react'
import CIcon from '@coreui/icons-react'
import { cilPlus, cilPencil, cilTrash, cilWarning } from '@coreui/icons'
import { webhooksAPI } from '../../api/webhooks'

const Webhooks = () => {
  const navigate = useNavigate()
  const [webhooks, setWebhooks] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => { loadWebhooks() }, [])

  const loadWebhooks = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await webhooksAPI.getWebhooks()
      // Backend returns array directly from JsonResource::collection()
      setWebhooks(response.data || [])
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to load webhooks')
      console.error('Error:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async (id) => {
    if (window.confirm('Are you sure?')) {
      try {
        await webhooksAPI.deleteWebhook(id)
        setWebhooks(webhooks.filter((w) => w.id !== id))
      } catch (err) {
        setError(err.response?.data?.message || 'Failed to delete')
      }
    }
  }

  return (
    <CRow>
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardHeader>
            <div className="d-flex justify-content-between align-items-center">
              <strong>Webhooks</strong>
              <CButton color="primary" size="sm" onClick={() => navigate('/webhooks/new')}>
                <CIcon icon={cilPlus} className="me-2" />
                Add Webhook
              </CButton>
            </div>
          </CCardHeader>
          <CCardBody>
            {error && <CAlert color="danger">{error}</CAlert>}
            {loading ? (
              <div className="text-center"><CSpinner color="primary" /></div>
            ) : (
              <CTable hover responsive>
                <CTableHead>
                  <CTableRow>
                    <CTableHeaderCell scope="col">#</CTableHeaderCell>
                    <CTableHeaderCell scope="col">URL</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Events</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Status</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Actions</CTableHeaderCell>
                  </CTableRow>
                </CTableHead>
                <CTableBody>
                  {webhooks.length === 0 ? (
                    <CTableRow>
                      <CTableDataCell colSpan="5" className="text-center text-muted py-5">
                        <CIcon icon={cilWarning} size="xl" className="mb-2" />
                        <div>No webhooks configured</div>
                        <small className="text-muted">Add a webhook to get started</small>
                      </CTableDataCell>
                    </CTableRow>
                  ) : (
                    webhooks.map((webhook, idx) => (
                      <CTableRow key={webhook.id}>
                        <CTableDataCell>{idx + 1}</CTableDataCell>
                        <CTableDataCell>{webhook.url || '-'}</CTableDataCell>
                        <CTableDataCell>{webhook.events_count || 0}</CTableDataCell>
                        <CTableDataCell><CBadge color={webhook.active ? 'success' : 'secondary'}>{webhook.active ? 'Active' : 'Inactive'}</CBadge></CTableDataCell>
                        <CTableDataCell>
                          <CButton color="info" size="sm" className="me-2" onClick={() => navigate(`/webhooks/${webhook.id}`)}><CIcon icon={cilPencil} /></CButton>
                          <CButton color="danger" size="sm" onClick={() => handleDelete(webhook.id)}><CIcon icon={cilTrash} /></CButton>
                        </CTableDataCell>
                      </CTableRow>
                    ))
                  )}
                </CTableBody>
              </CTable>
            )}
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  )
}

export default Webhooks
