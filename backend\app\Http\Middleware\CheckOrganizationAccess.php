<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckOrganizationAccess
{
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();
        if (!$user) {
            return response()->json(['message' => 'Unauthenticated'], 401);
        }

        $orgId = $request->input('organization_id') ?? $request->route('organization_id');
        if ($orgId && $orgId !== $user->organization_id) {
            return response()->json(['message' => 'Forbidden: invalid organization context'], 403);
        }

        return $next($request);
    }
}
