import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { <PERSON>ard, CCardBody, CCardHeader, CCol, CRow, CButton, CTable, CTableBody, CTableDataCell, CTableHead, CTableHeaderCell, CTableRow, CSpinner, CAlert, CBadge } from '@coreui/react'
import CIcon from '@coreui/icons-react'
import { cilPencil, cilTrash } from '@coreui/icons'
import { notificationsAPI } from '../../api/notifications'

const Notifications = () => {
  const navigate = useNavigate()
  const [notifications, setNotifications] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => { loadNotifications() }, [])

  const loadNotifications = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await notificationsAPI.getNotifications()
      setNotifications(response.data.data || [])
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to load notifications')
      console.error('Error:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async (id) => {
    if (window.confirm('Are you sure?')) {
      try {
        await notificationsAPI.markAsRead(id)
        setNotifications(notifications.filter((n) => n.id !== id))
      } catch (err) {
        setError(err.response?.data?.message || 'Failed to delete')
      }
    }
  }

  return (
    <CRow>
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardHeader>
            <strong>Notifications</strong>
          </CCardHeader>
          <CCardBody>
            {error && <CAlert color="danger">{error}</CAlert>}
            {loading ? (
              <div className="text-center"><CSpinner color="primary" /></div>
            ) : (
              <CTable hover responsive>
                <CTableHead>
                  <CTableRow>
                    <CTableHeaderCell scope="col">#</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Message</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Type</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Status</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Date</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Actions</CTableHeaderCell>
                  </CTableRow>
                </CTableHead>
                <CTableBody>
                  {notifications.length === 0 ? (
                    <CTableRow>
                      <CTableDataCell colSpan="6" className="text-center text-muted">
                        No notifications found.
                      </CTableDataCell>
                    </CTableRow>
                  ) : (
                    notifications.map((notif, idx) => (
                      <CTableRow key={notif.id}>
                        <CTableDataCell>{idx + 1}</CTableDataCell>
                        <CTableDataCell>{notif.message || '-'}</CTableDataCell>
                        <CTableDataCell>{notif.type || '-'}</CTableDataCell>
                        <CTableDataCell><CBadge color={notif.read_at ? 'secondary' : 'primary'}>{notif.read_at ? 'Read' : 'Unread'}</CBadge></CTableDataCell>
                        <CTableDataCell>{notif.created_at || '-'}</CTableDataCell>
                        <CTableDataCell>
                          <CButton color="info" size="sm" className="me-2" onClick={() => navigate(`/notifications/${notif.id}`)}><CIcon icon={cilPencil} /></CButton>
                          <CButton color="danger" size="sm" onClick={() => handleDelete(notif.id)}><CIcon icon={cilTrash} /></CButton>
                        </CTableDataCell>
                      </CTableRow>
                    ))
                  )}
                </CTableBody>
              </CTable>
            )}
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  )
}

export default Notifications
