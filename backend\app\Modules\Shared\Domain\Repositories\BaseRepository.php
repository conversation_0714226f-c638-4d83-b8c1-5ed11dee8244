<?php

namespace App\Modules\Shared\Domain\Repositories;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use App\Modules\Shared\Domain\Exceptions\NotFoundException;

/**
 * Base Repository Class
 * 
 * Provides common data access patterns with automatic organization scoping.
 * All queries are automatically filtered by organization_id (Rule 42)
 */
abstract class BaseRepository
{
    /**
     * Model class name
     *
     * @var string
     */
    protected string $model;

    /**
     * Get paginated results with organization scoping
     *
     * @param int $perPage Items per page
     * @param array $filters Additional filters
     * @param array $sort Sorting options
     * @return LengthAwarePaginator
     */
    public function paginate(int $perPage = 15, array $filters = [], array $sort = []): LengthAwarePaginator
    {
        $query = $this->getBaseQuery();
        
        $query = $this->applyFilters($query, $filters);
        $query = $this->applySorting($query, $sort);
        
        return $query->paginate($perPage);
    }

    /**
     * Get all records with organization scoping
     *
     * @param array $filters Additional filters
     * @return Collection
     */
    public function all(array $filters = []): Collection
    {
        $query = $this->getBaseQuery();
        $query = $this->applyFilters($query, $filters);
        
        return $query->get();
    }

    /**
     * Find record by ID with organization scoping
     *
     * @param string $id Record ID
     * @return Model|null
     */
    public function findById(string $id): ?Model
    {
        return $this->getBaseQuery()->find($id);
    }

    /**
     * Find record by ID or fail with exception
     *
     * @param string $id Record ID
     * @return Model
     * @throws NotFoundException
     */
    public function findByIdOrFail(string $id): Model
    {
        $model = $this->findById($id);
        
        if (!$model) {
            throw new NotFoundException("Record not found with ID: {$id}");
        }
        
        return $model;
    }

    /**
     * Create new record
     *
     * @param array $data Record data
     * @return Model
     */
    public function create(array $data): Model
    {
        // Automatically add organization_id if not present
        if (!isset($data['organization_id'])) {
            $data['organization_id'] = auth()->user()?->organization_id;
        }
        
        return $this->getModelInstance()::create($data);
    }

    /**
     * Update existing record
     *
     * @param Model $model Model instance
     * @param array $data Update data
     * @return Model
     */
    public function update(Model $model, array $data): Model
    {
        $model->update($data);
        return $model->fresh();
    }

    /**
     * Soft delete record
     *
     * @param Model $model Model instance
     * @return bool
     */
    public function delete(Model $model): bool
    {
        return $model->delete();
    }

    /**
     * Count records with organization scoping
     *
     * @param array $filters Additional filters
     * @return int
     */
    public function count(array $filters = []): int
    {
        $query = $this->getBaseQuery();
        $query = $this->applyFilters($query, $filters);
        
        return $query->count();
    }

    /**
     * Check if record exists
     *
     * @param string $id Record ID
     * @return bool
     */
    public function exists(string $id): bool
    {
        return $this->getBaseQuery()->where('id', $id)->exists();
    }

    /**
     * Apply filters to query
     *
     * @param Builder $query Query builder
     * @param array $filters Filters to apply
     * @return Builder
     */
    protected function applyFilters(Builder $query, array $filters): Builder
    {
        foreach ($filters as $field => $value) {
            if (is_array($value)) {
                $query->whereIn($field, $value);
            } else {
                $query->where($field, $value);
            }
        }
        
        return $query;
    }

    /**
     * Apply sorting to query
     *
     * @param Builder $query Query builder
     * @param array $sort Sorting options ['field' => 'direction']
     * @return Builder
     */
    protected function applySorting(Builder $query, array $sort): Builder
    {
        foreach ($sort as $field => $direction) {
            $query->orderBy($field, $direction);
        }
        
        return $query;
    }

    /**
     * Apply search to query
     *
     * @param Builder $query Query builder
     * @param string $search Search term
     * @param array $fields Fields to search in
     * @return Builder
     */
    protected function applySearch(Builder $query, string $search, array $fields): Builder
    {
        return $query->where(function ($q) use ($search, $fields) {
            foreach ($fields as $field) {
                $q->orWhere($field, 'LIKE', "%{$search}%");
            }
        });
    }

    /**
     * Get base query with organization scoping
     *
     * @return Builder
     */
    protected function getBaseQuery(): Builder
    {
        $query = $this->getModelInstance()::query();
        
        // Automatically scope to current user's organization
        $organizationId = auth()->user()?->organization_id;
        if ($organizationId) {
            $query->where('organization_id', $organizationId);
        }
        
        return $query;
    }

    /**
     * Expose a base query for advanced filtering in services.
     */
    public function query(): Builder
    {
        return $this->getBaseQuery();
    }

    /**
     * Get model instance
     *
     * @return Model
     */
    protected function getModelInstance(): Model
    {
        return new $this->model();
    }
}
