<?php

namespace App\Modules\Approvals\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Approvals\Domain\Services\ApprovalService;
use App\Modules\Approvals\Domain\Models\ApprovalRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ApprovalController extends Controller
{
    public function __construct(private readonly ApprovalService $approvalService)
    {
    }

    /**
     * List all approval requests
     */
    public function index(Request $request)
    {
        try {
            $filters = [
                'organization_id' => auth()->user()->organization_id,
                'status' => $request->input('status'),
            ];

            $approvals = $this->approvalService->listApprovals(array_filter($filters));

            return response()->json([
                'data' => $approvals,
                'count' => $approvals->count(),
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Create new approval request
     */
    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
                'type' => 'required|string',
                'description' => 'nullable|string',
                'amount' => 'nullable|numeric',
                'reference_type' => 'nullable|string',
                'reference_id' => 'nullable|string',
                'steps' => 'required|array',
                'steps.*.approver_id' => 'nullable|string',
                'steps.*.approver_role' => 'nullable|string',
            ]);

            $validated['organization_id'] = auth()->user()->organization_id;
            $validated['requester_id'] = auth()->id();

            $approval = $this->approvalService->createApprovalRequest($validated);

            return response()->json($approval, 201);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json(['errors' => $e->errors()], 422);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Get approval request details
     */
    public function show(string $id)
    {
        try {
            $approval = $this->approvalService->getApprovalById($id);

            if (!$approval || $approval->organization_id !== auth()->user()->organization_id) {
                return response()->json(['error' => 'Approval not found'], 404);
            }

            return response()->json($approval);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Approve a request
     */
    public function approve(string $id, Request $request)
    {
        try {
            $approval = $this->approvalService->getApprovalById($id);

            if (!$approval || $approval->organization_id !== auth()->user()->organization_id) {
                return response()->json(['error' => 'Approval not found'], 404);
            }

            $comment = $request->input('comment');
            $approved = $this->approvalService->approveRequest($approval, auth()->user(), $comment);

            return response()->json($approved);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Reject a request
     */
    public function reject(string $id, Request $request)
    {
        try {
            $validated = $request->validate([
                'reason' => 'required|string',
            ]);

            $approval = $this->approvalService->getApprovalById($id);

            if (!$approval || $approval->organization_id !== auth()->user()->organization_id) {
                return response()->json(['error' => 'Approval not found'], 404);
            }

            $rejected = $this->approvalService->rejectRequest($approval, auth()->user(), $validated['reason']);

            return response()->json($rejected);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json(['errors' => $e->errors()], 422);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Add comment to approval
     */
    public function addComment(string $id, Request $request)
    {
        try {
            $validated = $request->validate([
                'comment' => 'required|string',
            ]);

            $approval = $this->approvalService->getApprovalById($id);

            if (!$approval || $approval->organization_id !== auth()->user()->organization_id) {
                return response()->json(['error' => 'Approval not found'], 404);
            }

            $comment = $this->approvalService->addComment($approval, auth()->user(), $validated['comment']);

            return response()->json($comment, 201);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json(['errors' => $e->errors()], 422);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }
}
