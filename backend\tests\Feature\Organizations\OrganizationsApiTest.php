<?php

namespace Tests\Feature\Organizations;

use Tests\TestCase;
use Tests\Support\CreatesOrgUser;
use Illuminate\Foundation\Testing\RefreshDatabase;

class OrganizationsApiTest extends TestCase
{
    use RefreshDatabase, CreatesOrgUser;

    public function test_organizations_crud(): void
    {
        $org = $this->createOrganization();
        $user = $this->createUserInOrg($org);
        $this->actingAs($user);

        // Index
        $this->getJson('/api/v1/organizations')->assertStatus(200);
        // Store
        $id = $this->postJson('/api/v1/organizations', [
            'name' => 'Child Org',
            'code' => 'CHILD'.rand(100,999),
            'type' => 'branch',
            'status' => 'active',
        ])->assertStatus(201)->json('id');
        // Show
        $this->getJson('/api/v1/organizations/'.$id)->assertStatus(200);
        // Update
        $this->patchJson('/api/v1/organizations/'.$id, ['name' => 'Child Org Updated'])->assertStatus(200);
        // Destroy
        $this->deleteJson('/api/v1/organizations/'.$id)->assertStatus(200)->assertJsonFragment(['deleted' => true]);
    }
}
