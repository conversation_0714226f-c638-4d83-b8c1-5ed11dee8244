<?php

namespace App\Modules\Approvals\Domain\Services;

use App\Modules\Shared\Domain\Services\BaseService;
use App\Modules\Approvals\Domain\Models\ApprovalRequest;
use App\Modules\Approvals\Domain\Models\ApprovalStep;
use App\Modules\Approvals\Domain\Models\ApprovalComment;
use App\Modules\Approvals\Domain\Repositories\ApprovalRepository;
use App\Modules\Users\Domain\Models\User;
use App\Modules\Billing\Domain\Models\Subscription;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class ApprovalService extends BaseService
{
    public function __construct(private readonly ApprovalRepository $approvals)
    {
    }

    /**
     * Create a new approval request
     */
    public function createApprovalRequest(array $data): ApprovalRequest
    {
        return DB::transaction(function () use ($data) {
            $approval = $this->approvals->create([
                'organization_id' => $data['organization_id'] ?? $this->getCurrentOrganizationId(),
                'requester_id' => $data['requester_id'] ?? auth()->id(),
                'type' => $data['type'],
                'reference_type' => $data['reference_type'] ?? null,
                'reference_id' => $data['reference_id'] ?? null,
                'status' => 'pending',
                'description' => $data['description'] ?? null,
                'amount' => $data['amount'] ?? null,
                'data' => $data['data'] ?? [],
                'current_step' => 1,
                'submitted_at' => now(),
            ]);

            // Create approval steps
            if (!empty($data['steps'])) {
                foreach ($data['steps'] as $index => $step) {
                    ApprovalStep::create([
                        'organization_id' => $approval->organization_id,
                        'approval_request_id' => $approval->id,
                        'step_number' => $index + 1,
                        'approver_id' => $step['approver_id'] ?? null,
                        'approver_role' => $step['approver_role'] ?? null,
                        'status' => 'pending',
                        'required_count' => $step['required_count'] ?? 1,
                        'approval_type' => $step['approval_type'] ?? 'sequential',
                    ]);
                }
            }

            $this->logAction('Approval request created', [
                'approval_id' => $approval->id,
                'type' => $approval->type,
            ]);

            return $approval;
        });
    }

    /**
     * Approve an approval request
     */
    public function approveRequest(ApprovalRequest $approval, User $approver, ?string $comment = null): ApprovalRequest
    {
        return DB::transaction(function () use ($approval, $approver, $comment) {
            // Check resource limits before final approval for registrations
            if ($approval->type === 'registration') {
                $this->checkResourceLimitsForRegistration($approval);
            }

            $currentStep = $approval->steps()->where('step_number', $approval->current_step)->first();

            if (!$currentStep) {
                // Fallback to first pending step if current_step mismatch
                $currentStep = $approval->steps()->where('status', 'pending')->orderBy('step_number')->first();
                if (!$currentStep) {
                    throw new \Exception('Current approval step not found');
                }
                $approval->update(['current_step' => $currentStep->step_number]);
            }

            // Mark step as approved
            $currentStep->update([
                'status' => 'approved',
                'approved_by' => $approver->id,
                'approved_at' => now(),
            ]);

            // Add comment if provided
            if ($comment) {
                $this->addComment($approval, $approver, $comment, 'approval');
            }

            // Check if all steps are approved
            $pendingSteps = $approval->steps()->where('status', 'pending')->count();

            if ($pendingSteps === 0) {
                $approval->update([
                    'status' => 'approved',
                    'completed_at' => now(),
                ]);
            } else {
                // Move to next step
                $approval->update(['current_step' => $approval->current_step + 1]);
            }

            $this->logAction('Approval request approved', [
                'approval_id' => $approval->id,
                'approver_id' => $approver->id,
            ]);

            return $approval->fresh();
        });
    }

    /**
     * Reject an approval request
     */
    public function rejectRequest(ApprovalRequest $approval, User $rejector, string $reason): ApprovalRequest
    {
        return DB::transaction(function () use ($approval, $rejector, $reason) {
            $currentStep = $approval->steps()->where('step_number', $approval->current_step)->first();

            if ($currentStep) {
                $currentStep->update([
                    'status' => 'rejected',
                    'rejected_by' => $rejector->id,
                    'rejected_at' => now(),
                ]);
            }

            $approval->update([
                'status' => 'rejected',
                'completed_at' => now(),
            ]);

            $this->addComment($approval, $rejector, $reason, 'rejection');

            $this->logAction('Approval request rejected', [
                'approval_id' => $approval->id,
                'rejector_id' => $rejector->id,
            ]);

            return $approval->fresh();
        });
    }

    /**
     * Get approval by ID
     */
    public function getApprovalById(string $id): ?ApprovalRequest
    {
        return $this->approvals->findById($id);
    }

    /**
     * List all approvals
     */
    public function listApprovals(array $filters = []): Collection
    {
        return $this->approvals->all($filters);
    }

    /**
     * Add comment to approval
     */
    public function addComment(ApprovalRequest $approval, User $user, string $comment, string $type = 'comment'): ApprovalComment
    {
        return ApprovalComment::create([
            'organization_id' => $approval->organization_id,
            'approval_request_id' => $approval->id,
            'user_id' => $user->id,
            'comment' => $comment,
            'type' => $type,
            'created_at' => now(),
        ]);
    }

    /**
     * Escalate approval request
     */
    public function escalateRequest(ApprovalRequest $approval, User $escalatedBy, string $reason): ApprovalRequest
    {
        return DB::transaction(function () use ($approval, $escalatedBy, $reason) {
            $approval->update(['status' => 'escalated']);

            $this->addComment($approval, $escalatedBy, $reason, 'escalation');

            $this->logAction('Approval request escalated', [
                'approval_id' => $approval->id,
                'escalated_by' => $escalatedBy->id,
            ]);

            return $approval->fresh();
        });
    }

    /**
     * Get approvals by user (as approver)
     */
    public function getApprovalsByUser(User $user, array $filters = []): Collection
    {
        $query = ApprovalStep::query()
            ->where('organization_id', $user->organization_id)
            ->where('approver_id', $user->id)
            ->with('approvalRequest');

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        return $query->get()->map(fn($step) => $step->approvalRequest);
    }

    /**
     * Get pending approvals for user
     */
    public function getPendingApprovals(User $user): Collection
    {
        return $this->getApprovalsByUser($user, ['status' => 'pending']);
    }

    /**
     * Check resource limits before approving registration
     * 
     * @throws \Exception if resource limits are exceeded
     */
    private function checkResourceLimitsForRegistration(ApprovalRequest $approval): void
    {
        // Get the tenant organization from the approval data
        $registrationType = $approval->data['registration_type'] ?? null;
        $tenantId = $approval->data['tenant_organization_id'] ?? $approval->organization_id;

        // Get active subscription for this tenant
        $subscription = Subscription::where('organization_id', $tenantId)
            ->where('status', 'active')
            ->with('plan')
            ->first();

        if (!$subscription) {
            throw new \Exception('No active subscription found for this organization');
        }

        $plan = $subscription->plan;

        // Check limits based on registration type
        // Note: 0 = unlimited in plan limits
        if ($registrationType === 'user') {
            $currentUserCount = $subscription->user_count ?? 0;
            
            if ($plan->user_limit > 0 && $currentUserCount >= $plan->user_limit) {
                throw new \Exception(
                    "User limit reached. Current: {$currentUserCount}, Limit: {$plan->user_limit}. "
                    . "Please upgrade your plan to add more users."
                );
            }
        } elseif ($registrationType === 'sub_organization') {
            $currentSubOrgCount = $subscription->sub_org_count ?? 0;
            
            if ($plan->sub_org_limit > 0 && $currentSubOrgCount >= $plan->sub_org_limit) {
                throw new \Exception(
                    "Sub-organization limit reached. Current: {$currentSubOrgCount}, Limit: {$plan->sub_org_limit}. "
                    . "Please upgrade your plan to add more sub-organizations."
                );
            }
        }
    }
}
