<?php

namespace App\Modules\RolesPermissions\Domain\Services;

use App\Modules\Shared\Domain\Services\BaseService;
use App\Modules\RolesPermissions\Domain\Models\Role;
use App\Modules\RolesPermissions\Domain\Models\Permission;
use App\Modules\RolesPermissions\Domain\Models\UserPermission;
use App\Modules\RolesPermissions\Domain\Repositories\RoleRepository;
use App\Modules\RolesPermissions\Domain\Repositories\PermissionRepository;
use App\Modules\Users\Domain\Models\User;
use Illuminate\Database\Eloquent\Collection;

class RoleService extends BaseService
{
    public function __construct(
        private readonly RoleRepository $roles,
        private readonly PermissionRepository $permissions
    ) {
    }

    public function createRole(array $data): Role
    {
        return $this->roles->create($data);
    }

    public function updateRole(Role $role, array $data): Role
    {
        return $this->roles->update($role, $data);
    }

    public function deleteRole(Role $role): bool
    {
        return $this->roles->delete($role);
    }

    public function getRoleById(string $id): ?Role
    {
        return $this->roles->findById($id);
    }

    public function listRoles(array $filters = []): Collection
    {
        return $this->roles->all($filters);
    }

    public function attachPermission(Role $role, Permission $permission): void
    {
        $role->permissions()->syncWithoutDetaching([
            $permission->id => ['organization_id' => $role->organization_id]
        ]);
    }

    public function detachPermission(Role $role, Permission $permission): void
    {
        $role->permissions()->detach($permission->id);
    }

    public function getRolePermissions(Role $role): Collection
    {
        return $role->permissions()->get();
    }

    public function grantToUser(User $user, Permission $permission): UserPermission
    {
        return UserPermission::create([
            'organization_id' => $user->organization_id,
            'user_id' => $user->id,
            'permission_id' => $permission->id,
            'granted_by' => auth()->id(),
            'granted_at' => now(),
        ]);
    }
}
