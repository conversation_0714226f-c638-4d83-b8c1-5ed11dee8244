<?php

namespace App\Modules\Shared\Domain\Services;

use Closure;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use App\Modules\Shared\Domain\Exceptions\UnauthorizedException;

/**
 * Base Service Class
 * 
 * Provides reusable patterns for all services:
 * - Transaction handling with retry logic
 * - Structured logging with correlation ID
 * - Organization access validation
 * - Caching helpers
 * - Event emission
 * 
 * All services MUST extend this class (Rule 41)
 */
abstract class BaseService
{
    /**
     * Execute operation within database transaction with retry logic
     *
     * @param Closure $callback Operation to execute
     * @param int $maxAttempts Maximum retry attempts
     * @return mixed
     * @throws Exception
     */
    protected function executeInTransaction(Closure $callback, int $maxAttempts = 3)
    {
        $startTime = microtime(true);
        
        try {
            $result = DB::transaction($callback, $maxAttempts);
            
            $duration = microtime(true) - $startTime;
            $this->recordMetric('service.transaction', $duration);
            
            return $result;
        } catch (Exception $e) {
            $this->logError('Transaction failed', [
                'exception' => get_class($e),
                'message' => $e->getMessage(),
                'attempts' => $maxAttempts
            ]);
            throw $e;
        }
    }

    /**
     * Log action with correlation ID and context
     *
     * @param string $action Action description
     * @param array $context Additional context data
     * @param string $level Log level (info, warning, error)
     * @return void
     */
    protected function logAction(string $action, array $context = [], string $level = 'info'): void
    {
        $logContext = array_merge([
            'action' => $action,
            'correlation_id' => $this->getCorrelationId(),
            'user_id' => auth()->id(),
            'organization_id' => $this->getCurrentOrganizationId(),
            'timestamp' => now()->toIso8601String(),
            'service' => static::class,
        ], $context);

        Log::$level("Service action: {$action}", $logContext);
    }

    /**
     * Log error with context
     *
     * @param string $message Error message
     * @param array $context Error context
     * @return void
     */
    protected function logError(string $message, array $context = []): void
    {
        $this->logAction($message, $context, 'error');
    }

    /**
     * Log warning with context
     *
     * @param string $message Warning message
     * @param array $context Warning context
     * @return void
     */
    protected function logWarning(string $message, array $context = []): void
    {
        $this->logAction($message, $context, 'warning');
    }

    /**
     * Handle exception and log it
     *
     * @param Exception $e Exception to handle
     * @return void
     * @throws Exception
     */
    protected function handleException(Exception $e): void
    {
        $this->logError('Exception occurred', [
            'exception' => get_class($e),
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]);

        throw $e;
    }

    /**
     * Validate organization access for given organization ID
     *
     * @param string $orgId Organization ID to validate
     * @return void
     * @throws UnauthorizedException
     */
    protected function validateOrganizationAccess(string $orgId): void
    {
        $currentOrgId = $this->getCurrentOrganizationId();
        
        if ($currentOrgId !== $orgId) {
            $this->logWarning('Unauthorized organization access attempt', [
                'attempted_org' => $orgId,
                'user_org' => $currentOrgId
            ]);
            
            throw new UnauthorizedException('Access denied: You do not have permission to access this organization.');
        }
    }

    /**
     * Get current user's organization ID
     *
     * @return string|null
     */
    protected function getCurrentOrganizationId(): ?string
    {
        return auth()->user()?->organization_id;
    }

    /**
     * Check if current user is Portal Owner
     *
     * @return bool
     */
    protected function isPortalOwner(): bool
    {
        return auth()->user()?->isPortalOwner() ?? false;
    }

    /**
     * Determine if organization filter should be applied
     *
     * @return bool
     */
    protected function shouldApplyOrganizationFilter(): bool
    {
        return !$this->isPortalOwner();
    }

    /**
     * Get current authenticated user
     *
     * @return mixed
     */
    protected function getCurrentUser()
    {
        return auth()->user();
    }

    /**
     * Cache result of callback with given key
     *
     * @param string $key Cache key
     * @param Closure $callback Callback to execute if cache miss
     * @param int $ttl Time to live in seconds
     * @return mixed
     */
    protected function remember(string $key, Closure $callback, int $ttl = 3600)
    {
        $cacheKey = $this->buildCacheKey($key);
        
        return Cache::remember($cacheKey, $ttl, function () use ($callback) {
            $startTime = microtime(true);
            $result = $callback();
            $duration = microtime(true) - $startTime;
            
            $this->recordMetric('cache.miss', $duration);
            
            return $result;
        });
    }

    /**
     * Forget cache by pattern
     *
     * @param string $pattern Cache key pattern
     * @return void
     */
    protected function forgetCache(string $pattern): void
    {
        $cacheKey = $this->buildCacheKey($pattern);
        Cache::forget($cacheKey);
        
        $this->logAction('Cache invalidated', ['pattern' => $pattern]);
    }

    /**
     * Build cache key with organization context
     *
     * @param string $key Base cache key
     * @return string
     */
    private function buildCacheKey(string $key): string
    {
        $orgId = $this->getCurrentOrganizationId();
        return "org_{$orgId}_{$key}";
    }

    /**
     * Dispatch job asynchronously
     *
     * @param mixed $job Job instance
     * @return void
     */
    protected function dispatch($job): void
    {
        dispatch($job);
        
        $this->logAction('Job dispatched', [
            'job' => get_class($job)
        ]);
    }

    /**
     * Emit domain event
     *
     * @param object $event Event instance
     * @return void
     */
    protected function emit(object $event): void
    {
        event($event);
        
        $this->logAction('Event emitted', [
            'event' => get_class($event)
        ]);
    }

    /**
     * Get correlation ID from request
     *
     * @return string
     */
    private function getCorrelationId(): string
    {
        return request()->header('X-Correlation-ID', uniqid('corr_', true));
    }

    /**
     * Record performance metric
     *
     * @param string $metric Metric name
     * @param float $value Metric value
     * @return void
     */
    private function recordMetric(string $metric, float $value): void
    {
        // In production, send to monitoring service (e.g., Prometheus, DataDog)
        // For now, just log it
        if (config('app.debug')) {
            Log::debug("Metric: {$metric}", [
                'value' => $value,
                'unit' => 'seconds'
            ]);
        }
    }
}
