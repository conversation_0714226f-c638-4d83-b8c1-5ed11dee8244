<?php

namespace App\Modules\Shared\Domain\Traits;

use Illuminate\Database\Eloquent\Builder;

trait HasOrganizationId
{
    public function scopeForOrganization(Builder $query, string $organizationId): Builder
    {
        return $query->where($this->getTable() . '.organization_id', $organizationId);
    }

    public function setOrganization(string $organizationId): self
    {
        $this->organization_id = $organizationId;
        return $this;
    }
}
