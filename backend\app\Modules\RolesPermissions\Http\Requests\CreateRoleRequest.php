<?php

namespace App\Modules\RolesPermissions\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CreateRoleRequest extends FormRequest
{
    public function authorize(): bool { return true; }
    public function rules(): array
    {
        return [
            'organization_id' => ['required','uuid'],
            'name' => ['required','string','max:100'],
            'slug' => ['required','string','max:100'],
            'description' => ['nullable','string','max:500'],
            'level' => ['nullable','integer','min:0'],
        ];
    }
}
