import React, { useState, useEffect, useCallback } from 'react'
import { useNavigate } from 'react-router-dom'
import {
  CCard,
  CCardBody,
  CCardHeader,
  CCol,
  CRow,
  CButton,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
  CSpinner,
  CAlert,
  CBadge,
  CFormInput,
  CInputGroup,
  CDropdown,
  CDropdownToggle,
  CDropdownMenu,
  CDropdownItem,
  CFormSelect,
  CPagination,
  CPaginationItem,
  CFormCheck,
  CBreadcrumb,
  CBreadcrumbItem,
  CModal,
  CModalHeader,
  CModalTitle,
  CModalBody,
  CModalFooter,
  CForm,
  CFormLabel,
  CFormTextarea,
} from '@coreui/react'
import CIcon from '@coreui/icons-react'
import { 
  cilPlus, 
  cilPencil, 
  cilTrash, 
  cilFilter,
  cilSearch,
  cilCloudDownload,
  cilCloudUpload,
  cilFilterX,
  cilWarning,
} from '@coreui/icons'
import { organizationsAPI } from '../../api/organizations'
import { ExportToCsv } from 'export-to-csv'

const Organizations = () => {
  const navigate = useNavigate()
  const [organizations, setOrganizations] = useState([])
  const [filteredOrgs, setFilteredOrgs] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [selectedOrgs, setSelectedOrgs] = useState([])
  const [filters, setFilters] = useState({
    search: '',
    status: 'all',
    sortBy: 'name',
    sortOrder: 'asc',
  })
  const [pagination, setPagination] = useState({
    currentPage: 1,
    itemsPerPage: 10,
    totalItems: 0,
  })
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [bulkAction, setBulkAction] = useState('')
  const [showImportModal, setShowImportModal] = useState(false)
  const [importFile, setImportFile] = useState(null)
  const [importing, setImporting] = useState(false)

  useEffect(() => {
    loadOrganizations()
  }, [filters, pagination.currentPage])
  
  useEffect(() => {
    filterAndSortOrganizations()
  }, [organizations, filters])

  const loadOrganizations = async () => {
    try {
      setLoading(true)
      setError(null)
      const params = {
        page: pagination.currentPage,
        per_page: pagination.itemsPerPage,
        search: filters.search,
        status: filters.status === 'all' ? '' : filters.status,
        sort_by: filters.sortBy,
        sort_order: filters.sortOrder,
      }
      
      const response = await organizationsAPI.getOrganizations(params)
      const { data, meta } = response.data
      setOrganizations(data || [])
      setPagination(prev => ({
        ...prev,
        totalItems: meta?.total || 0,
        totalPages: meta?.last_page || 1,
      }))
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to load organizations')
      console.error('Error loading organizations:', err)
    } finally {
      setLoading(false)
    }
  }

  const filterAndSortOrganizations = useCallback(() => {
    let result = [...organizations]
    
    // Apply search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase()
      result = result.filter(org => 
        org.name?.toLowerCase().includes(searchLower) ||
        org.code?.toLowerCase().includes(searchLower) ||
        org.email?.toLowerCase().includes(searchLower)
      )
    }
    
    // Apply status filter
    if (filters.status !== 'all') {
      result = result.filter(org => org.status === filters.status)
    }
    
    // Apply sorting
    result.sort((a, b) => {
      let comparison = 0
      if (a[filters.sortBy] > b[filters.sortBy]) {
        comparison = 1
      } else if (a[filters.sortBy] < b[filters.sortBy]) {
        comparison = -1
      }
      return filters.sortOrder === 'asc' ? comparison : -comparison
    })
    
    setFilteredOrgs(result)
    setPagination(prev => ({
      ...prev,
      currentPage: 1,
      totalItems: result.length,
      totalPages: Math.ceil(result.length / prev.itemsPerPage)
    }))
  }, [organizations, filters])

  const handleDelete = async (id) => {
    try {
      await organizationsAPI.deleteOrganization(id)
      setOrganizations(organizations.filter((o) => o.id !== id))
      setSelectedOrgs(selectedOrgs.filter(orgId => orgId !== id))
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to delete organization')
    }
  }
  
  const handleBulkDelete = async () => {
    try {
      await Promise.all(selectedOrgs.map(id => organizationsAPI.deleteOrganization(id)))
      setOrganizations(organizations.filter(org => !selectedOrgs.includes(org.id)))
      setSelectedOrgs([])
      setShowDeleteModal(false)
    } catch (err) {
      setError('Failed to delete selected organizations')
    }
  }
  
  const handleBulkStatusChange = async (status) => {
    try {
      await Promise.all(
        selectedOrgs.map(id => 
          organizationsAPI.updateOrganization(id, { status })
        )
      )
      setOrganizations(organizations.map(org => 
        selectedOrgs.includes(org.id) ? { ...org, status } : org
      ))
      setSelectedOrgs([])
    } catch (err) {
      setError('Failed to update organizations status')
    }
  }
  
  const handleSelectAll = (e) => {
    if (e.target.checked) {
      setSelectedOrgs(paginatedOrgs.map(org => org.id))
    } else {
      setSelectedOrgs([])
    }
  }
  
  const handleSelectOrg = (orgId) => {
    setSelectedOrgs(prev => 
      prev.includes(orgId)
        ? prev.filter(id => id !== orgId)
        : [...prev, orgId]
    )
  }
  
  const handleExport = () => {
    const options = { 
      fieldSeparator: ',',
      quoteStrings: '"',
      decimalSeparator: '.',
      showLabels: true, 
      showTitle: true,
      title: 'Organizations Export',
      useTextFile: false,
      useBom: true,
      useKeysAsHeaders: true,
    }
    
    const dataToExport = filteredOrgs.map(org => ({
      name: org.name,
      code: org.code || '',
      email: org.email || '',
      phone: org.phone || '',
      status: org.status,
      created_at: org.created_at,
    }))
    
    const csvExporter = new ExportToCsv(options)
    csvExporter.generateCsv(dataToExport)
  }
  
  const handleImport = async (e) => {
    e.preventDefault()
    if (!importFile) return
    
    try {
      setImporting(true)
      const formData = new FormData()
      formData.append('file', importFile)
      
      const response = await organizationsAPI.importOrganizations(formData)
      await loadOrganizations()
      setShowImportModal(false)
      setImportFile(null)
      
      // Show success message
      setError({
        type: 'success',
        message: `Successfully imported ${response.data.imported} organizations`
      })
    } catch (err) {
      setError({
        type: 'danger',
        message: err.response?.data?.message || 'Failed to import organizations'
      })
    } finally {
      setImporting(false)
    }
  }
  
  const handleFilterChange = (e) => {
    const { name, value } = e.target
    setFilters(prev => ({ ...prev, [name]: value }))
  }
  
  const handleSort = (column) => {
    setFilters(prev => ({
      ...prev,
      sortBy: column,
      sortOrder: prev.sortBy === column && prev.sortOrder === 'asc' ? 'desc' : 'asc'
    }))
  }
  
  const resetFilters = () => {
    setFilters({
      search: '',
      status: 'all',
      sortBy: 'name',
      sortOrder: 'asc',
    })
  }
  
  const paginatedOrgs = filteredOrgs.slice(
    (pagination.currentPage - 1) * pagination.itemsPerPage,
    pagination.currentPage * pagination.itemsPerPage
  )

  const getStatusBadge = (status) => {
    const colors = { 
      active: 'success', 
      inactive: 'secondary',
      pending: 'warning',
      suspended: 'danger'
    }
    return <CBadge color={colors[status] || 'secondary'}>{status}</CBadge>
  }
  
  const SortableHeader = ({ column, children }) => (
    <CTableHeaderCell 
      scope="col" 
      style={{ cursor: 'pointer' }}
      onClick={() => handleSort(column)}
    >
      {children}
      {filters.sortBy === column && (
        <CIcon 
          icon={filters.sortOrder === 'asc' ? 'cil-arrow-top' : 'cil-arrow-bottom'} 
          className="ms-1" 
        />
      )}
    </CTableHeaderCell>
  )

  return (
    <CRow>
      <CCol xs={12}>
        <div className="mb-3">
          <CBreadcrumb>
            <CBreadcrumbItem href="/">Home</CBreadcrumbItem>
            <CBreadcrumbItem active>Organizations</CBreadcrumbItem>
          </CBreadcrumb>
        </div>
        
        <CCard className="mb-4">
          <CCardHeader>
            <div className="d-flex justify-content-between align-items-center">
              <div>
                <h5 className="mb-0">Organizations</h5>
                <small className="text-muted">Manage your organization structure and settings</small>
              </div>
              <div>
                <CButton 
                  color="primary" 
                  className="me-2"
                  onClick={() => navigate('/organizations/new')}
                >
                  <CIcon icon={cilPlus} className="me-2" />
                  Add Organization
                </CButton>
                <CDropdown>
                  <CDropdownToggle color="secondary" className="me-2">
                    <CIcon icon={cilFilter} className="me-2" />
                    Actions
                  </CDropdownToggle>
                  <CDropdownMenu>
                    <CDropdownItem onClick={handleExport}>
                      <CIcon icon={cilCloudDownload} className="me-2" />
                      Export to CSV
                    </CDropdownItem>
                    <CDropdownItem onClick={() => setShowImportModal(true)}>
                      <CIcon icon={cilCloudUpload} className="me-2" />
                      Import from CSV
                    </CDropdownItem>
                    <CDropdownItem 
                      disabled={selectedOrgs.length === 0}
                      onClick={() => handleBulkStatusChange('active')}
                    >
                      <CIcon icon={cilCheck} className="me-2" />
                      Activate Selected
                    </CDropdownItem>
                    <CDropdownItem 
                      disabled={selectedOrgs.length === 0}
                      onClick={() => handleBulkStatusChange('inactive')}
                    >
                      <CIcon icon={cilX} className="me-2" />
                      Deactivate Selected
                    </CDropdownItem>
                    <CDropdownItem 
                      disabled={selectedOrgs.length === 0}
                      onClick={() => setShowDeleteModal(true)}
                      className="text-danger"
                    >
                      <CIcon icon={cilTrash} className="me-2" />
                      Delete Selected
                    </CDropdownItem>
                  </CDropdownMenu>
                </CDropdown>
              </div>
            </div>
          </CCardHeader>
          <CCardBody>
            {error && (
              <CAlert color={error.type || 'danger'} onClose={() => setError(null)} dismissible>
                {error.message}
              </CAlert>
            )}
            
            {/* Filters */}
            <div className="mb-4 p-3 bg-light rounded">
              <CRow className="g-3">
                <CCol md={5}>
                  <CInputGroup>
                    <CFormInput
                      placeholder="Search organizations..."
                      name="search"
                      value={filters.search}
                      onChange={handleFilterChange}
                    />
                    <CButton type="button" color="primary" variant="outline">
                      <CIcon icon={cilSearch} />
                    </CButton>
                  </CInputGroup>
                </CCol>
                <CCol md={3}>
                  <CFormSelect 
                    name="status" 
                    value={filters.status}
                    onChange={handleFilterChange}
                  >
                    <option value="all">All Statuses</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                    <option value="pending">Pending</option>
                    <option value="suspended">Suspended</option>
                  </CFormSelect>
                </CCol>
                <CCol md={2}>
                  <CFormSelect 
                    name="itemsPerPage"
                    value={pagination.itemsPerPage}
                    onChange={(e) => setPagination(prev => ({
                      ...prev,
                      itemsPerPage: parseInt(e.target.value),
                      currentPage: 1
                    }))}
                  >
                    <option value="10">10 per page</option>
                    <option value="25">25 per page</option>
                    <option value="50">50 per page</option>
                    <option value="100">100 per page</option>
                  </CFormSelect>
                </CCol>
                <CCol md={2} className="d-flex">
                  <CButton 
                    color="secondary" 
                    variant="outline" 
                    className="ms-auto"
                    onClick={resetFilters}
                  >
                    <CIcon icon={cilFilterX} className="me-2" />
                    Reset
                  </CButton>
                </CCol>
              </CRow>
              
              {selectedOrgs.length > 0 && (
                <div className="mt-3 p-2 bg-white rounded border">
                  <div className="d-flex align-items-center">
                    <CIcon icon={cilWarning} className="text-warning me-2" />
                    <span className="me-3">{selectedOrgs.length} organization(s) selected</span>
                    <CButton 
                      size="sm" 
                      color="link" 
                      className="text-danger"
                      onClick={() => setSelectedOrgs([])}
                    >
                      Clear selection
                    </CButton>
                  </div>
                </div>
              )}
            </div>
            
            {loading ? (
              <div className="text-center py-5">
                <CSpinner color="primary" />
                <div className="mt-2">Loading organizations...</div>
              </div>
            ) : (
              <div className="table-responsive">
                <CTable hover responsive>
                  <CTableHead>
                    <CTableRow>
                      <CTableHeaderCell width={50}>
                        <CFormCheck 
                          id="selectAll"
                          checked={selectedOrgs.length === paginatedOrgs.length && paginatedOrgs.length > 0}
                          onChange={handleSelectAll}
                        />
                      </CTableHeaderCell>
                      <SortableHeader column="name">Name</SortableHeader>
                      <SortableHeader column="code">Code</SortableHeader>
                      <SortableHeader column="email">Email</SortableHeader>
                      <SortableHeader column="status">Status</SortableHeader>
                      <SortableHeader column="created_at">Created</SortableHeader>
                      <CTableHeaderCell>Actions</CTableHeaderCell>
                    </CTableRow>
                  </CTableHead>
                  <CTableBody>
                    {filteredOrgs.length === 0 ? (
                      <CTableRow>
                        <CTableDataCell colSpan="7" className="text-center text-muted py-5">
                          <CIcon icon={cilWarning} size="xl" className="mb-2" />
                          <div>No organizations found</div>
                          <small className="text-muted">
                            {filters.search || filters.status !== 'all' 
                              ? 'Try adjusting your search or filter criteria'
                              : 'Create a new organization to get started'}
                          </small>
                        </CTableDataCell>
                      </CTableRow>
                    ) : (
                      paginatedOrgs.map((org) => (
                        <CTableRow key={org.id} className={selectedOrgs.includes(org.id) ? 'table-active' : ''}>
                          <CTableDataCell>
                            <CFormCheck 
                              checked={selectedOrgs.includes(org.id)}
                              onChange={() => handleSelectOrg(org.id)}
                              onClick={(e) => e.stopPropagation()}
                            />
                          </CTableDataCell>
                          <CTableDataCell 
                            className="cursor-pointer"
                            onClick={() => navigate(`/organizations/${org.id}`)}
                          >
                            <div className="fw-semibold">{org.name}</div>
                            <small className="text-muted">{org.parent_id ? 'Child Organization' : 'Root Organization'}</small>
                          </CTableDataCell>
                          <CTableDataCell>
                            {org.code || <span className="text-muted">-</span>}
                          </CTableDataCell>
                          <CTableDataCell>
                            {org.email ? (
                              <a href={`mailto:${org.email}`} className="text-decoration-none">
                                {org.email}
                              </a>
                            ) : (
                              <span className="text-muted">-</span>
                            )}
                          </CTableDataCell>
                          <CTableDataCell>
                            {getStatusBadge(org.status)}
                          </CTableDataCell>
                          <CTableDataCell>
                            <small className="text-muted">
                              {new Date(org.created_at).toLocaleDateString()}
                            </small>
                          </CTableDataCell>
                          <CTableDataCell>
                            <div className="d-flex">
                              <CButton
                                color="primary"
                                size="sm"
                                variant="ghost"
                                className="me-2"
                                onClick={() => navigate(`/organizations/${org.id}`)}
                                title="Edit"
                              >
                                <CIcon icon={cilPencil} />
                              </CButton>
                              <CButton
                                color="danger"
                                size="sm"
                                variant="ghost"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  setSelectedOrgs([org.id])
                                  setShowDeleteModal(true)
                                }}
                                title="Delete"
                              >
                                <CIcon icon={cilTrash} />
                              </CButton>
                            </div>
                          </CTableDataCell>
                        </CTableRow>
                      ))
                    )}
                  </CTableBody>
                </CTable>
                
                {/* Pagination */}
                {pagination.totalPages > 1 && (
                  <div className="mt-3 d-flex justify-content-between align-items-center">
                    <div className="text-muted small">
                      Showing {((pagination.currentPage - 1) * pagination.itemsPerPage) + 1} to{' '}
                      {Math.min(pagination.currentPage * pagination.itemsPerPage, pagination.totalItems)} of{' '}
                      {pagination.totalItems} entries
                    </div>
                    
                    <CPagination aria-label="Page navigation">
                      <CPaginationItem 
                        disabled={pagination.currentPage === 1}
                        onClick={() => setPagination(prev => ({
                          ...prev,
                          currentPage: prev.currentPage - 1
                        }))}
                      >
                        Previous
                      </CPaginationItem>
                      
                      {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                        let pageNum
                        if (pagination.totalPages <= 5) {
                          pageNum = i + 1
                        } else if (pagination.currentPage <= 3) {
                          pageNum = i + 1
                        } else if (pagination.currentPage > pagination.totalPages - 3) {
                          pageNum = pagination.totalPages - 4 + i
                        } else {
                          pageNum = pagination.currentPage - 2 + i
                        }
                        
                        return (
                          <CPaginationItem
                            key={pageNum}
                            active={pagination.currentPage === pageNum}
                            onClick={() => setPagination(prev => ({
                              ...prev,
                              currentPage: pageNum
                            }))}
                          >
                            {pageNum}
                          </CPaginationItem>
                        )
                      })}
                      
                      <CPaginationItem 
                        disabled={pagination.currentPage === pagination.totalPages}
                        onClick={() => setPagination(prev => ({
                          ...prev,
                          currentPage: prev.currentPage + 1
                        }))}
                      >
                        Next
                      </CPaginationItem>
                    </CPagination>
                  </div>
                )}
              </div>
            )}
          </CCardBody>
        </CCard>
      </CCol>
      
      {/* Delete Confirmation Modal */}
      <CModal visible={showDeleteModal} onClose={() => setShowDeleteModal(false)}>
        <CModalHeader closeButton>
          <CModalTitle>Confirm Deletion</CModalTitle>
        </CModalHeader>
        <CModalBody>
          <p>Are you sure you want to delete {selectedOrgs.length} selected organization(s)? This action cannot be undone.</p>
          <p className="text-danger">
            <CIcon icon={cilWarning} className="me-2" />
            This will permanently delete the organization(s) and all associated data.
          </p>
        </CModalBody>
        <CModalFooter>
          <CButton color="secondary" onClick={() => setShowDeleteModal(false)}>
            Cancel
          </CButton>
          <CButton color="danger" onClick={handleBulkDelete}>
            Delete {selectedOrgs.length} Organization(s)
          </CButton>
        </CModalFooter>
      </CModal>
      
      {/* Import Modal */}
      <CModal visible={showImportModal} onClose={() => setShowImportModal(false)}>
        <CForm onSubmit={handleImport}>
          <CModalHeader closeButton>
            <CModalTitle>Import Organizations</CModalTitle>
          </CModalHeader>
          <CModalBody>
            <div className="mb-3">
              <CFormLabel>CSV File</CFormLabel>
              <CFormInput 
                type="file" 
                accept=".csv" 
                onChange={(e) => setImportFile(e.target.files[0])}
                required
              />
              <CFormText>
                Upload a CSV file with organization data. 
                <a href="/templates/organizations-import-template.csv" download>Download template</a>.
              </CFormText>
            </div>
            
            <div className="alert alert-info">
              <h6>CSV Format:</h6>
              <pre className="mb-0">
                name,code,email,phone,address,status
                Example Corp,EX123,<EMAIL>,+1234567890,123 Main St,active
              </pre>
            </div>
          </CModalBody>
          <CModalFooter>
            <CButton 
              color="secondary" 
              onClick={() => setShowImportModal(false)}
              disabled={importing}
            >
              Cancel
            </CButton>
            <CButton 
              color="primary" 
              type="submit" 
              disabled={!importFile || importing}
            >
              {importing ? 'Importing...' : 'Import Organizations'}
            </CButton>
          </CModalFooter>
        </CForm>
      </CModal>
    </CRow>
  )
}

export default Organizations
