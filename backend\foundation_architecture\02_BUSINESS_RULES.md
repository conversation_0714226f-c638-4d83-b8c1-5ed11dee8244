# Business Rules & Regulations

## India-Specific Requirements

### Geographic Scope
⚠️ **CRITICAL RULE:** The business operates **exclusively within India** with no plans for global expansion.

**Implications:**
- All features must comply with Indian regulations
- No payment gateway integration (Stripe, PayPal, etc.)
- Currency: INR (Indian Rupees) only
- Timezone: IST (Indian Standard Time)
- Language: English only (no internationalization needed)
- Date formats: DD/MM/YYYY (Indian standard)

---

## Resource Limit Rules

### 1. User Limits

#### Hard Limits
- **Enforcement:** Registration requests blocked when limit reached
- **Counting:** Total users across tenant + all sub-organizations
- **Approval Required:** Yes - All new user registrations must be approved
- **Add-on Available:** Yes - "Additional Users" add-on can increase limit

#### Business Logic
```
IF (current_user_count >= plan.user_limit AND plan.user_limit > 0)
THEN
    - Block new user registration
    - Show upgrade prompt
    - Require approval from organization admin
    - Send alert notification
END IF
```

####Examples
- **Basic Plan:** 10 users
  - Main org: 4 users
  - Sub-org A: 3 users
  - Sub-org B: 2 users
  - **Total: 9/10** → 1 more user allowed

- **Premium Plan:** 50 users
  - Can add users freely until limit
  - **Unlimited Plan:** user_limit = 0 (no restrictions)

---

### 2. Sub-Organization Limits

#### Hard Limits
- **Enforcement:** Creation blocked when limit reached
- **Counting:** Direct and indirect sub-organizations under tenant
- **Approval Required:** Yes - Creation requires approval workflow
- **Add-on Available:** Yes - "Additional Sub-Organizations" add-on

#### Business Logic
```
IF (current_sub_org_count >= plan.sub_org_limit AND plan.sub_org_limit > 0)
THEN
    - Block new sub-org creation
    - Show upgrade message
    - Require approval for special cases
    - Log attempt in audit trail
END IF
```

#### Examples
```
Tenant Org
  ├── Sub-org 1
  ├── Sub-org 2
  │   └── Sub-org 2.1 (nested)
  └── Sub-org 3

Total count: 4 sub-organizations (including nested)
```

---

### 3. Storage Limits

#### Soft Limits with Alerts
- **Enforcement:** Alerts at thresholds, hard block at 100%
- **Counting:** Total file storage across organization
- **Approval Required:** No - But notifications sent
- **Add-on Available:** Yes - "Additional Storage" add-on (per GB)

#### Alert Thresholds
| Threshold | Action | Recipients |
|-----------|--------|------------|
| 75% | ⚠️ Warning notification | Organization admins |
| 90% | 🔶 Critical alert | All admins + portal owner notification |
| 95% | 🚨 Urgent alert | Daily reminders |
| 100% | 🛑 Hard block | Upload prevented |

#### Business Logic
```
IF (storage_used >= plan.storage_limit * 0.75)
THEN
    - Send alert notification
    - Log usage in resource_usage_logs table
    - Set alert_level based on percentage
    - Recommend upgrade or add-on purchase
END IF

IF (storage_used >= plan.storage_limit)
THEN
    - Block new file uploads
    - Show storage full message
    - Force upgrade or cleanup
END IF
```

---

### 4. Hierarchy Depth Limits

#### Hard Limits
- **Enforcement:** Nested sub-org creation blocked at limit
- **Counting:** Maximum depth level in organization tree
- **Approval Required:** No
- **Add-on Available:** Yes - "Additional Hierarchy Level" add-on

#### Depth Calculation
```
Portal Owner (level 0)
  └── Tenant (level 1)
      └── Sub-org (level 2)
          └── Sub-sub-org (level 3)
              └── Sub-sub-sub-org (level 4)

If plan.hierarchy_depth_limit = 3:
  - Maximum allowed: level 4 (Tenant + 3 nested levels)
  - Level 5 would be blocked
```

---

### 5. API Call Limits

#### Soft Limits with Throttling
- **Enforcement:** Rate limiting via middleware
- **Counting:** Requests per day per organization
- **Approval Required:** No
- **Add-on Available:** Yes - "Additional API Calls" add-on (per 1000 calls)

#### Throttle Rules
| Plan Type | Daily Limit | Per Minute | Burst Allowed |
|-----------|-------------|------------|---------------|
| Basic | 10,000 | 100 | 150 |
| Professional | 50,000 | 500 | 750 |
| Enterprise | 500,000 | 5,000 | 7,500 |
| Unlimited | No limit | 10,000 | 15,000 |

---

### 6. Module Access Rules

#### Feature Flags
- **Enforcement:** UI and API level restrictions
- **Counting:** Enabled modules in plan
- **Approval Required:** No - Handled by subscription
- **Add-on Available:** Yes - "Additional Module" add-on

#### Module Categories
1. **Core Modules** (Always included)
   - Organizations
   - Users
   - Settings
   - Audit

2. **Standard Modules** (Plan-dependent)
   - Billing
   - Approvals
   - Notifications
   - Reports

3. **Premium Modules** (Requires add-on or enterprise plan)
   - Accounting
   - Inventory
   - Advanced Integration

---

## Approval Workflow Rules

### User Registration Approval

#### When Required
- **Always:** Every new user registration
- **Exception:** Portal Owner can bypass for their own organization
- **Timeout:** Auto-reject after 7 days of inaction

#### Approval Chain
```
1. User submits registration request
   ↓
2. Check: Is user limit reached?
   YES → Reject immediately with upgrade message
   NO → Continue to approval
   ↓
3. Assign to Organization Admin(s)
   ↓
4. Admin reviews request
   ↓
5. Approved → User account created
   Rejected → Request closed with reason
```

#### Required Data
- User details (name, email, role)
- Requesting organization
- Justification/reason
- Current resource usage
- Plan limits

---

### Sub-Organization Creation Approval

#### When Required
- **Always:** Creating new sub-organization
- **Exception:** None - Always requires approval
- **Timeout:** Auto-reject after 14 days

#### Approval Chain
```
1. User submits sub-org creation request
   ↓
2. Check: Is sub-org limit reached?
   YES → Reject with upgrade message
   NO → Check hierarchy depth
   ↓
3. Check: Does new sub-org exceed depth limit?
   YES → Reject
   NO → Continue to approval
   ↓
4. Assign to Parent Organization Admin
   ↓
5. Admin reviews structure and justification
   ↓
6. Approved → Sub-org created
   Rejected → Request closed
```

---

## Subscription Rules

### Plan Changes

#### Upgrade Rules
```
User requests upgrade
  ↓
Calculate prorated amount
  ↓
IF (immediate upgrade)
  THEN
    - Calculate days remaining in current period
    - Credit unused portion of current plan
    - Charge for remaining days of new plan
    - Apply immediately
  ELSE
    - Schedule upgrade for next billing period
    - No proration needed
  END IF
```

#### Downgrade Rules
```
User requests downgrade
  ↓
Check if current usage exceeds new plan limits
  ↓
IF (usage > new_plan_limits)
  THEN
    - Show warning message
    - Require user confirmation
    - Give 30 days to reduce usage
    - Block downgrade until compliant
  ELSE
    - Schedule for next billing period
    - No refund for current period
  END IF
```

---

### Add-On Purchase Rules

#### Immediate Effect
- Add-ons activate immediately upon purchase
- Prorated billing for current period
- Auto-renews with subscription

#### Calculation Example
```
Current Plan: ₹1,000/month (30 days)
Days Remaining: 15 days
Daily Rate: ₹1,000 / 30 = ₹33.33

Add-on: 5 extra users @ ₹100 each = ₹500/month
Add-on Daily Rate: ₹500 / 30 = ₹16.67

Prorated Charge: ₹16.67 × 15 days = ₹250
```

---

## Resource Usage Tracking Rules

### Logging Frequency
- **Automated:** Daily at 00:00 IST
- **Manual Trigger:** On-demand via API or admin panel
- **Retention:** 90 days (configurable)

### What Gets Logged
```
For each active subscription:
  - Current user count
  - Current sub-org count
  - Current storage usage (bytes)
  - Current hierarchy depth
  - Timestamp of logging
  - Alert level (normal/warning/critical/exceeded)
```

### Alert Triggers
```
IF (usage_percentage >= 75%)
  alert_level = 'warning'
  - Send email to admins
  - Show dashboard notification

IF (usage_percentage >= 90%)
  alert_level = 'critical'
  - Send urgent email
  - SMS notification (if configured)
  - Dashboard banner

IF (usage_percentage >= 100%)
  alert_level = 'exceeded'
  - Block resource creation
  - Force upgrade prompt
  - Escalate to portal owner
```

---

## Data Isolation Rules

### Tenant Isolation
⚠️ **CRITICAL:** Absolute data isolation between tenants

#### Enforcement Mechanisms
1. **Database Level:**
   - All tables have `organization_id` column
   - Global query scopes enforce tenant filtering
   - Foreign keys maintain referential integrity

2. **Application Level:**
   - Middleware validates organization context
   - API requests include organization identifier
   - Session contains organization scope

3. **Testing Requirements:**
   - Every feature must pass tenant isolation tests
   - Cross-tenant data access attempts must fail
   - Audit logs track all data access

#### Example
```
Tenant A cannot:
  - View Tenant B's users
  - Access Tenant B's data
  - See Tenant B's usage statistics
  - Receive Tenant B's notifications

Even Portal Owner must:
  - Switch context to view tenant data
  - Log all access to tenant scope
  - Respect tenant privacy settings
```

---

## Audit & Compliance Rules

### What Must Be Logged
1. **User Actions:**
   - Login/Logout
   - Password changes
   - Permission modifications
   - Resource creation/modification/deletion

2. **Organization Changes:**
   - Sub-org creation/deletion
   - Hierarchy modifications
   - User assignments

3. **Billing Events:**
   - Plan changes
   - Add-on purchases
   - Payment attempts (without sensitive data)
   - Subscription status changes

4. **Resource Limit Events:**
   - Limit breaches
   - Alert triggers
   - Block events
   - Override approvals

### Retention Requirements
- **Audit Logs:** 7 years (Indian compliance)
- **Usage Logs:** 90 days (operational)
- **User Activity:** 1 year (security)
- **Financial Records:** Indefinite (or per Indian law)

---

## Error Handling Rules

### User-Facing Errors
- Never expose technical details
- Provide actionable guidance
- Log full details server-side
- Use Indian English

#### Examples
❌ Bad: "Database connection failed: errno 2002"
✅ Good: "We're experiencing technical difficulties. Please try again in a few moments."

❌ Bad: "Subscription::find() returned null"
✅ Good: "No active subscription found. Please contact support or subscribe to a plan."

---

## Notification Rules

### Alert Priorities
| Priority | Delivery Method | Timing |
|----------|----------------|--------|
| Low | In-app only | Batch daily |
| Medium | In-app + Email | Within 1 hour |
| High | In-app + Email | Within 15 minutes |
| Critical | In-app + Email + SMS | Immediate |

### Resource Usage Notifications
```
75% threshold:
  - Priority: Medium
  - Recipients: Organization admins
  - Frequency: Once per threshold crossing

90% threshold:
  - Priority: High
  - Recipients: All admins
  - Frequency: Daily reminders

100% threshold:
  - Priority: Critical
  - Recipients: All admins + Portal owner notification
  - Frequency: Every 6 hours until resolved
```

---

*Next Document: 03_SYSTEM_ARCHITECTURE.md - High-level system architecture and design patterns*
