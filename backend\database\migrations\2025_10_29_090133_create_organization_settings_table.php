<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('organization_settings', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('organization_id');
            $table->string('key')->comment('Setting key');
            $table->text('value')->nullable()->comment('Setting value');
            $table->string('type', 50)->default('string')->comment('string, boolean, integer, json');
            $table->text('description')->nullable();
            $table->timestamps();
            
            $table->unique(['organization_id', 'key']);
            
            $table->index('organization_id');
            $table->index('key');
            
            $table->foreign('organization_id')->references('id')->on('organizations')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('organization_settings');
    }
};
