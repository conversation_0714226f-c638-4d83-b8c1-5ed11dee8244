<?php

namespace Database\Factories\Modules\Approvals\Domain\Models;

use App\Modules\Approvals\Domain\Models\ApprovalRequest;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class ApprovalRequestFactory extends Factory
{
    protected $model = ApprovalRequest::class;

    public function definition(): array
    {
        return [
            'organization_id' => \App\Modules\Organizations\Domain\Models\Organization::factory(),
            'requester_id' => \App\Modules\Users\Domain\Models\User::factory(),
            'type' => 'generic',
            'status' => 'pending',
            'description' => $this->faker->sentence(),
            'amount' => $this->faker->randomFloat(2, 10, 1000),
            'data' => [],
            'current_step' => 1,
            'submitted_at' => now(),
            'completed_at' => null,
        ];
    }
}
