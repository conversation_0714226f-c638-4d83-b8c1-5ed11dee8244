<?php

namespace App\Modules\Billing\Presentation\API;

use App\Http\Controllers\Controller;
use App\Modules\Billing\Application\Services\BillingService;
use App\Modules\Billing\Application\Services\UsageTrackingService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class BillingController extends Controller
{
    public function __construct(
        private readonly BillingService $billingService,
        private readonly UsageTrackingService $usageTrackingService
    ) {
    }

    /**
     * Add an add-on to a subscription
     */
    public function addAddon(Request $request, string $subscriptionId): JsonResponse
    {
        $validated = $request->validate([
            'addon_id' => 'required|uuid|exists:addons,id',
            'quantity' => 'integer|min:1',
        ]);

        try {
            $subscription = $this->billingService->addAddon(
                $subscriptionId,
                $validated['addon_id'],
                $validated['quantity'] ?? 1
            );

            return response()->json([
                'success' => true,
                'message' => 'Add-on successfully added to subscription',
                'data' => $subscription->load('addonCatalog'),
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Remove an add-on from a subscription
     */
    public function removeAddon(string $subscriptionId, string $addonId): JsonResponse
    {
        try {
            $subscription = $this->billingService->removeAddon($subscriptionId, $addonId);

            return response()->json([
                'success' => true,
                'message' => 'Add-on successfully removed from subscription',
                'data' => $subscription->load('addonCatalog'),
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Upgrade a subscription to a new plan
     */
    public function upgrade(Request $request, string $subscriptionId): JsonResponse
    {
        $validated = $request->validate([
            'new_plan_id' => 'required|uuid|exists:plans,id',
        ]);

        try {
            $subscription = $this->billingService->upgradeSubscription(
                $subscriptionId,
                $validated['new_plan_id']
            );

            return response()->json([
                'success' => true,
                'message' => 'Subscription successfully upgraded',
                'data' => $subscription->load('plan'),
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Downgrade a subscription to a new plan
     */
    public function downgrade(Request $request, string $subscriptionId): JsonResponse
    {
        $validated = $request->validate([
            'new_plan_id' => 'required|uuid|exists:plans,id',
        ]);

        try {
            $subscription = $this->billingService->downgradeSubscription(
                $subscriptionId,
                $validated['new_plan_id']
            );

            return response()->json([
                'success' => true,
                'message' => 'Subscription successfully downgraded',
                'data' => $subscription->load('plan'),
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Cancel a subscription
     */
    public function cancel(Request $request, string $subscriptionId): JsonResponse
    {
        $validated = $request->validate([
            'immediate' => 'boolean',
        ]);

        try {
            $subscription = $this->billingService->cancelSubscription(
                $subscriptionId,
                $validated['immediate'] ?? false
            );

            return response()->json([
                'success' => true,
                'message' => $validated['immediate'] ?? false 
                    ? 'Subscription successfully cancelled immediately' 
                    : 'Subscription will be cancelled at the end of billing period',
                'data' => $subscription,
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Get resource usage for a tenant organization
     */
    public function getUsage(string $organizationId): JsonResponse
    {
        try {
            $usage = $this->usageTrackingService->getCurrentUsage($organizationId);

            return response()->json([
                'success' => true,
                'data' => $usage,
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Get usage history for a subscription
     */
    public function getUsageHistory(Request $request, string $subscriptionId): JsonResponse
    {
        $validated = $request->validate([
            'days' => 'integer|min:1|max:365',
        ]);

        try {
            $subscription = \App\Modules\Billing\Domain\Models\Subscription::findOrFail($subscriptionId);
            $days = $validated['days'] ?? 30;
            
            $logs = $this->usageTrackingService->getUsageHistory(
                $subscription,
                null,
                now()->subDays($days),
                now()
            );

            return response()->json([
                'success' => true,
                'data' => $logs,
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Get usage alerts for a tenant organization
     */
    public function getUsageAlerts(string $organizationId): JsonResponse
    {
        try {
            $alerts = $this->usageTrackingService->getUsageAlerts($organizationId);

            return response()->json([
                'success' => true,
                'data' => $alerts,
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Calculate price for plan upgrade
     */
    public function calculateUpgradePrice(Request $request, string $subscriptionId): JsonResponse
    {
        $validated = $request->validate([
            'new_plan_id' => 'required|uuid|exists:plans,id',
        ]);

        try {
            $priceDetails = $this->billingService->calculateUpgradePrice(
                $subscriptionId,
                $validated['new_plan_id']
            );

            return response()->json([
                'success' => true,
                'data' => $priceDetails,
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }
}
