# Portal Owner Frontend - Rapid Completion Guide

## Current Status
✅ API Infrastructure: 11/11 files
✅ Redux Store: 3/3 files  
✅ Navigation: 2/2 files
✅ Working Components: 4/20 (Users, Organizations list, Organizations detail, Roles list)

## Remaining Components to Create (16 files)

### List Views (7 files) - Use this template:

**File: src/views/[module]/[Module].js**

```javascript
import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { CCard, CCardBody, CCardHeader, CCol, CRow, CButton, CTable, CTableBody, CTableDataCell, CTableHead, CTableHeaderCell, CTableRow, CSpinner, CAlert, CBadge } from '@coreui/react'
import CIcon from '@coreui/icons-react'
import { cilPlus, cilPencil, cilTrash } from '@coreui/icons'
import { [MODULE]API } from '../../api/[module]'

const [Module] = () => {
  const navigate = useNavigate()
  const [items, setItems] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    loadItems()
  }, [])

  const loadItems = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await [MODULE]API.get[Modules]()
      setItems(response.data.data || [])
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to load')
      console.error('Error:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async (id) => {
    if (window.confirm('Are you sure?')) {
      try {
        await [MODULE]API.delete[Module](id)
        setItems(items.filter((i) => i.id !== id))
      } catch (err) {
        setError(err.response?.data?.message || 'Failed to delete')
      }
    }
  }

  return (
    <CRow>
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardHeader>
            <div className="d-flex justify-content-between align-items-center">
              <strong>[Module Name]</strong>
              <CButton color="primary" size="sm" onClick={() => navigate('/[path]/new')}>
                <CIcon icon={cilPlus} className="me-2" />
                Add [Item]
              </CButton>
            </div>
          </CCardHeader>
          <CCardBody>
            {error && <CAlert color="danger">{error}</CAlert>}
            {loading ? (
              <div className="text-center"><CSpinner color="primary" /></div>
            ) : (
              <CTable hover responsive>
                <CTableHead>
                  <CTableRow>
                    <CTableHeaderCell scope="col">#</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Name</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Status</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Actions</CTableHeaderCell>
                  </CTableRow>
                </CTableHead>
                <CTableBody>
                  {items.length === 0 ? (
                    <CTableRow>
                      <CTableDataCell colSpan="4" className="text-center text-muted">
                        No items found
                      </CTableDataCell>
                    </CTableRow>
                  ) : (
                    items.map((item, idx) => (
                      <CTableRow key={item.id}>
                        <CTableDataCell>{idx + 1}</CTableDataCell>
                        <CTableDataCell>{item.name}</CTableDataCell>
                        <CTableDataCell>
                          <CBadge color={item.status === 'active' ? 'success' : 'secondary'}>
                            {item.status}
                          </CBadge>
                        </CTableDataCell>
                        <CTableDataCell>
                          <CButton color="info" size="sm" className="me-2"
                            onClick={() => navigate(`/[path]/${item.id}`)}>
                            <CIcon icon={cilPencil} />
                          </CButton>
                          <CButton color="danger" size="sm" onClick={() => handleDelete(item.id)}>
                            <CIcon icon={cilTrash} />
                          </CButton>
                        </CTableDataCell>
                      </CTableRow>
                    ))
                  )}
                </CTableBody>
              </CTable>
            )}
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  )
}

export default [Module]
```

### List Views to Create (7 files):
1. ✅ Subscriptions.js - Update existing
2. ✅ Payments.js - Update existing
3. ✅ Invoices.js - Update existing
4. ✅ Approvals.js - Update existing
5. ✅ Notifications.js - Update existing
6. ✅ Webhooks.js - Update existing
7. ✅ Reports.js - Update existing (already has tabs)

### Detail Pages to Create (9 files):

**File: src/views/[module]/[Module]Detail.js**

```javascript
import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { CCard, CCardBody, CCardHeader, CCol, CRow, CForm, CFormLabel, CFormInput, CFormSelect, CButton, CSpinner, CAlert } from '@coreui/react'
import { [MODULE]API } from '../../api/[module]'

const [Module]Detail = () => {
  const { id } = useParams()
  const navigate = useNavigate()
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [formData, setFormData] = useState({ name: '', description: '', status: 'active' })
  const [saving, setSaving] = useState(false)

  useEffect(() => {
    if (id && id !== 'new') {
      loadItem()
    } else {
      setLoading(false)
    }
  }, [id])

  const loadItem = async () => {
    try {
      setLoading(true)
      const response = await [MODULE]API.get[Module](id)
      setFormData(response.data.data)
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to load')
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    try {
      setSaving(true)
      if (id && id !== 'new') {
        await [MODULE]API.update[Module](id, formData)
      } else {
        await [MODULE]API.create[Module](formData)
      }
      navigate('/[path]')
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to save')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return <CRow><CCol xs={12} className="text-center"><CSpinner color="primary" /></CCol></CRow>
  }

  return (
    <CRow>
      <CCol xs={12} md={8}>
        <CCard>
          <CCardHeader>
            <strong>{id && id !== 'new' ? 'Edit' : 'Create'} [Item]</strong>
          </CCardHeader>
          <CCardBody>
            {error && <CAlert color="danger">{error}</CAlert>}
            <CForm onSubmit={handleSubmit}>
              <div className="mb-3">
                <CFormLabel htmlFor="name">Name</CFormLabel>
                <CFormInput id="name" name="name" value={formData.name} onChange={handleChange} required />
              </div>
              <div className="mb-3">
                <CFormLabel htmlFor="description">Description</CFormLabel>
                <CFormInput id="description" name="description" value={formData.description} onChange={handleChange} />
              </div>
              <div className="mb-3">
                <CFormLabel htmlFor="status">Status</CFormLabel>
                <CFormSelect id="status" name="status" value={formData.status} onChange={handleChange}>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </CFormSelect>
              </div>
              <div className="d-grid gap-2">
                <CButton type="submit" color="primary" disabled={saving}>
                  {saving ? <CSpinner size="sm" className="me-2" /> : null}
                  {id && id !== 'new' ? 'Update' : 'Create'}
                </CButton>
                <CButton type="button" color="secondary" onClick={() => navigate('/[path]')}>
                  Cancel
                </CButton>
              </div>
            </CForm>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  )
}

export default [Module]Detail
```

### Detail Pages to Create (9 files):
1. RoleDetail.js
2. SubscriptionDetail.js
3. PaymentDetail.js
4. InvoiceDetail.js
5. ApprovalDetail.js
6. NotificationDetail.js
7. WebhookDetail.js

### Dashboard Implementation:

**File: src/views/dashboard/Dashboard.js**

```javascript
import React, { useState, useEffect } from 'react'
import { CCard, CCardBody, CCardHeader, CCol, CRow, CSpinner, CAlert } from '@coreui/react'
import { reportsAPI } from '../../api/reports'

const Dashboard = () => {
  const [metrics, setMetrics] = useState({})
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    loadMetrics()
  }, [])

  const loadMetrics = async () => {
    try {
      setLoading(true)
      const [subs, payments, revenue, users, approvals] = await Promise.all([
        reportsAPI.getSubscriptionsReport(),
        reportsAPI.getPaymentsReport(),
        reportsAPI.getRevenueReport(),
        reportsAPI.getUsersReport(),
        reportsAPI.getApprovalsReport(),
      ])
      setMetrics({
        subscriptions: subs.data.data,
        payments: payments.data.data,
        revenue: revenue.data.data,
        users: users.data.data,
        approvals: approvals.data.data,
      })
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to load metrics')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return <CRow><CCol xs={12} className="text-center"><CSpinner color="primary" /></CCol></CRow>
  }

  return (
    <CRow>
      {error && <CCol xs={12}><CAlert color="danger">{error}</CAlert></CCol>}
      
      <CCol xs={12} sm={6} lg={3}>
        <CCard className="mb-4">
          <CCardBody>
            <div className="text-muted small">Active Subscriptions</div>
            <div className="fs-5 fw-bold">{metrics.subscriptions?.active || 0}</div>
          </CCardBody>
        </CCard>
      </CCol>

      <CCol xs={12} sm={6} lg={3}>
        <CCard className="mb-4">
          <CCardBody>
            <div className="text-muted small">Total Revenue</div>
            <div className="fs-5 fw-bold">${metrics.revenue?.total || 0}</div>
          </CCardBody>
        </CCard>
      </CCol>

      <CCol xs={12} sm={6} lg={3}>
        <CCard className="mb-4">
          <CCardBody>
            <div className="text-muted small">Total Users</div>
            <div className="fs-5 fw-bold">{metrics.users?.total || 0}</div>
          </CCardBody>
        </CCard>
      </CCol>

      <CCol xs={12} sm={6} lg={3}>
        <CCard className="mb-4">
          <CCardBody>
            <div className="text-muted small">Pending Approvals</div>
            <div className="fs-5 fw-bold">{metrics.approvals?.pending || 0}</div>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  )
}

export default Dashboard
```

### Settings Implementation:

**File: src/views/settings/Settings.js**

```javascript
import React, { useState, useEffect } from 'react'
import { CCard, CCardBody, CCardHeader, CCol, CRow, CForm, CFormLabel, CFormInput, CButton, CSpinner, CAlert } from '@coreui/react'
import { settingsAPI } from '../../api/settings'

const Settings = () => {
  const [settings, setSettings] = useState({})
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [saving, setSaving] = useState(false)

  useEffect(() => {
    loadSettings()
  }, [])

  const loadSettings = async () => {
    try {
      setLoading(true)
      const response = await settingsAPI.getSettings()
      setSettings(response.data.data || {})
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to load settings')
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (key, value) => {
    setSettings((prev) => ({ ...prev, [key]: value }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    try {
      setSaving(true)
      await settingsAPI.updateSettings(settings)
      setError(null)
      alert('Settings saved successfully')
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to save settings')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return <CRow><CCol xs={12} className="text-center"><CSpinner color="primary" /></CCol></CRow>
  }

  return (
    <CRow>
      <CCol xs={12} md={8}>
        <CCard>
          <CCardHeader>
            <strong>System Settings</strong>
          </CCardHeader>
          <CCardBody>
            {error && <CAlert color="danger">{error}</CAlert>}
            <CForm onSubmit={handleSubmit}>
              {Object.entries(settings).map(([key, value]) => (
                <div key={key} className="mb-3">
                  <CFormLabel htmlFor={key}>{key}</CFormLabel>
                  <CFormInput id={key} value={value} onChange={(e) => handleChange(key, e.target.value)} />
                </div>
              ))}
              <CButton type="submit" color="primary" disabled={saving}>
                {saving ? <CSpinner size="sm" className="me-2" /> : null}
                Save Settings
              </CButton>
            </CForm>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  )
}

export default Settings
```

## Implementation Checklist

### List Views (Update existing files):
- [ ] Subscriptions.js
- [ ] Payments.js
- [ ] Invoices.js
- [ ] Approvals.js
- [ ] Notifications.js
- [ ] Webhooks.js

### Detail Pages (Create new files):
- [ ] RoleDetail.js
- [ ] SubscriptionDetail.js
- [ ] PaymentDetail.js
- [ ] InvoiceDetail.js
- [ ] ApprovalDetail.js
- [ ] NotificationDetail.js
- [ ] WebhookDetail.js

### Other Pages:
- [ ] Dashboard.js (update)
- [ ] Reports.js (update with data)
- [ ] Settings.js (update)

## Testing Checklist

For each component:
1. [ ] Open in browser
2. [ ] Check Network tab for API calls
3. [ ] Verify data loads correctly
4. [ ] Test error handling
5. [ ] Test loading states
6. [ ] Test CRUD operations
7. [ ] Check console for errors

## API Endpoints to Verify

- [ ] GET /organizations
- [ ] GET /users
- [ ] GET /roles
- [ ] GET /subscriptions
- [ ] GET /payments
- [ ] GET /invoices
- [ ] GET /approvals
- [ ] GET /notifications
- [ ] GET /webhooks
- [ ] GET /reports/*
- [ ] GET /settings

## Common Issues & Fixes

### Issue: API returns 401 (Unauthorized)
**Fix**: Check token in localStorage, ensure login works first

### Issue: CORS errors
**Fix**: Verify API base URL in client.js matches backend

### Issue: Data not loading
**Fix**: Check Network tab, look for failed requests, check console for errors

### Issue: Form not submitting
**Fix**: Verify form data matches API requirements, check error messages

## Performance Tips

1. Use React.lazy() for route components (already done)
2. Add pagination for large lists
3. Implement search/filter
4. Cache API responses
5. Use loading skeletons instead of spinners

## Security Checklist

- [ ] JWT tokens stored securely
- [ ] Token refresh working
- [ ] Protected routes implemented
- [ ] Error messages don't expose sensitive data
- [ ] Input validation on forms
- [ ] CSRF protection (if needed)

