<?php

namespace Tests\Feature\Approvals;

use Tests\TestCase;
use App\Modules\Approvals\Domain\Services\ApprovalService;
use App\Modules\Approvals\Domain\Models\ApprovalRequest;
use App\Modules\Organizations\Domain\Models\Organization;
use App\Modules\Users\Domain\Models\User;

class ApprovalServiceTest extends TestCase
{
    private ApprovalService $approvalService;
    private Organization $organization;
    private User $requester;
    private User $approver;

    protected function setUp(): void
    {
        parent::setUp();
        $this->approvalService = app(ApprovalService::class);
        $this->organization = Organization::factory()->create();
        $this->requester = User::factory()->create(['organization_id' => $this->organization->id]);
        $this->approver = User::factory()->create(['organization_id' => $this->organization->id]);
    }

    public function test_can_create_approval_request()
    {
        $data = [
            'organization_id' => $this->organization->id,
            'requester_id' => $this->requester->id,
            'type' => 'leave_request',
            'description' => 'Annual leave request',
            'amount' => 5,
            'steps' => [
                [
                    'approver_id' => $this->approver->id,
                    'approval_type' => 'sequential',
                ],
            ],
        ];

        $approval = $this->approvalService->createApprovalRequest($data);

        $this->assertNotNull($approval);
        $this->assertEquals('pending', $approval->status);
        $this->assertEquals($this->requester->id, $approval->requester_id);
        $this->assertCount(1, $approval->steps);
    }

    public function test_can_approve_request()
    {
        $approval = ApprovalRequest::factory()->create([
            'organization_id' => $this->organization->id,
            'requester_id' => $this->requester->id,
            'status' => 'pending',
        ]);

        $step = $approval->steps()->create([
            'organization_id' => $this->organization->id,
            'step_number' => 1,
            'approver_id' => $this->approver->id,
            'status' => 'pending',
        ]);

        $result = $this->approvalService->approveRequest($approval, $this->approver);

        $this->assertEquals('approved', $result->status);
        $step->refresh();
        $this->assertEquals('approved', $step->status);
        $this->assertEquals($this->approver->id, $step->approved_by);
    }

    public function test_can_reject_request()
    {
        $approval = ApprovalRequest::factory()->create([
            'organization_id' => $this->organization->id,
            'requester_id' => $this->requester->id,
            'status' => 'pending',
        ]);

        $step = $approval->steps()->create([
            'organization_id' => $this->organization->id,
            'step_number' => 1,
            'approver_id' => $this->approver->id,
            'status' => 'pending',
        ]);

        $result = $this->approvalService->rejectRequest($approval, $this->approver, 'Not approved');

        $this->assertEquals('rejected', $result->status);
        $step->refresh();
        $this->assertEquals('rejected', $step->status);
    }

    public function test_can_add_comment()
    {
        $approval = ApprovalRequest::factory()->create([
            'organization_id' => $this->organization->id,
        ]);

        $comment = $this->approvalService->addComment($approval, $this->approver, 'This looks good', 'comment');

        $this->assertNotNull($comment);
        $this->assertEquals('This looks good', $comment->comment);
        $this->assertEquals($this->approver->id, $comment->user_id);
    }

    public function test_can_escalate_request()
    {
        $approval = ApprovalRequest::factory()->create([
            'organization_id' => $this->organization->id,
            'status' => 'pending',
        ]);

        $result = $this->approvalService->escalateRequest($approval, $this->approver, 'Needs escalation');

        $this->assertEquals('escalated', $result->status);
    }

    public function test_can_get_pending_approvals()
    {
        $approval1 = ApprovalRequest::factory()->create(['organization_id' => $this->organization->id]);
        $approval1->steps()->create([
            'organization_id' => $this->organization->id,
            'approver_id' => $this->approver->id,
            'status' => 'pending',
        ]);

        $approval2 = ApprovalRequest::factory()->create(['organization_id' => $this->organization->id]);
        $approval2->steps()->create([
            'organization_id' => $this->organization->id,
            'approver_id' => $this->approver->id,
            'status' => 'pending',
        ]);

        $pending = $this->approvalService->getPendingApprovals($this->approver);

        $this->assertCount(2, $pending);
    }
}
