<?php

namespace Tests\Unit\Billing;

use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;
use Illuminate\Foundation\Testing\DatabaseMigrations;
use App\Modules\Organizations\Domain\Models\Organization;
use App\Modules\Billing\Domain\Models\Plan;
use App\Modules\Billing\Domain\Models\Subscription;
use App\Modules\Billing\Domain\Models\ResourceUsageLog;
use App\Modules\Billing\Application\Services\UsageTrackingService;
use Carbon\Carbon;

class UsageTrackingServiceTest extends TestCase
{
    use DatabaseMigrations;

    protected UsageTrackingService $service;
    protected Organization $tenant;
    protected Plan $plan;
    protected Subscription $subscription;

    protected function setUp(): void
    {
        parent::setUp();

        $this->service = app(UsageTrackingService::class);

        $portalOwner = Organization::factory()->create([
            'type' => 'portal_owner',
            'parent_id' => null,
        ]);

        $this->tenant = Organization::factory()->create([
            'type' => 'tenant',
            'parent_id' => $portalOwner->id,
        ]);

        $this->plan = Plan::factory()->create([
            'name' => 'Test Plan',
            'slug' => 'test',
            'user_limit' => 10,
            'sub_org_limit' => 5,
            'storage_limit' => 50, // GB
            'hierarchy_depth_limit' => 3,
            'api_calls_limit' => 5000,
        ]);

        $this->subscription = Subscription::factory()->create([
            'organization_id' => $this->tenant->id,
            'plan_id' => $this->plan->id,
            'status' => 'active',
            'user_count' => 5,
            'sub_org_count' => 2,
            'storage_used' => 10 * 1024 * 1024 * 1024, // 10 GB
            'hierarchy_depth' => 2,
        ]);
    }
    #[Test]
    public function can_log_resource_usage()
    {
        $result = $this->service->logResourceUsage($this->subscription);

        $this->assertTrue($result['success']);
        $this->assertNotEmpty($result['logs']);
        $this->assertDatabaseHas('resource_usage_logs', [
            'subscription_id' => $this->subscription->id,
            'resource_type' => 'users',
        ]);
    }
    #[Test]
    public function logs_all_resource_types_with_limits()
    {
        $result = $this->service->logResourceUsage($this->subscription);

        $this->assertTrue($result['success']);
        
        // Should log users, sub_orgs, storage, hierarchy_depth
        $logs = $result['logs'];
        $this->assertCount(4, $logs); // 4 resource types with limits

        $loggedTypes = collect($logs)->pluck('resource_type')->toArray();
        $this->assertContains('users', $loggedTypes);
        $this->assertContains('sub_orgs', $loggedTypes);
        $this->assertContains('storage', $loggedTypes);
        $this->assertContains('hierarchy_depth', $loggedTypes);
    }
    #[Test]
    public function calculates_usage_percentage_correctly()
    {
        $result = $this->service->logResourceUsage($this->subscription);

        $userLog = collect($result['logs'])->firstWhere('resource_type', 'users');
        
        // 5 users out of 10 = 50%
        $this->assertEquals(50.00, $userLog->usage_percentage);
    }
    #[Test]
    public function calculates_correct_alert_level()
    {
        $this->subscription->update(['user_count' => 8]); // 80%

        $result = $this->service->logResourceUsage($this->subscription);
        $userLog = collect($result['logs'])->firstWhere('resource_type', 'users');

        $this->assertEquals('warning', $userLog->alert_level);
    }
    #[Test]
    public function sets_critical_alert_level_above_90_percent()
    {
        $this->subscription->update(['user_count' => 9]); // 90%

        $result = $this->service->logResourceUsage($this->subscription);
        $userLog = collect($result['logs'])->firstWhere('resource_type', 'users');

        $this->assertEquals('critical', $userLog->alert_level);
    }
    #[Test]
    public function sets_exceeded_alert_level_at_100_percent()
    {
        $this->subscription->update(['user_count' => 11]); // 110%

        $result = $this->service->logResourceUsage($this->subscription);
        $userLog = collect($result['logs'])->firstWhere('resource_type', 'users');

        $this->assertEquals('exceeded', $userLog->alert_level);
    }
    #[Test]
    public function can_get_usage_history()
    {
        // Create multiple logs
        ResourceUsageLog::factory()->count(3)->create([
            'subscription_id' => $this->subscription->id,
            'tenant_organization_id' => $this->tenant->id,
            'resource_type' => 'users',
        ]);

        $history = $this->service->getUsageHistory($this->subscription);

        $this->assertEquals(3, $history['total']);
        $this->assertCount(3, $history['logs']);
    }
    #[Test]
    public function can_filter_usage_history_by_resource_type()
    {
        ResourceUsageLog::factory()->count(2)->create([
            'subscription_id' => $this->subscription->id,
            'tenant_organization_id' => $this->tenant->id,
            'resource_type' => 'users',
        ]);

        ResourceUsageLog::factory()->count(3)->create([
            'subscription_id' => $this->subscription->id,
            'tenant_organization_id' => $this->tenant->id,
            'resource_type' => 'storage',
        ]);

        $history = $this->service->getUsageHistory($this->subscription, 'users');

        $this->assertEquals(2, $history['total']);
        $this->assertEquals('users', $history['resource_type']);
    }
    #[Test]
    public function can_filter_usage_history_by_date_range()
    {
        $startDate = now()->subDays(10);
        $endDate = now()->subDays(5);

        ResourceUsageLog::factory()->create([
            'subscription_id' => $this->subscription->id,
            'tenant_organization_id' => $this->tenant->id,
            'recorded_at' => now()->subDays(15), // Outside range
        ]);

        ResourceUsageLog::factory()->create([
            'subscription_id' => $this->subscription->id,
            'tenant_organization_id' => $this->tenant->id,
            'recorded_at' => now()->subDays(7), // Inside range
        ]);

        $history = $this->service->getUsageHistory(
            $this->subscription,
            null,
            $startDate,
            $endDate
        );

        $this->assertEquals(1, $history['total']);
    }
    #[Test]
    public function can_get_usage_trends()
    {
        // Create logs with increasing usage
        ResourceUsageLog::factory()->create([
            'subscription_id' => $this->subscription->id,
            'tenant_organization_id' => $this->tenant->id,
            'resource_type' => 'users',
            'usage_percentage' => 40,
            'recorded_at' => now()->subDays(5),
        ]);

        ResourceUsageLog::factory()->create([
            'subscription_id' => $this->subscription->id,
            'tenant_organization_id' => $this->tenant->id,
            'resource_type' => 'users',
            'usage_percentage' => 60,
            'recorded_at' => now()->subDays(2),
        ]);

        $trends = $this->service->getUsageTrends($this->subscription, 'users', 7);

        $this->assertEquals('users', $trends['resource_type']);
        $this->assertEquals(2, $trends['data_points']);
        $this->assertEquals('increasing', $trends['trend_direction']);
        $this->assertEquals(60, $trends['current_usage']);
    }
    #[Test]
    public function detects_decreasing_trend()
    {
        ResourceUsageLog::factory()->create([
            'subscription_id' => $this->subscription->id,
            'tenant_organization_id' => $this->tenant->id,
            'resource_type' => 'storage',
            'usage_percentage' => 80,
            'recorded_at' => now()->subDays(5),
        ]);

        ResourceUsageLog::factory()->create([
            'subscription_id' => $this->subscription->id,
            'tenant_organization_id' => $this->tenant->id,
            'resource_type' => 'storage',
            'usage_percentage' => 50,
            'recorded_at' => now(),
        ]);

        $trends = $this->service->getUsageTrends($this->subscription, 'storage', 7);

        $this->assertEquals('decreasing', $trends['trend_direction']);
    }
    #[Test]
    public function can_get_latest_usage_snapshot()
    {
        ResourceUsageLog::factory()->create([
            'subscription_id' => $this->subscription->id,
            'tenant_organization_id' => $this->tenant->id,
            'resource_type' => 'users',
            'usage_value' => 5,
            'limit_value' => 10,
            'usage_percentage' => 50,
        ]);

        ResourceUsageLog::factory()->create([
            'subscription_id' => $this->subscription->id,
            'tenant_organization_id' => $this->tenant->id,
            'resource_type' => 'storage',
            'usage_value' => 20,
            'limit_value' => 100,
            'usage_percentage' => 20,
        ]);

        $snapshot = $this->service->getLatestUsageSnapshot($this->subscription);

        $this->assertEquals($this->subscription->id, $snapshot['subscription_id']);
        $this->assertArrayHasKey('users', $snapshot['resources']);
        $this->assertArrayHasKey('storage', $snapshot['resources']);
        $this->assertEquals(50, $snapshot['resources']['users']['usage_percentage']);
    }
    #[Test]
    public function can_cleanup_old_logs()
    {
        // Create old logs
        ResourceUsageLog::factory()->count(5)->create([
            'subscription_id' => $this->subscription->id,
            'tenant_organization_id' => $this->tenant->id,
            'recorded_at' => now()->subDays(100),
        ]);

        // Create recent logs
        ResourceUsageLog::factory()->count(3)->create([
            'subscription_id' => $this->subscription->id,
            'tenant_organization_id' => $this->tenant->id,
            'recorded_at' => now()->subDays(10),
        ]);

        $deleted = $this->service->cleanupOldLogs(90);

        $this->assertEquals(5, $deleted);
        $this->assertEquals(3, ResourceUsageLog::count());
    }
    #[Test]
    public function can_get_alert_summary()
    {
        ResourceUsageLog::factory()->create([
            'subscription_id' => $this->subscription->id,
            'tenant_organization_id' => $this->tenant->id,
            'resource_type' => 'users',
            'alert_level' => 'warning',
            'recorded_at' => now()->subDays(3),
        ]);

        ResourceUsageLog::factory()->create([
            'subscription_id' => $this->subscription->id,
            'tenant_organization_id' => $this->tenant->id,
            'resource_type' => 'storage',
            'alert_level' => 'critical',
            'recorded_at' => now()->subDays(2),
        ]);

        ResourceUsageLog::factory()->create([
            'subscription_id' => $this->subscription->id,
            'tenant_organization_id' => $this->tenant->id,
            'resource_type' => 'sub_orgs',
            'alert_level' => 'normal',
            'recorded_at' => now()->subDay(),
        ]);

        $summary = $this->service->getAlertSummary($this->tenant);

        $this->assertTrue($summary['has_subscription']);
        $this->assertEquals(3, $summary['total_logs']);
        $this->assertArrayHasKey('warning', $summary['alerts_by_level']);
        $this->assertArrayHasKey('critical', $summary['alerts_by_level']);
        $this->assertEquals(2, $summary['risk_count']);
    }
    #[Test]
    public function alert_summary_handles_no_subscription()
    {
        $noSubOrg = Organization::factory()->create([
            'type' => 'tenant',
            'parent_id' => $this->tenant->parent_id,
        ]);

        $summary = $this->service->getAlertSummary($noSubOrg);

        $this->assertFalse($summary['has_subscription']);
        $this->assertArrayHasKey('message', $summary);
    }
    #[Test]
    public function can_log_all_active_subscriptions()
    {
        // Create additional subscription
        $anotherTenant = Organization::factory()->create([
            'type' => 'tenant',
            'parent_id' => $this->tenant->parent_id,
        ]);

        Subscription::factory()->create([
            'organization_id' => $anotherTenant->id,
            'plan_id' => $this->plan->id,
            'status' => 'active',
        ]);

        $result = $this->service->logAllActiveSubscriptions();

        $this->assertEquals(2, $result['total']);
        $this->assertEquals(2, $result['success']);
        $this->assertEquals(0, $result['failed']);
    }
    #[Test]
    public function does_not_log_usage_for_unlimited_resources()
    {
        $unlimitedPlan = Plan::factory()->create([
            'user_limit' => 0, // Unlimited
            'sub_org_limit' => 0,
            'storage_limit' => 0,
            'hierarchy_depth_limit' => 0,
        ]);

        $unlimitedSub = Subscription::factory()->create([
            'organization_id' => $this->tenant->id,
            'plan_id' => $unlimitedPlan->id,
            'status' => 'active',
        ]);

        $result = $this->service->logResourceUsage($unlimitedSub);

        $this->assertTrue($result['success']);
        $this->assertEmpty($result['logs']); // No logs for unlimited resources
    }
    #[Test]
    public function usage_history_respects_limit_parameter()
    {
        ResourceUsageLog::factory()->count(50)->create([
            'subscription_id' => $this->subscription->id,
            'tenant_organization_id' => $this->tenant->id,
        ]);

        $history = $this->service->getUsageHistory($this->subscription, null, null, null, 10);

        $this->assertCount(10, $history['logs']);
    }
    #[Test]
    public function trends_calculate_average_and_peak_usage()
    {
        ResourceUsageLog::factory()->create([
            'subscription_id' => $this->subscription->id,
            'tenant_organization_id' => $this->tenant->id,
            'resource_type' => 'users',
            'usage_percentage' => 40,
            'recorded_at' => now()->subDays(5),
        ]);

        ResourceUsageLog::factory()->create([
            'subscription_id' => $this->subscription->id,
            'tenant_organization_id' => $this->tenant->id,
            'resource_type' => 'users',
            'usage_percentage' => 80,
            'recorded_at' => now()->subDays(3),
        ]);

        ResourceUsageLog::factory()->create([
            'subscription_id' => $this->subscription->id,
            'tenant_organization_id' => $this->tenant->id,
            'resource_type' => 'users',
            'usage_percentage' => 60,
            'recorded_at' => now(),
        ]);

        $trends = $this->service->getUsageTrends($this->subscription, 'users', 7);

        $this->assertEquals(60, $trends['average_usage']);
        $this->assertEquals(80, $trends['peak_usage']);
    }
}
