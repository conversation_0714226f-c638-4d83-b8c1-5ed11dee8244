<?php

namespace App\Modules\RolesPermissions\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\RolesPermissions\Domain\Models\RoleAssignment;
use Illuminate\Http\Request;

class RoleAssignmentController extends Controller
{
    public function index(Request $request)
    {
        $q = RoleAssignment::query()->where('organization_id', $request->user()->organization_id);
        return response()->json($q->latest()->paginate(15));
    }

    public function store(Request $request)
    {
        $data = $request->validate([
            'user_id' => ['required','uuid'],
            'role_id' => ['required','uuid'],
            'expires_at' => ['nullable','date'],
            'scope' => ['nullable','string','max:50'],
            'scope_id' => ['nullable','string','max:100'],
        ]);
        $data['organization_id'] = $request->user()->organization_id;
        $data['assigned_by'] = $request->user()->id;
        $data['assigned_at'] = now();
        $ra = RoleAssignment::create($data);
        return response()->json($ra, 201);
    }

    public function show(Request $request, string $id)
    {
        $ra = RoleAssignment::where('organization_id', $request->user()->organization_id)->findOrFail($id);
        return response()->json($ra);
    }

    public function update(Request $request, string $id)
    {
        $ra = RoleAssignment::where('organization_id', $request->user()->organization_id)->findOrFail($id);
        $data = $request->validate([
            'expires_at' => ['sometimes','nullable','date'],
            'scope' => ['sometimes','nullable','string','max:50'],
            'scope_id' => ['sometimes','nullable','string','max:100'],
        ]);
        $ra->update($data);
        return response()->json($ra);
    }

    public function destroy(Request $request, string $id)
    {
        $ra = RoleAssignment::where('organization_id', $request->user()->organization_id)->findOrFail($id);
        $ra->delete();
        return response()->json(['deleted' => true]);
    }
}
