<?php

namespace App\Modules\Users\Domain\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use App\Modules\Shared\Domain\Traits\HasUUID;
use App\Modules\Shared\Domain\Traits\HasOrganizationId;
use App\Modules\Organizations\Domain\Models\Organization;
use App\Modules\RolesPermissions\Domain\Models\Role;
use App\Modules\RolesPermissions\Domain\Models\Permission;

class User extends Authenticatable
{
    use HasFactory, Notifiable, SoftDeletes, HasUUID, HasOrganizationId;

    protected $table = 'users';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'organization_id',
        'name',
        'email',
        'mobile',
        'password',
        'status',
        'approval_request_id',
        'email_verified_at',
        'mobile_verified_at',
        'mfa_enabled',
        'mfa_secret',
        'failed_login_attempts',
        'locked_until',
        'last_login_at',
        'last_login_ip',
        'avatar',
        'timezone',
        'language',
        'preferences',
    ];

    protected $hidden = [
        'password',
        'remember_token',
        'mfa_secret',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'mobile_verified_at' => 'datetime',
        'mfa_enabled' => 'boolean',
        'failed_login_attempts' => 'integer',
        'locked_until' => 'datetime',
        'last_login_at' => 'datetime',
        'preferences' => 'array',
        'password' => 'hashed',
    ];

    // Relationships
    public function organization()
    {
        return $this->belongsTo(Organization::class, 'organization_id');
    }

    public function roles()
    {
        return $this->belongsToMany(
            Role::class,
            'role_assignments',
            'user_id',
            'role_id'
        )->withTimestamps()->withPivot('assigned_at', 'expires_at', 'scope', 'scope_id');
    }

    public function permissions()
    {
        return $this->belongsToMany(
            Permission::class,
            'user_permissions',
            'user_id',
            'permission_id'
        )->withTimestamps()->withPivot('granted_at', 'expires_at');
    }

    public function profile()
    {
        return $this->hasOne(UserProfile::class, 'user_id');
    }

    public function credentials()
    {
        return $this->hasMany(UserCredential::class, 'user_id');
    }

    public function sessions()
    {
        return $this->hasMany(UserSession::class, 'user_id');
    }

    public function loginAudits()
    {
        return $this->hasMany(LoginAuditTrail::class, 'user_id');
    }

    public function approvalRequest()
    {
        return $this->belongsTo(
            \App\Modules\Approvals\Domain\Models\ApprovalRequest::class,
            'approval_request_id'
        );
    }

    // Helper methods
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    public function isLocked(): bool
    {
        return $this->locked_until && $this->locked_until->isFuture();
    }

    public function hasRole(string $roleName): bool
    {
        return $this->roles()->where('slug', $roleName)->exists();
    }

    public function hasPermission(string $permissionSlug): bool
    {
        return $this->permissions()->where('slug', $permissionSlug)->exists()
            || $this->roles()->whereHas('permissions', function ($query) use ($permissionSlug) {
                $query->where('slug', $permissionSlug);
            })->exists();
    }

    public function isPendingApproval(): bool
    {
        return $this->status === 'pending' && !is_null($this->approval_request_id);
    }
}
