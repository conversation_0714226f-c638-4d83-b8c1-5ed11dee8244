<?php

namespace App\Modules\Approvals\Domain\Events;

use App\Modules\Shared\Domain\Events\DomainEvent;

class ApprovalRequested extends DomainEvent
{
    public function __construct(public readonly string $approvalRequestId, ?string $organizationId = null, ?string $userId = null, array $payload = [])
    {
        parent::__construct($organizationId, $userId, array_merge($payload, ['approval_request_id' => $approvalRequestId]));
    }
}
