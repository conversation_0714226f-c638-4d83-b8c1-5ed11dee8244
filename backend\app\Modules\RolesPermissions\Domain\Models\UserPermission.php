<?php

namespace App\Modules\RolesPermissions\Domain\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Modules\Shared\Domain\Traits\HasUUID;
use App\Modules\Shared\Domain\Traits\HasOrganizationId;

class UserPermission extends Model
{
    use HasFactory, HasUUID, HasOrganizationId;

    protected $table = 'user_permissions';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'organization_id', 'user_id', 'permission_id', 'granted_by', 'granted_at', 'expires_at'
    ];

    protected $casts = [
        'granted_at' => 'datetime',
        'expires_at' => 'datetime',
    ];

    public function permission()
    {
        return $this->belongsTo(Permission::class, 'permission_id');
    }
}
