import client from './client'

export const organizationsAPI = {
  // Organizations CRUD
  getOrganizations: (params) => client.get('/organizations', { params }),
  createOrganization: (data) => client.post('/organizations', data),
  getOrganization: (id) => client.get(`/organizations/${id}`),
  updateOrganization: (id, data) => client.patch(`/organizations/${id}`, data),
  deleteOrganization: (id) => client.delete(`/organizations/${id}`),
  
  // Organization features
  getHierarchy: (id) => client.get(`/organizations/${id}/hierarchy`),
  getMembers: (id, params) => client.get(`/organizations/${id}/members`, { params }),
  
  // Resource usage
  getUsage: (organizationId) => client.get(`/organizations/${organizationId}/usage`),
  getUsageAlerts: (organizationId) => client.get(`/organizations/${organizationId}/usage/alerts`),
}
