<?php

namespace App\Modules\Billing\Domain\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Modules\Shared\Domain\Traits\HasUUID;

class Plan extends Model
{
    use HasFactory, SoftDeletes, HasUUID;

    protected $table = 'plans';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'name',
        'slug',
        'description',
        'price',
        'yearly_price',
        'billing_period',
        'user_limit',
        'storage_limit',
        'sub_org_limit',
        'hierarchy_depth_limit',
        'api_calls_limit',
        'modules',
        'features', // deprecated, use modules
        'is_active',
        'trial_days',
        'sort_order',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'yearly_price' => 'decimal:2',
        'user_limit' => 'integer',
        'storage_limit' => 'integer',
        'sub_org_limit' => 'integer',
        'hierarchy_depth_limit' => 'integer',
        'api_calls_limit' => 'integer',
        'modules' => 'array',
        'features' => 'array', // deprecated
        'is_active' => 'boolean',
        'trial_days' => 'integer',
        'sort_order' => 'integer',
    ];

    // Relationships
    public function subscriptions()
    {
        return $this->hasMany(Subscription::class, 'plan_id');
    }

    // Helper methods
    public function isUnlimited(): bool
    {
        return $this->user_limit === 0;
    }

    public function hasTrial(): bool
    {
        return $this->trial_days > 0;
    }

    public function hasModule(string $moduleName): bool
    {
        return in_array($moduleName, $this->modules ?? []);
    }

    public function getSubOrgAddOnPrice(): int
    {
        return match($this->slug) {
            'basic' => 1500,
            'pro' => 2000,
            'enterprise' => 2500,
            default => 2000,
        };
    }

    public function getUserAddOnPrice(): int
    {
        return 500; // ₹500 per user per month
    }

    public function getStorageAddOnPrice(): int
    {
        return 100; // ₹100 per GB per month
    }

    public function getHierarchyLevelAddOnPrice(): int
    {
        return 2000; // ₹2000 per level per month
    }

    public function getModuleAddOnPrice(): int
    {
        return 3000; // ₹3000 per module per month
    }

    public function getApiCallsAddOnPrice(): int
    {
        return 5000; // ₹5000 per 50K calls per month
    }
}
