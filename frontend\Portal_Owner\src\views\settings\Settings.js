import React from 'react'
import { <PERSON><PERSON>, <PERSON>ard<PERSON>ody, CCardHeader, CCol, CRow, CForm, CFormLabel, CFormInput, CButton } from '@coreui/react'

const Settings = () => {
  return (
    <CRow>
      <CCol xs={12} md={8}>
        <CCard className="mb-4">
          <CCardHeader>
            <strong>System Settings</strong>
          </CCardHeader>
          <CCardBody>
            <CForm>
              <div className="mb-3">
                <CFormLabel htmlFor="appName">Application Name</CFormLabel>
                <CFormInput id="appName" placeholder="Enter application name" />
              </div>
              <div className="mb-3">
                <CFormLabel htmlFor="appEmail">Support Email</CFormLabel>
                <CFormInput id="appEmail" type="email" placeholder="Enter support email" />
              </div>
              <div className="mb-3">
                <CFormLabel htmlFor="appPhone">Support Phone</CFormLabel>
                <CFormInput id="appPhone" placeholder="Enter support phone" />
              </div>
              <div className="mb-3">
                <CFormLabel htmlFor="appTimezone">Timezone</CFormLabel>
                <CFormInput id="appTimezone" placeholder="Select timezone" />
              </div>
              <div className="d-grid gap-2">
                <CButton color="primary">Save Settings</CButton>
              </div>
            </CForm>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  )
}

export default Settings
