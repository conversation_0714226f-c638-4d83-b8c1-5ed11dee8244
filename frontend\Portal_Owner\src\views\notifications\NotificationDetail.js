import React, { useState, useEffect } from 'react'
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom'
import { CCard, CCardBody, CCardHeader, CCol, CRow, CForm, CFormLabel, CFormInput, CFormTextarea, CButton, CSpinner, CAlert } from '@coreui/react'
import { notificationsAPI } from '../../api/notifications'

const NotificationDetail = () => {
  const { id } = useParams()
  const navigate = useNavigate()
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [notification, setNotification] = useState({ message: '', type: '', read_at: null })
  const [saving, setSaving] = useState(false)

  useEffect(() => {
    if (id) {
      loadNotification()
    }
  }, [id])

  const loadNotification = async () => {
    try {
      setLoading(true)
      const response = await notificationsAPI.getNotification(id)
      // Backend returns object directly from JsonResource
      setNotification(response.data)
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to load notification')
    } finally {
      setLoading(false)
    }
  }

  const handleMarkAsRead = async () => {
    try {
      setSaving(true)
      await notificationsAPI.markAsRead(id)
      navigate('/notifications')
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to mark as read')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return <CRow><CCol xs={12} className="text-center"><CSpinner color="primary" /></CCol></CRow>
  }

  return (
    <CRow>
      <CCol xs={12} md={8}>
        <CCard>
          <CCardHeader>
            <strong>Notification Details</strong>
          </CCardHeader>
          <CCardBody>
            {error && <CAlert color="danger">{error}</CAlert>}
            <CForm>
              <div className="mb-3">
                <CFormLabel htmlFor="type">Type</CFormLabel>
                <CFormInput id="type" value={notification.type} disabled />
              </div>
              <div className="mb-3">
                <CFormLabel htmlFor="message">Message</CFormLabel>
                <CFormTextarea id="message" value={notification.message} disabled rows="4" />
              </div>
              <div className="mb-3">
                <CFormLabel htmlFor="status">Status</CFormLabel>
                <CFormInput id="status" value={notification.read_at ? 'Read' : 'Unread'} disabled />
              </div>
              {!notification.read_at && (
                <CButton type="button" color="primary" onClick={handleMarkAsRead} disabled={saving} className="me-2">
                  {saving ? <CSpinner size="sm" className="me-2" /> : null}
                  Mark as Read
                </CButton>
              )}
              <CButton type="button" color="secondary" onClick={() => navigate('/notifications')}>
                Back
              </CButton>
            </CForm>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  )
}

export default NotificationDetail
