<?php

namespace App\Modules\Billing\Domain\Services;

use App\Modules\Shared\Domain\Services\BaseService;
use App\Modules\Organizations\Domain\Models\Organization;

class IndianGSTService extends BaseService
{
    /**
     * Indian state codes for GST
     */
    private const STATE_CODES = [
        '01' => 'Jammu and Kashmir',
        '02' => 'Himachal Pradesh',
        '03' => 'Punjab',
        '04' => 'Chandigarh',
        '05' => 'Uttarakhand',
        '06' => 'Haryana',
        '07' => 'Delhi',
        '08' => 'Rajasthan',
        '09' => 'Uttar Pradesh',
        '10' => 'Bihar',
        '11' => 'Sikkim',
        '12' => 'Arunachal Pradesh',
        '13' => 'Nagaland',
        '14' => 'Manipur',
        '15' => 'Mizoram',
        '16' => 'Tripura',
        '17' => 'Meghalaya',
        '18' => 'Assam',
        '19' => 'West Bengal',
        '20' => 'Jharkhand',
        '21' => 'Odisha',
        '22' => 'Chhattisgarh',
        '23' => 'Madhya Pradesh',
        '24' => 'Gujarat',
        '26' => 'Dadra and Nagar Haveli and Daman and Diu',
        '27' => 'Maharashtra',
        '28' => 'Andhra Pradesh',
        '29' => 'Karnataka',
        '30' => 'Goa',
        '31' => 'Lakshadweep',
        '32' => 'Kerala',
        '33' => 'Tamil Nadu',
        '34' => 'Puducherry',
        '35' => 'Andaman and Nicobar Islands',
        '36' => 'Telangana',
        '37' => 'Andhra Pradesh (New)',
        '38' => 'Ladakh',
    ];

    /**
     * Calculate GST for an amount
     *
     * @param float $amount Taxable amount (before GST)
     * @param string $supplierStateCode Supplier's state code
     * @param string $customerStateCode Customer's state code
     * @param float $gstRate GST rate (default 18%)
     * @return array GST calculation breakdown
     */
    public function calculateGST(
        float $amount,
        string $supplierStateCode,
        string $customerStateCode,
        float $gstRate = 18.00
    ): array {
        $isIntraState = $supplierStateCode === $customerStateCode;
        
        if ($isIntraState) {
            // Intra-state: CGST + SGST (split equally)
            $halfRate = $gstRate / 2;
            $cgst = round(($amount * $halfRate) / 100, 2);
            $sgst = round(($amount * $halfRate) / 100, 2);
            $igst = 0;
        } else {
            // Inter-state: IGST only
            $cgst = 0;
            $sgst = 0;
            $igst = round(($amount * $gstRate) / 100, 2);
        }
        
        $totalTax = $cgst + $sgst + $igst;
        $totalAmount = $amount + $totalTax;
        
        return [
            'taxable_amount' => round($amount, 2),
            'gst_rate' => $gstRate,
            'is_intra_state' => $isIntraState,
            
            // CGST details
            'cgst_rate' => $isIntraState ? $halfRate : 0,
            'cgst' => $cgst,
            
            // SGST details
            'sgst_rate' => $isIntraState ? $halfRate : 0,
            'sgst' => $sgst,
            
            // IGST details
            'igst_rate' => !$isIntraState ? $gstRate : 0,
            'igst' => $igst,
            
            // Totals
            'total_tax' => $totalTax,
            'total_amount' => round($totalAmount, 2),
            
            // Place of supply
            'place_of_supply' => $this->getStateName($customerStateCode),
            'supply_type' => $isIntraState ? 'Intra-State' : 'Inter-State',
        ];
    }

    /**
     * Validate GSTIN format and checksum
     *
     * @param string $gstin GSTIN to validate
     * @return bool
     */
    public function validateGSTIN(string $gstin): bool
    {
        // GSTIN format: 22AAAAA0000A1Z5
        // 2 digits state code + 10 digits PAN + 1 digit entity + 1 digit Z + 1 check digit
        $pattern = '/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/';
        
        if (!preg_match($pattern, $gstin)) {
            return false;
        }
        
        // Validate state code
        $stateCode = substr($gstin, 0, 2);
        if (!isset(self::STATE_CODES[$stateCode])) {
            return false;
        }
        
        // Validate PAN portion (characters 3-12)
        $pan = substr($gstin, 2, 10);
        $panPattern = '/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/';
        if (!preg_match($panPattern, $pan)) {
            return false;
        }
        
        return true;
    }

    /**
     * Validate PAN format
     *
     * @param string $pan PAN to validate
     * @return bool
     */
    public function validatePAN(string $pan): bool
    {
        // PAN format: **********
        // 5 letters + 4 numbers + 1 letter
        $pattern = '/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/';
        return preg_match($pattern, $pan) === 1;
    }

    /**
     * Extract state code from GSTIN
     *
     * @param string $gstin
     * @return string|null
     */
    public function extractStateCode(string $gstin): ?string
    {
        if (strlen($gstin) >= 2) {
            return substr($gstin, 0, 2);
        }
        return null;
    }

    /**
     * Get state name from code
     *
     * @param string $code
     * @return string|null
     */
    public function getStateName(string $code): ?string
    {
        return self::STATE_CODES[$code] ?? null;
    }

    /**
     * Get all state codes
     *
     * @return array
     */
    public function getAllStates(): array
    {
        return self::STATE_CODES;
    }

    /**
     * Get GST rate for service type
     *
     * @param string $serviceType
     * @return float
     */
    public function getGSTRate(string $serviceType = 'software'): float
    {
        // As per Indian GST rates
        return match($serviceType) {
            'software', 'saas', 'it_services' => 18.00,
            'consulting', 'professional_services' => 18.00,
            'training', 'education' => 18.00,
            default => 18.00
        };
    }

    /**
     * Check if reverse charge is applicable
     *
     * @param Organization $supplier
     * @param Organization $customer
     * @return bool
     */
    public function isReverseChargeApplicable(Organization $supplier, Organization $customer): bool
    {
        // Reverse charge applies when:
        // 1. Supplier is unregistered and customer is registered
        // 2. Specified services under GST law
        
        $supplierRegistered = !empty($supplier->gstin);
        $customerRegistered = !empty($customer->gstin);
        
        // If supplier not registered and customer is registered
        if (!$supplierRegistered && $customerRegistered) {
            return true;
        }
        
        return false;
    }

    /**
     * Format amount in Indian currency format
     *
     * @param float $amount
     * @return string
     */
    public function formatIndianCurrency(float $amount): string
    {
        // Indian numbering system: 1,00,000.00
        $amount = number_format($amount, 2, '.', '');
        $parts = explode('.', $amount);
        $integer = $parts[0];
        $decimal = $parts[1] ?? '00';
        
        // Format integer part with Indian comma pattern
        $lastThree = substr($integer, -3);
        $otherNumbers = substr($integer, 0, -3);
        
        if ($otherNumbers != '') {
            $lastThree = ',' . $lastThree;
        }
        
        $formatted = preg_replace('/\B(?=(\d{2})+(?!\d))/', ',', $otherNumbers) . $lastThree;
        
        return '₹ ' . $formatted . '.' . $decimal;
    }

    /**
     * Convert number to words (Indian format)
     *
     * @param float $number
     * @return string
     */
    public function numberToWords(float $number): string
    {
        $ones = ['', 'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine'];
        $teens = ['Ten', 'Eleven', 'Twelve', 'Thirteen', 'Fourteen', 'Fifteen', 'Sixteen', 'Seventeen', 'Eighteen', 'Nineteen'];
        $tens = ['', '', 'Twenty', 'Thirty', 'Forty', 'Fifty', 'Sixty', 'Seventy', 'Eighty', 'Ninety'];
        
        $rupees = floor($number);
        $paise = round(($number - $rupees) * 100);
        
        $words = $this->convertToWords($rupees, $ones, $teens, $tens);
        $result = "Rupees " . $words;
        
        if ($paise > 0) {
            $paiseWords = $this->convertToWords($paise, $ones, $teens, $tens);
            $result .= " and " . $paiseWords . " Paise";
        }
        
        return $result . " Only";
    }

    /**
     * Helper method to convert number to words
     */
    private function convertToWords(int $number, array $ones, array $teens, array $tens): string
    {
        if ($number == 0) return 'Zero';
        
        $words = '';
        
        // Crore
        if ($number >= 10000000) {
            $words .= $this->convertToWords(floor($number / 10000000), $ones, $teens, $tens) . ' Crore ';
            $number %= 10000000;
        }
        
        // Lakh
        if ($number >= 100000) {
            $words .= $this->convertToWords(floor($number / 100000), $ones, $teens, $tens) . ' Lakh ';
            $number %= 100000;
        }
        
        // Thousand
        if ($number >= 1000) {
            $words .= $this->convertToWords(floor($number / 1000), $ones, $teens, $tens) . ' Thousand ';
            $number %= 1000;
        }
        
        // Hundred
        if ($number >= 100) {
            $words .= $ones[floor($number / 100)] . ' Hundred ';
            $number %= 100;
        }
        
        // Tens and ones
        if ($number >= 20) {
            $words .= $tens[floor($number / 10)] . ' ';
            $number %= 10;
        } elseif ($number >= 10) {
            $words .= $teens[$number - 10] . ' ';
            $number = 0;
        }
        
        if ($number > 0) {
            $words .= $ones[$number] . ' ';
        }
        
        return trim($words);
    }
}
