<?php

namespace Tests\Unit\Billing;

use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;
use Illuminate\Foundation\Testing\DatabaseMigrations;
use App\Modules\Organizations\Domain\Models\Organization;
use App\Modules\Billing\Domain\Models\Plan;
use App\Modules\Billing\Domain\Models\Subscription;
use App\Modules\Billing\Domain\Models\SubscriptionAddOn;
use App\Modules\Billing\Application\Services\BillingService;
use Carbon\Carbon;

class BillingServiceTest extends TestCase
{
    use DatabaseMigrations;

    protected BillingService $service;
    protected Organization $tenant;
    protected Plan $plan;
    protected Subscription $subscription;

    protected function setUp(): void
    {
        parent::setUp();

        Carbon::setTestNow(); // Reset time
        $this->service = app(BillingService::class);

        $portalOwner = Organization::factory()->create([
            'type' => 'portal_owner',
            'parent_id' => null,
        ]);

        $this->tenant = Organization::factory()->create([
            'type' => 'tenant',
            'parent_id' => $portalOwner->id,
        ]);

        $this->plan = Plan::factory()->create([
            'name' => 'Test Plan',
            'slug' => 'test',
            'price' => 10000,
            'user_limit' => 5,
            'sub_org_limit' => 2,
            'storage_limit' => 10,
            'modules' => ['inventory', 'accounting'],
        ]);

        $this->subscription = Subscription::factory()->create([
            'organization_id' => $this->tenant->id,
            'plan_id' => $this->plan->id,
            'status' => 'active',
            'price' => $this->plan->price,
            'starts_at' => now()->startOfMonth(),
        ]);
    }

    protected function tearDown(): void
    {
        Carbon::setTestNow(); // Reset time after each test
        parent::tearDown();
    }
    #[Test]
    public function can_purchase_add_on()
    {
        $result = $this->service->purchaseAddOn($this->subscription, 'user', 5);

        $this->assertTrue($result['success']);
        $this->assertInstanceOf(SubscriptionAddOn::class, $result['add_on']);
        $this->assertEquals(2500, $result['total_price']); // 500 * 5
        $this->assertDatabaseHas('subscription_add_ons', [
            'subscription_id' => $this->subscription->id,
            'add_on_type' => 'user',
            'quantity' => 5,
        ]);
    }
    #[Test]
    public function cannot_purchase_invalid_add_on_type()
    {
        $result = $this->service->purchaseAddOn($this->subscription, 'invalid_type', 1);

        $this->assertFalse($result['success']);
        $this->assertStringContainsString('Invalid add-on type', $result['message']);
    }
    #[Test]
    public function purchase_add_on_calculates_prorated_amount()
    {
        Carbon::setTestNow(Carbon::create(2025, 1, 15)); // Mid-month

        $result = $this->service->purchaseAddOn($this->subscription, 'storage', 10);

        $this->assertTrue($result['success']);
        $this->assertNotEquals($result['total_price'], $result['prorated_amount']);
        $this->assertLessThan($result['total_price'], $result['prorated_amount']);
    }
    #[Test]
    public function purchase_add_on_on_first_day_has_no_proration()
    {
        Carbon::setTestNow(Carbon::create(2025, 1, 1));

        $result = $this->service->purchaseAddOn($this->subscription, 'storage', 10);

        $this->assertTrue($result['success']);
        $this->assertEquals($result['total_price'], $result['prorated_amount']);
    }
    #[Test]
    public function can_cancel_add_on()
    {
        $addOn = SubscriptionAddOn::factory()->create([
            'subscription_id' => $this->subscription->id,
            'add_on_type' => 'user',
            'starts_at' => now(),
            'ends_at' => null,
        ]);

        $result = $this->service->cancelAddOn($addOn);

        $this->assertTrue($result['success']);
        $addOn->refresh();
        $this->assertNotNull($addOn->ends_at);
    }
    #[Test]
    public function can_specify_custom_end_date_when_cancelling()
    {
        $addOn = SubscriptionAddOn::factory()->create([
            'subscription_id' => $this->subscription->id,
            'add_on_type' => 'user',
            'starts_at' => now(),
            'ends_at' => null,
        ]);

        $futureDate = now()->addDays(30);
        $result = $this->service->cancelAddOn($addOn, $futureDate);

        $this->assertTrue($result['success']);
        $addOn->refresh();
        $this->assertEquals($futureDate->toDateString(), $addOn->ends_at->toDateString());
    }
    #[Test]
    public function get_add_on_unit_price_returns_correct_prices()
    {
        $this->assertEquals(500, $this->service->getAddOnUnitPrice($this->plan, 'user'));
        $this->assertEquals(100, $this->service->getAddOnUnitPrice($this->plan, 'storage'));
        $this->assertIsInt($this->service->getAddOnUnitPrice($this->plan, 'sub_org'));
        $this->assertEquals(0, $this->service->getAddOnUnitPrice($this->plan, 'invalid'));
    }
    #[Test]
    public function calculate_prorated_amount_works_correctly()
    {
        $addOn = SubscriptionAddOn::factory()->create([
            'subscription_id' => $this->subscription->id,
            'total_price' => 3000,
        ]);

        $startDate = Carbon::create(2025, 1, 15); // 31 days in Jan, starting on 15th
        $proratedAmount = $this->service->calculateProratedAmount($addOn, $startDate);

        // 17 days remaining (15-31)
        $expectedAmount = (3000 / 31) * 17;
        $this->assertEquals($expectedAmount, $proratedAmount);
    }
    #[Test]
    public function calculate_monthly_total_includes_active_add_ons()
    {
        SubscriptionAddOn::factory()->create([
            'subscription_id' => $this->subscription->id,
            'add_on_type' => 'user',
            'total_price' => 2500,
            'starts_at' => now()->subDays(5),
            'ends_at' => null,
        ]);

        SubscriptionAddOn::factory()->create([
            'subscription_id' => $this->subscription->id,
            'add_on_type' => 'storage',
            'total_price' => 1000,
            'starts_at' => now()->subDays(10),
            'ends_at' => null,
        ]);

        $result = $this->service->calculateMonthlyTotal($this->subscription);

        $this->assertEquals(10000, $result['base_price']);
        $this->assertEquals(3500, $result['add_ons_total']);
        $this->assertEquals(13500, $result['grand_total']);
        $this->assertEquals(2, $result['active_add_ons_count']);
    }
    #[Test]
    public function calculate_monthly_total_excludes_inactive_add_ons()
    {
        // Active add-on
        SubscriptionAddOn::factory()->create([
            'subscription_id' => $this->subscription->id,
            'total_price' => 2500,
            'starts_at' => now()->subDays(5),
            'ends_at' => null,
        ]);

        // Not yet started
        SubscriptionAddOn::factory()->create([
            'subscription_id' => $this->subscription->id,
            'total_price' => 1000,
            'starts_at' => now()->addDays(5),
            'ends_at' => null,
        ]);

        // Already ended
        SubscriptionAddOn::factory()->create([
            'subscription_id' => $this->subscription->id,
            'total_price' => 1500,
            'starts_at' => now()->subDays(20),
            'ends_at' => now()->subDays(5),
        ]);

        $result = $this->service->calculateMonthlyTotal($this->subscription);

        $this->assertEquals(1, $result['active_add_ons_count']);
        $this->assertEquals(2500, $result['add_ons_total']);
    }
    #[Test]
    public function get_add_on_pricing_returns_all_prices()
    {
        $result = $this->service->getAddOnPricing($this->plan);

        $this->assertArrayHasKey('plan', $result);
        $this->assertArrayHasKey('add_on_prices', $result);
        $this->assertArrayHasKey('user', $result['add_on_prices']);
        $this->assertArrayHasKey('storage', $result['add_on_prices']);
        $this->assertArrayHasKey('sub_org', $result['add_on_prices']);
        $this->assertEquals(500, $result['add_on_prices']['user']['unit_price']);
    }
    #[Test]
    public function calculate_current_period_billing_works_correctly()
    {
        $this->subscription->update(['starts_at' => now()->startOfMonth()]);

        SubscriptionAddOn::factory()->create([
            'subscription_id' => $this->subscription->id,
            'total_price' => 3100,
            'starts_at' => now()->startOfMonth(),
            'ends_at' => null,
        ]);

        $result = $this->service->calculateCurrentPeriodBilling($this->subscription);

        $this->assertEquals(10000, $result['base_price']);
        $this->assertGreaterThan(0, $result['add_ons_total']);
        $this->assertGreaterThan(10000, $result['grand_total']);
        $this->assertCount(1, $result['add_ons']);
    }
    #[Test]
    public function calculate_current_period_billing_prorates_mid_month_add_ons()
    {
        Carbon::setTestNow(Carbon::create(2025, 1, 1));
        $this->subscription->update(['starts_at' => now()->startOfMonth()]);

        // Add-on starting mid-month
        SubscriptionAddOn::factory()->create([
            'subscription_id' => $this->subscription->id,
            'total_price' => 3100, // Full price
            'starts_at' => Carbon::create(2025, 1, 15),
            'ends_at' => null,
        ]);

        $result = $this->service->calculateCurrentPeriodBilling($this->subscription);

        $addOnCharge = $result['add_ons'][0]['prorated_charge'];
        $this->assertLessThan(3100, $addOnCharge); // Should be prorated
        $this->assertEqualsWithDelta(17, $result['add_ons'][0]['days_active'], 1); // 15-31 = 17 days (allow some float variance)
    }
    #[Test]
    public function get_active_add_ons_returns_only_active()
    {
        SubscriptionAddOn::factory()->create([
            'subscription_id' => $this->subscription->id,
            'starts_at' => now()->subDays(5),
            'ends_at' => null,
        ]);

        SubscriptionAddOn::factory()->create([
            'subscription_id' => $this->subscription->id,
            'starts_at' => now()->addDays(5),
            'ends_at' => null,
        ]);

        $result = $this->service->getActiveAddOns($this->subscription);

        $this->assertCount(1, $result);
    }
    #[Test]
    public function can_upgrade_add_on_quantity()
    {
        $addOn = SubscriptionAddOn::factory()->create([
            'subscription_id' => $this->subscription->id,
            'quantity' => 5,
            'unit_price' => 500,
            'total_price' => 2500,
        ]);

        $result = $this->service->upgradeAddOn($addOn, 3);

        $this->assertTrue($result['success']);
        $this->assertEquals(5, $result['old_quantity']);
        $this->assertEquals(8, $result['new_quantity']);
        $this->assertEquals(1500, $result['additional_charge']); // 500 * 3
        
        $addOn->refresh();
        $this->assertEquals(8, $addOn->quantity);
        $this->assertEquals(4000, $addOn->total_price); // 500 * 8
    }
    #[Test]
    public function get_add_on_usage_summary_returns_correct_data()
    {
        SubscriptionAddOn::factory()->create([
            'subscription_id' => $this->subscription->id,
            'add_on_type' => 'user',
            'quantity' => 5,
            'total_price' => 2500,
            'starts_at' => now()->subDays(5),
            'ends_at' => null,
        ]);

        SubscriptionAddOn::factory()->create([
            'subscription_id' => $this->subscription->id,
            'add_on_type' => 'user',
            'quantity' => 3,
            'total_price' => 1500,
            'starts_at' => now()->subDays(10),
            'ends_at' => null,
        ]);

        SubscriptionAddOn::factory()->create([
            'subscription_id' => $this->subscription->id,
            'add_on_type' => 'storage',
            'quantity' => 10,
            'total_price' => 1000,
            'starts_at' => now()->subDays(3),
            'ends_at' => null,
        ]);

        $result = $this->service->getAddOnUsageSummary($this->subscription);

        $this->assertEquals(3, $result['total_active_add_ons']);
        $this->assertEquals(5000, $result['monthly_add_on_cost']);
        $this->assertCount(2, $result['by_type']); // user and storage

        $userType = collect($result['by_type'])->firstWhere('type', 'user');
        $this->assertEquals(2, $userType['count']);
        $this->assertEquals(8, $userType['total_quantity']);
        $this->assertEquals(4000, $userType['total_cost']);
    }
    #[Test]
    public function get_add_on_usage_summary_handles_no_add_ons()
    {
        $result = $this->service->getAddOnUsageSummary($this->subscription);

        $this->assertEquals(0, $result['total_active_add_ons']);
        $this->assertEquals(0, $result['monthly_add_on_cost']);
        $this->assertEmpty($result['by_type']);
    }
    #[Test]
    public function purchase_add_on_stores_metadata()
    {
        $result = $this->service->purchaseAddOn($this->subscription, 'user', 5);

        $this->assertTrue($result['success']);
        $addOn = $result['add_on'];
        $this->assertNotNull($addOn->metadata);
        $this->assertArrayHasKey('purchased_at', $addOn->metadata);
    }
}
