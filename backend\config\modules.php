<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Enabled Modules
    |--------------------------------------------------------------------------
    |
    | List of enabled modules in the application
    |
    */
    'enabled' => [
        'Users',
        'Organizations',
        'Roles',
        'Permissions',
        'Billing',
        'Approvals',
        'Notifications',
        'Audit',
        'Reports',
        'Integration',
        'Settings',
    ],

    /*
    |--------------------------------------------------------------------------
    | Module Configurations
    |--------------------------------------------------------------------------
    */
    'Users' => [
        'enabled' => env('MODULE_USERS_ENABLED', true),
        'features' => [
            'mfa' => env('FEATURE_MFA_ENABLED', true),
            'email_verification' => env('FEATURE_EMAIL_VERIFICATION', true),
            'password_reset' => env('FEATURE_PASSWORD_RESET', true),
            'otp_login' => env('FEATURE_OTP_LOGIN', true),
        ],
        'settings' => [
            'max_login_attempts' => env('MAX_LOGIN_ATTEMPTS', 5),
            'session_timeout' => env('SESSION_TIMEOUT', 3600),
            'password_min_length' => env('PASSWORD_MIN_LENGTH', 8),
            'otp_expiry' => env('OTP_EXPIRY', 300), // 5 minutes
        ],
    ],

    'Billing' => [
        'enabled' => env('MODULE_BILLING_ENABLED', true),
        'features' => [
            'subscriptions' => env('FEATURE_SUBSCRIPTIONS', true),
            'payments' => env('FEATURE_PAYMENTS', true),
            'invoices' => env('FEATURE_INVOICES', true),
            'auto_renewal' => env('FEATURE_AUTO_RENEWAL', true),
        ],
        'settings' => [
            'payment_gateway' => env('PAYMENT_GATEWAY', 'razorpay'),
            'currency' => env('DEFAULT_CURRENCY', 'INR'),
            'tax_rate' => env('TAX_RATE', 18.0),
            'trial_days' => env('TRIAL_DAYS', 14),
        ],
    ],

    'Organizations' => [
        'enabled' => env('MODULE_ORGANIZATIONS_ENABLED', true),
        'settings' => [
            'max_hierarchy_depth' => env('MAX_HIERARCHY_DEPTH', 10),
            'allow_multi_tenant' => env('ALLOW_MULTI_TENANT', true),
        ],
    ],

    'Approvals' => [
        'enabled' => env('MODULE_APPROVALS_ENABLED', true),
        'settings' => [
            'max_approval_levels' => env('MAX_APPROVAL_LEVELS', 5),
            'auto_escalation_hours' => env('AUTO_ESCALATION_HOURS', 24),
        ],
    ],

    'Notifications' => [
        'enabled' => env('MODULE_NOTIFICATIONS_ENABLED', true),
        'channels' => [
            'email' => env('NOTIFICATION_EMAIL_ENABLED', true),
            'sms' => env('NOTIFICATION_SMS_ENABLED', false),
            'in_app' => env('NOTIFICATION_IN_APP_ENABLED', true),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Global Feature Flags
    |--------------------------------------------------------------------------
    */
    'features' => [
        'rate_limiting' => env('FEATURE_RATE_LIMITING', true),
        'api_versioning' => env('FEATURE_API_VERSIONING', true),
        'audit_logging' => env('FEATURE_AUDIT_LOGGING', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | API Configuration
    |--------------------------------------------------------------------------
    */
    'api' => [
        'version' => env('API_VERSION', 'v1'),
        'prefix' => env('API_PREFIX', 'api'),
        'rate_limit' => env('API_RATE_LIMIT', 60), // per minute
        'pagination' => [
            'default_per_page' => env('API_PAGINATION_DEFAULT', 15),
            'max_per_page' => env('API_PAGINATION_MAX', 100),
        ],
    ],
];
