<?php

namespace App\Modules\Notifications\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Notifications\Domain\Services\NotificationService;
use App\Modules\Notifications\Domain\Models\Notification;
use Illuminate\Http\Request;

class NotificationController extends Controller
{
    public function __construct(private readonly NotificationService $notificationService)
    {
    }

    /**
     * Get user's notifications
     */
    public function index(Request $request)
    {
        try {
            $filters = [
                'type' => $request->input('type'),
                'read' => $request->boolean('read'),
            ];

            $notifications = $this->notificationService->getNotifications(
                auth()->user(),
                array_filter($filters, fn($v) => $v !== null)
            );

            return response()->json([
                'data' => $notifications,
                'count' => $notifications->count(),
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Get notification details
     */
    public function show(string $id)
    {
        try {
            $notification = Notification::findOrFail($id);

            if ($notification->user_id !== auth()->id() || $notification->organization_id !== auth()->user()->organization_id) {
                return response()->json(['error' => 'Notification not found'], 404);
            }

            return response()->json($notification);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json(['error' => 'Notification not found'], 404);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Mark notification as read
     */
    public function markAsRead(string $id)
    {
        try {
            $notification = Notification::findOrFail($id);

            if ($notification->user_id !== auth()->id() || $notification->organization_id !== auth()->user()->organization_id) {
                return response()->json(['error' => 'Notification not found'], 404);
            }

            $marked = $this->notificationService->markAsRead($notification);

            return response()->json(['read' => true, 'read_at' => $marked->read_at]);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json(['error' => 'Notification not found'], 404);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }
}
