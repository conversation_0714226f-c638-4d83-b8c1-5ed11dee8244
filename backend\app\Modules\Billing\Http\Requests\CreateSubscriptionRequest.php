<?php

namespace App\Modules\Billing\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CreateSubscriptionRequest extends FormRequest
{
    public function authorize(): bool { return true; }
    public function rules(): array
    {
        return [
            'organization_id' => ['required','uuid'],
            'plan_id' => ['required','uuid'],
            'billing_period' => ['required','in:monthly,yearly'],
            'auto_renew' => ['sometimes','boolean'],
        ];
    }
}
