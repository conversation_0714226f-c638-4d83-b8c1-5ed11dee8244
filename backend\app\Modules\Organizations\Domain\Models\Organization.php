<?php

namespace App\Modules\Organizations\Domain\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Modules\Shared\Domain\Traits\HasUUID;
use App\Modules\Users\Domain\Models\User;

class Organization extends Model
{
    use HasFactory, SoftDeletes, HasUUID;

    protected $table = 'organizations';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'parent_id',
        'type',
        'name',
        'code',
        'description',
        'email',
        'phone',
        'address',
        'city',
        'state',
        'country',
        'postal_code',
        'timezone',
        'currency',
        'language',
        // GST and Billing
        'gstin',
        'legal_name',
        'billing_address',
        'billing_city',
        'billing_state',
        'billing_postal_code',
        'billing_country',
        // Approval
        'status',
        'approval_request_id',
        'settings',
    ];

    protected $casts = [
        'settings' => 'array',
    ];

    // Relationships
    public function parent()
    {
        return $this->belongsTo(Organization::class, 'parent_id');
    }

    public function children()
    {
        return $this->hasMany(Organization::class, 'parent_id');
    }

    public function users()
    {
        return $this->hasMany(User::class, 'organization_id');
    }

    public function ancestors()
    {
        return $this->belongsToMany(
            Organization::class,
            'org_unit_closure',
            'descendant_id',
            'ancestor_id'
        )->withPivot('depth');
    }

    public function descendants()
    {
        return $this->belongsToMany(
            Organization::class,
            'org_unit_closure',
            'ancestor_id',
            'descendant_id'
        )->withPivot('depth');
    }

    public function approvalRequest()
    {
        return $this->belongsTo(
            \App\Modules\Approvals\Domain\Models\ApprovalRequest::class,
            'approval_request_id'
        );
    }

    public function subscription()
    {
        return $this->hasOne(
            \App\Modules\Billing\Domain\Models\Subscription::class,
            'organization_id'
        )
        ->where('status', 'active')
        ->latest('starts_at');
    }

    // Helper methods
    public function isRoot(): bool
    {
        return is_null($this->parent_id);
    }

    public function hasChildren(): bool
    {
        return $this->children()->exists();
    }

    public function isPendingApproval(): bool
    {
        return $this->status === 'pending_approval';
    }

    public function isTenant(): bool
    {
        return $this->type === 'tenant';
    }

    public function isPortalOwner(): bool
    {
        return $this->type === 'portal_owner' && is_null($this->parent_id);
    }

    public function isSubOrganization(): bool
    {
        return $this->type === 'sub_organization';
    }
}
