<?php

namespace Tests\Unit\Services;

use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;
use Illuminate\Foundation\Testing\DatabaseMigrations;
use App\Modules\Approvals\Domain\Services\ApprovalService;
use App\Modules\Approvals\Domain\Models\ApprovalRequest;
use App\Modules\Organizations\Domain\Models\Organization;
use App\Modules\Users\Domain\Models\User;
use App\Modules\Billing\Domain\Models\Plan;
use App\Modules\Billing\Domain\Models\Subscription;

class ApprovalServiceTest extends TestCase
{
    use DatabaseMigrations;

    protected ApprovalService $approvalService;
    protected Organization $portalOwner;
    protected Organization $tenant;
    protected Plan $plan;
    protected Subscription $subscription;
    protected User $requester;
    protected User $approver;

    protected function setUp(): void
    {
        parent::setUp();

        $this->approvalService = app(ApprovalService::class);

        $this->portalOwner = Organization::factory()->create([
            'type' => 'portal_owner',
            'parent_id' => null,
        ]);

        $this->tenant = Organization::factory()->create([
            'type' => 'tenant',
            'parent_id' => $this->portalOwner->id,
        ]);

        $this->plan = Plan::factory()->create([
            'user_limit' => 10,
            'sub_org_limit' => 5,
            'storage_limit' => 50,
        ]);

        $this->subscription = Subscription::factory()->create([
            'organization_id' => $this->tenant->id,
            'plan_id' => $this->plan->id,
            'status' => 'active',
            'user_count' => 5,
            'sub_org_count' => 2,
        ]);

        $this->requester = User::factory()->create([
            'organization_id' => $this->tenant->id,
        ]);

        $this->approver = User::factory()->create([
            'organization_id' => $this->tenant->id,
        ]);
    }
    #[Test]
    public function it_approves_user_registration_within_limits()
    {
        $approval = ApprovalRequest::factory()->create([
            'organization_id' => $this->tenant->id,
            'requester_id' => $this->requester->id,
            'type' => 'registration',
            'status' => 'pending',
            'current_step' => 1,
            'data' => [
                'registration_type' => 'user',
                'tenant_organization_id' => $this->tenant->id,
            ],
        ]);

        $approval->steps()->create([
            'organization_id' => $this->tenant->id,
            'step_number' => 1,
            'approver_id' => $this->approver->id,
            'status' => 'pending',
        ]);

        $result = $this->approvalService->approveRequest($approval, $this->approver);

        $this->assertEquals('approved', $result->status);
    }
    #[Test]
    public function it_rejects_user_registration_when_limit_exceeded()
    {
        // Set user count to limit
        $this->subscription->update(['user_count' => 10]);

        $approval = ApprovalRequest::factory()->create([
            'organization_id' => $this->tenant->id,
            'requester_id' => $this->requester->id,
            'type' => 'registration',
            'status' => 'pending',
            'current_step' => 1,
            'data' => [
                'registration_type' => 'user',
                'tenant_organization_id' => $this->tenant->id,
            ],
        ]);

        $approval->steps()->create([
            'organization_id' => $this->tenant->id,
            'step_number' => 1,
            'approver_id' => $this->approver->id,
            'status' => 'pending',
        ]);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('User limit reached');

        $this->approvalService->approveRequest($approval, $this->approver);
    }
    #[Test]
    public function it_approves_sub_org_registration_within_limits()
    {
        $approval = ApprovalRequest::factory()->create([
            'organization_id' => $this->tenant->id,
            'requester_id' => $this->requester->id,
            'type' => 'registration',
            'status' => 'pending',
            'current_step' => 1,
            'data' => [
                'registration_type' => 'sub_organization',
                'tenant_organization_id' => $this->tenant->id,
            ],
        ]);

        $approval->steps()->create([
            'organization_id' => $this->tenant->id,
            'step_number' => 1,
            'approver_id' => $this->approver->id,
            'status' => 'pending',
        ]);

        $result = $this->approvalService->approveRequest($approval, $this->approver);

        $this->assertEquals('approved', $result->status);
    }
    #[Test]
    public function it_rejects_sub_org_registration_when_limit_exceeded()
    {
        // Set sub org count to limit
        $this->subscription->update(['sub_org_count' => 5]);

        $approval = ApprovalRequest::factory()->create([
            'organization_id' => $this->tenant->id,
            'requester_id' => $this->requester->id,
            'type' => 'registration',
            'status' => 'pending',
            'current_step' => 1,
            'data' => [
                'registration_type' => 'sub_organization',
                'tenant_organization_id' => $this->tenant->id,
            ],
        ]);

        $approval->steps()->create([
            'organization_id' => $this->tenant->id,
            'step_number' => 1,
            'approver_id' => $this->approver->id,
            'status' => 'pending',
        ]);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Sub-organization limit reached');

        $this->approvalService->approveRequest($approval, $this->approver);
    }
    #[Test]
    public function it_allows_approval_when_no_subscription()
    {
        // Delete subscription
        $this->subscription->delete();

        $approval = ApprovalRequest::factory()->create([
            'organization_id' => $this->tenant->id,
            'requester_id' => $this->requester->id,
            'type' => 'registration',
            'status' => 'pending',
            'current_step' => 1,
            'data' => [
                'registration_type' => 'user',
                'tenant_organization_id' => $this->tenant->id,
            ],
        ]);

        $approval->steps()->create([
            'organization_id' => $this->tenant->id,
            'step_number' => 1,
            'approver_id' => $this->approver->id,
            'status' => 'pending',
        ]);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('No active subscription found');

        $this->approvalService->approveRequest($approval, $this->approver);
    }
    #[Test]
    public function it_bypasses_limit_check_for_non_registration_types()
    {
        // Set user count to limit
        $this->subscription->update(['user_count' => 10]);

        $approval = ApprovalRequest::factory()->create([
            'organization_id' => $this->tenant->id,
            'requester_id' => $this->requester->id,
            'type' => 'purchase_order', // Non-registration type
            'status' => 'pending',
            'current_step' => 1,
        ]);

        $approval->steps()->create([
            'organization_id' => $this->tenant->id,
            'step_number' => 1,
            'approver_id' => $this->approver->id,
            'status' => 'pending',
        ]);

        // Should not throw exception even though user limit is reached
        $result = $this->approvalService->approveRequest($approval, $this->approver);

        $this->assertEquals('approved', $result->status);
    }
    #[Test]
    public function it_allows_unlimited_users_when_plan_has_no_user_limit()
    {
        // Plan with 0 user_limit = unlimited
        $this->plan->update(['user_limit' => 0]);
        $this->subscription->update(['user_count' => 1000]);

        $approval = ApprovalRequest::factory()->create([
            'organization_id' => $this->tenant->id,
            'requester_id' => $this->requester->id,
            'type' => 'registration',
            'status' => 'pending',
            'current_step' => 1,
            'data' => [
                'registration_type' => 'user',
                'tenant_organization_id' => $this->tenant->id,
            ],
        ]);

        $approval->steps()->create([
            'organization_id' => $this->tenant->id,
            'step_number' => 1,
            'approver_id' => $this->approver->id,
            'status' => 'pending',
        ]);

        $result = $this->approvalService->approveRequest($approval, $this->approver);

        $this->assertEquals('approved', $result->status);
    }
    #[Test]
    public function it_allows_unlimited_sub_orgs_when_plan_has_no_sub_org_limit()
    {
        // Plan with 0 sub_org_limit = unlimited
        $this->plan->update(['sub_org_limit' => 0]);
        $this->subscription->update(['sub_org_count' => 100]);

        $approval = ApprovalRequest::factory()->create([
            'organization_id' => $this->tenant->id,
            'requester_id' => $this->requester->id,
            'type' => 'registration',
            'status' => 'pending',
            'current_step' => 1,
            'data' => [
                'registration_type' => 'sub_organization',
                'tenant_organization_id' => $this->tenant->id,
            ],
        ]);

        $approval->steps()->create([
            'organization_id' => $this->tenant->id,
            'step_number' => 1,
            'approver_id' => $this->approver->id,
            'status' => 'pending',
        ]);

        $result = $this->approvalService->approveRequest($approval, $this->approver);

        $this->assertEquals('approved', $result->status);
    }
    #[Test]
    public function it_checks_limits_at_exact_boundary()
    {
        // User count exactly at limit minus one
        $this->subscription->update(['user_count' => 9]);

        $approval = ApprovalRequest::factory()->create([
            'organization_id' => $this->tenant->id,
            'requester_id' => $this->requester->id,
            'type' => 'registration',
            'status' => 'pending',
            'current_step' => 1,
            'data' => [
                'registration_type' => 'user',
                'tenant_organization_id' => $this->tenant->id,
            ],
        ]);

        $approval->steps()->create([
            'organization_id' => $this->tenant->id,
            'step_number' => 1,
            'approver_id' => $this->approver->id,
            'status' => 'pending',
        ]);

        // Should succeed as we're not at limit yet
        $result = $this->approvalService->approveRequest($approval, $this->approver);
        $this->assertEquals('approved', $result->status);

        // Try again with count at limit
        $this->subscription->update(['user_count' => 10]);
        
        $approval2 = ApprovalRequest::factory()->create([
            'organization_id' => $this->tenant->id,
            'requester_id' => $this->requester->id,
            'type' => 'registration',
            'status' => 'pending',
            'current_step' => 1,
            'data' => [
                'registration_type' => 'user',
                'tenant_organization_id' => $this->tenant->id,
            ],
        ]);

        $approval2->steps()->create([
            'organization_id' => $this->tenant->id,
            'step_number' => 1,
            'approver_id' => $this->approver->id,
            'status' => 'pending',
        ]);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('User limit reached');
        
        $this->approvalService->approveRequest($approval2, $this->approver);
    }
}
