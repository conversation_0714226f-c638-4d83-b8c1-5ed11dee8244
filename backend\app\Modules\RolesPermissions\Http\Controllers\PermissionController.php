<?php

namespace App\Modules\RolesPermissions\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\RolesPermissions\Domain\Services\PermissionService;
use App\Modules\RolesPermissions\Domain\Models\Permission;
use App\Modules\RolesPermissions\Http\Requests\CreatePermissionRequest;
use App\Modules\RolesPermissions\Http\Requests\UpdatePermissionRequest;
use App\Modules\RolesPermissions\Http\Resources\PermissionResource;

class PermissionController extends Controller
{
    public function __construct(private readonly PermissionService $service) {}

    public function index()
    {
        return response()->json(PermissionResource::collection($this->service->listPermissions()));
    }

    public function store(CreatePermissionRequest $request)
    {
        $permission = $this->service->createPermission($request->validated());
        return response()->json(new PermissionResource($permission), 201);
    }

    public function show(string $id)
    {
        $permission = $this->service->getPermissionById($id);
        abort_if(!$permission, 404);
        return response()->json(new PermissionResource($permission));
    }

    public function update(UpdatePermissionRequest $request, string $id)
    {
        $permission = $this->service->getPermissionById($id);
        abort_if(!$permission, 404);
        $updated = $this->service->updatePermission($permission, $request->validated());
        return response()->json(new PermissionResource($updated));
    }

    public function destroy(string $id)
    {
        $permission = $this->service->getPermissionById($id);
        abort_if(!$permission, 404);
        return response()->json(['deleted' => $this->service->deletePermission($permission)]);
    }
}
