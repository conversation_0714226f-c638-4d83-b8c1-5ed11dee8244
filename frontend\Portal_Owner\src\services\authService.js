/**
 * Authentication Service
 * Manages authentication state and session management
 */

class AuthService {
  /**
   * Check if user is authenticated
   * @returns {boolean} True if user has valid token
   */
  isAuthenticated() {
    const token = localStorage.getItem('authToken')
    const refreshToken = localStorage.getItem('refreshToken')
    const tokenExpiry = localStorage.getItem('authTokenExpiry')
    
    // Check if both tokens exist and are valid
    if (!token || token === 'undefined' || !refreshToken || refreshToken === 'undefined') {
      return false
    }
    
    // Check if token is expired
    if (tokenExpiry) {
      const expiryTime = parseInt(tokenExpiry, 10)
      if (Date.now() > expiryTime) {
        this.clearTokens()
        return false
      }
    }
    
    return true
  }

  /**
   * Get the current auth token
   * @returns {string|null} The auth token or null
   */
  getToken() {
    const token = localStorage.getItem('authToken')
    if (token === 'undefined') {
      return null
    }
    return token
  }

  /**
   * Get the refresh token
   * @returns {string|null} The refresh token or null
   */
  getRefreshToken() {
    const token = localStorage.getItem('refreshToken')
    if (token === 'undefined') {
      return null
    }
    return token
  }

  /**
   * Store tokens after login
   * @param {string} token - The auth token
   * @param {string} refreshToken - The refresh token
   * @param {number} expiresIn - Token expiration time in seconds (default: 3600 = 1 hour)
   */
  setTokens(token, refreshToken, expiresIn = 3600) {
    localStorage.setItem('authToken', token)
    localStorage.setItem('refreshToken', refreshToken)
    
    // Store expiry time (current time + expires_in seconds)
    const expiryTime = Date.now() + (expiresIn * 1000)
    localStorage.setItem('authTokenExpiry', expiryTime.toString())
  }

  /**
   * Clear all authentication data (logout)
   */
  logout() {
    localStorage.removeItem('authToken')
    localStorage.removeItem('refreshToken')
    
    // Redirect to login page
    window.location.href = '/login'
  }

  /**
   * Clear tokens silently (for session expiration)
   */
  clearTokens() {
    localStorage.removeItem('authToken')
    localStorage.removeItem('refreshToken')
    localStorage.removeItem('authTokenExpiry')
  }

  /**
   * Handle session expiration
   */
  handleSessionExpired() {
    this.clearTokens()
    window.location.href = '/login'
  }
}

export default new AuthService()
