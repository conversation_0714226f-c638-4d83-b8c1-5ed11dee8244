import React, { useState, useEffect } from 'react'
import { CCard, CCardBody, CCardHeader, CCol, CRow, CSpinner, CAlert } from '@coreui/react'
import { reportsAPI } from '../../api/reports'

const Dashboard = () => {
  const [metrics, setMetrics] = useState({})
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    loadMetrics()
  }, [])

  const loadMetrics = async () => {
    try {
      setLoading(true)
      setError(null)
      const [subs, payments, revenue, users, approvals] = await Promise.all([
        reportsAPI.getSubscriptionsReport(),
        reportsAPI.getPaymentsReport(),
        reportsAPI.getRevenueReport(),
        reportsAPI.getUsersReport(),
        reportsAPI.getApprovalsReport(),
      ])
      setMetrics({
        subscriptions: subs.data.data,
        payments: payments.data.data,
        revenue: revenue.data.data,
        users: users.data.data,
        approvals: approvals.data.data,
      })
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to load metrics')
      console.error('Error:', err)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <CRow>
        <CCol xs={12} className="text-center">
          <CSpinner color="primary" />
        </CCol>
      </CRow>
    )
  }

  return (
    <CRow>
      {error && (
        <CCol xs={12}>
          <CAlert color="danger">{error}</CAlert>
        </CCol>
      )}

      <CCol xs={12} sm={6} lg={3}>
        <CCard className="mb-4">
          <CCardBody>
            <div className="text-muted small">Active Subscriptions</div>
            <div className="fs-5 fw-bold">{metrics.subscriptions?.active || 0}</div>
          </CCardBody>
        </CCard>
      </CCol>

      <CCol xs={12} sm={6} lg={3}>
        <CCard className="mb-4">
          <CCardBody>
            <div className="text-muted small">Total Revenue</div>
            <div className="fs-5 fw-bold">${metrics.revenue?.total || 0}</div>
          </CCardBody>
        </CCard>
      </CCol>

      <CCol xs={12} sm={6} lg={3}>
        <CCard className="mb-4">
          <CCardBody>
            <div className="text-muted small">Total Users</div>
            <div className="fs-5 fw-bold">{metrics.users?.total || 0}</div>
          </CCardBody>
        </CCard>
      </CCol>

      <CCol xs={12} sm={6} lg={3}>
        <CCard className="mb-4">
          <CCardBody>
            <div className="text-muted small">Pending Approvals</div>
            <div className="fs-5 fw-bold">{metrics.approvals?.pending || 0}</div>
          </CCardBody>
        </CCard>
      </CCol>

      <CCol xs={12}>
        <CCard>
          <CCardHeader>
            <strong>Recent Activity</strong>
          </CCardHeader>
          <CCardBody>
            <div className="text-muted text-center">
              Dashboard metrics loaded successfully. Navigate to specific sections for detailed information.
            </div>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  )
}

export default Dashboard
