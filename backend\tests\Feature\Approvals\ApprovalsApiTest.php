<?php

namespace Tests\Feature\Approvals;

use Tests\TestCase;
use Tests\Support\CreatesOrgUser;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ApprovalsApiTest extends TestCase
{
    use RefreshDatabase, CreatesOrgUser;

    public function test_approval_flow_create_approve_reject_comment(): void
    {
        $org = $this->createOrganization();
        $user = $this->createUserInOrg($org);
        $approver = $this->createUserInOrg($org, ['email' => '<EMAIL>']);
        $this->actingAs($user);

        // Create
        $payload = [
            'type' => 'generic',
            'description' => 'Test approval',
            'steps' => [
                ['approver_id' => $approver->id],
            ],
        ];
        $approvalId = $this->postJson('/api/v1/approvals', $payload)->assertStatus(201)->json('id');

        // Show
        $this->getJson('/api/v1/approvals/'.$approvalId)->assertStatus(200);

        // Approve (as approver)
        $this->actingAs($approver);
        $this->postJson('/api/v1/approvals/'.$approvalId.'/approve', ['comment' => 'Looks good'])->assertStatus(200);

        // Add comment
        $this->postJson('/api/v1/approvals/'.$approvalId.'/comments', ['comment' => 'Extra note'])->assertStatus(201);

        // Reject (create a new one and reject)
        $this->actingAs($user);
        $approvalId2 = $this->postJson('/api/v1/approvals', $payload)->assertStatus(201)->json('id');
        $this->actingAs($approver);
        $this->postJson('/api/v1/approvals/'.$approvalId2.'/reject', ['reason' => 'Not acceptable'])->assertStatus(200);
    }
}
