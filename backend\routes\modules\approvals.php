<?php

use Illuminate\Support\Facades\Route;

Route::middleware('auth')->group(function () {
    // Approvals
    Route::get('approvals', [\App\Modules\Approvals\Http\Controllers\ApprovalController::class, 'index']);
    Route::post('approvals', [\App\Modules\Approvals\Http\Controllers\ApprovalController::class, 'store']);
    Route::get('approvals/{id}', [\App\Modules\Approvals\Http\Controllers\ApprovalController::class, 'show']);
    Route::post('approvals/{id}/approve', [\App\Modules\Approvals\Http\Controllers\ApprovalController::class, 'approve']);
    Route::post('approvals/{id}/reject', [\App\Modules\Approvals\Http\Controllers\ApprovalController::class, 'reject']);
    Route::post('approvals/{id}/comments', [\App\Modules\Approvals\Http\Controllers\ApprovalController::class, 'addComment']);
});
