{"version": 3, "sources": ["../../simplebar-react/dist/index.mjs", "../../lodash-es/isObject.js", "../../lodash-es/_freeGlobal.js", "../../lodash-es/_root.js", "../../lodash-es/now.js", "../../lodash-es/_trimmedEndIndex.js", "../../lodash-es/_baseTrim.js", "../../lodash-es/_Symbol.js", "../../lodash-es/_getRawTag.js", "../../lodash-es/_objectToString.js", "../../lodash-es/_baseGetTag.js", "../../lodash-es/isObjectLike.js", "../../lodash-es/isSymbol.js", "../../lodash-es/toNumber.js", "../../lodash-es/debounce.js", "../../lodash-es/throttle.js", "../../tslib/tslib.es6.js", "../../simplebar-core/src/helpers.ts", "../../simplebar-core/src/scrollbar-width.ts", "../../simplebar-core/src/index.ts"], "sourcesContent": ["/**\n * simplebar-react - v3.3.2\n * React component for SimpleBar\n * https://grsmto.github.io/simplebar/\n *\n * Made by Adrien Denat\n * Under MIT License\n */\n\nimport * as React from 'react';\nimport SimpleBarCore from 'simplebar-core';\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\r\n\r\nfunction __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\n\nvar SimpleBar = React.forwardRef(function (_a, ref) {\n    var children = _a.children, _b = _a.scrollableNodeProps, scrollableNodeProps = _b === void 0 ? {} : _b, otherProps = __rest(_a, [\"children\", \"scrollableNodeProps\"]);\n    var elRef = React.useRef();\n    var scrollableNodeRef = React.useRef();\n    var contentNodeRef = React.useRef();\n    var options = {};\n    var rest = {};\n    Object.keys(otherProps).forEach(function (key) {\n        if (Object.prototype.hasOwnProperty.call(SimpleBarCore.defaultOptions, key)) {\n            options[key] = otherProps[key];\n        }\n        else {\n            rest[key] = otherProps[key];\n        }\n    });\n    var classNames = __assign(__assign({}, SimpleBarCore.defaultOptions.classNames), options.classNames);\n    var scrollableNodeFullProps = __assign(__assign({}, scrollableNodeProps), { className: \"\".concat(classNames.contentWrapper).concat(scrollableNodeProps.className ? \" \".concat(scrollableNodeProps.className) : ''), tabIndex: options.tabIndex || SimpleBarCore.defaultOptions.tabIndex, role: 'region', 'aria-label': options.ariaLabel || SimpleBarCore.defaultOptions.ariaLabel });\n    React.useEffect(function () {\n        var instance;\n        scrollableNodeRef.current = scrollableNodeFullProps.ref\n            ? scrollableNodeFullProps.ref.current\n            : scrollableNodeRef.current;\n        if (elRef.current) {\n            instance = new SimpleBarCore(elRef.current, __assign(__assign(__assign({}, options), (scrollableNodeRef.current && {\n                scrollableNode: scrollableNodeRef.current\n            })), (contentNodeRef.current && {\n                contentNode: contentNodeRef.current\n            })));\n            if (typeof ref === 'function') {\n                ref(instance);\n            }\n            else if (ref) {\n                ref.current = instance;\n            }\n        }\n        return function () {\n            instance === null || instance === void 0 ? void 0 : instance.unMount();\n            instance = null;\n            if (typeof ref === 'function') {\n                ref(null);\n            }\n        };\n    }, []);\n    return (React.createElement(\"div\", __assign({ \"data-simplebar\": \"init\", ref: elRef }, rest),\n        React.createElement(\"div\", { className: classNames.wrapper },\n            React.createElement(\"div\", { className: classNames.heightAutoObserverWrapperEl },\n                React.createElement(\"div\", { className: classNames.heightAutoObserverEl })),\n            React.createElement(\"div\", { className: classNames.mask },\n                React.createElement(\"div\", { className: classNames.offset }, typeof children === 'function' ? (children({\n                    scrollableNodeRef: scrollableNodeRef,\n                    scrollableNodeProps: __assign(__assign({}, scrollableNodeFullProps), { ref: scrollableNodeRef }),\n                    contentNodeRef: contentNodeRef,\n                    contentNodeProps: {\n                        className: classNames.contentEl,\n                        ref: contentNodeRef\n                    }\n                })) : (React.createElement(\"div\", __assign({}, scrollableNodeFullProps),\n                    React.createElement(\"div\", { className: classNames.contentEl }, children))))),\n            React.createElement(\"div\", { className: classNames.placeholder })),\n        React.createElement(\"div\", { className: \"\".concat(classNames.track, \" \").concat(classNames.horizontal) },\n            React.createElement(\"div\", { className: classNames.scrollbar })),\n        React.createElement(\"div\", { className: \"\".concat(classNames.track, \" \").concat(classNames.vertical) },\n            React.createElement(\"div\", { className: classNames.scrollbar }))));\n});\nSimpleBar.displayName = 'SimpleBar';\n\nexport { SimpleBar as default };\n", "/**\n * Checks if `value` is the\n * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)\n * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an object, else `false`.\n * @example\n *\n * _.isObject({});\n * // => true\n *\n * _.isObject([1, 2, 3]);\n * // => true\n *\n * _.isObject(_.noop);\n * // => true\n *\n * _.isObject(null);\n * // => false\n */\nfunction isObject(value) {\n  var type = typeof value;\n  return value != null && (type == 'object' || type == 'function');\n}\n\nexport default isObject;\n", "/** Detect free variable `global` from Node.js. */\nvar freeGlobal = typeof global == 'object' && global && global.Object === Object && global;\n\nexport default freeGlobal;\n", "import freeGlobal from './_freeGlobal.js';\n\n/** Detect free variable `self`. */\nvar freeSelf = typeof self == 'object' && self && self.Object === Object && self;\n\n/** Used as a reference to the global object. */\nvar root = freeGlobal || freeSelf || Function('return this')();\n\nexport default root;\n", "import root from './_root.js';\n\n/**\n * Gets the timestamp of the number of milliseconds that have elapsed since\n * the Unix epoch (1 January 1970 00:00:00 UTC).\n *\n * @static\n * @memberOf _\n * @since 2.4.0\n * @category Date\n * @returns {number} Returns the timestamp.\n * @example\n *\n * _.defer(function(stamp) {\n *   console.log(_.now() - stamp);\n * }, _.now());\n * // => Logs the number of milliseconds it took for the deferred invocation.\n */\nvar now = function() {\n  return root.Date.now();\n};\n\nexport default now;\n", "/** Used to match a single whitespace character. */\nvar reWhitespace = /\\s/;\n\n/**\n * Used by `_.trim` and `_.trimEnd` to get the index of the last non-whitespace\n * character of `string`.\n *\n * @private\n * @param {string} string The string to inspect.\n * @returns {number} Returns the index of the last non-whitespace character.\n */\nfunction trimmedEndIndex(string) {\n  var index = string.length;\n\n  while (index-- && reWhitespace.test(string.charAt(index))) {}\n  return index;\n}\n\nexport default trimmedEndIndex;\n", "import trimmedEndIndex from './_trimmedEndIndex.js';\n\n/** Used to match leading whitespace. */\nvar reTrimStart = /^\\s+/;\n\n/**\n * The base implementation of `_.trim`.\n *\n * @private\n * @param {string} string The string to trim.\n * @returns {string} Returns the trimmed string.\n */\nfunction baseTrim(string) {\n  return string\n    ? string.slice(0, trimmedEndIndex(string) + 1).replace(reTrimStart, '')\n    : string;\n}\n\nexport default baseTrim;\n", "import root from './_root.js';\n\n/** Built-in value references. */\nvar Symbol = root.Symbol;\n\nexport default Symbol;\n", "import Symbol from './_Symbol.js';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar nativeObjectToString = objectProto.toString;\n\n/** Built-in value references. */\nvar symToStringTag = Symbol ? Symbol.toStringTag : undefined;\n\n/**\n * A specialized version of `baseGetTag` which ignores `Symbol.toStringTag` values.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the raw `toStringTag`.\n */\nfunction getRawTag(value) {\n  var isOwn = hasOwnProperty.call(value, symToStringTag),\n      tag = value[symToStringTag];\n\n  try {\n    value[symToStringTag] = undefined;\n    var unmasked = true;\n  } catch (e) {}\n\n  var result = nativeObjectToString.call(value);\n  if (unmasked) {\n    if (isOwn) {\n      value[symToStringTag] = tag;\n    } else {\n      delete value[symToStringTag];\n    }\n  }\n  return result;\n}\n\nexport default getRawTag;\n", "/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar nativeObjectToString = objectProto.toString;\n\n/**\n * Converts `value` to a string using `Object.prototype.toString`.\n *\n * @private\n * @param {*} value The value to convert.\n * @returns {string} Returns the converted string.\n */\nfunction objectToString(value) {\n  return nativeObjectToString.call(value);\n}\n\nexport default objectToString;\n", "import Symbol from './_Symbol.js';\nimport getRawTag from './_getRawTag.js';\nimport objectToString from './_objectToString.js';\n\n/** `Object#toString` result references. */\nvar nullTag = '[object Null]',\n    undefinedTag = '[object Undefined]';\n\n/** Built-in value references. */\nvar symToStringTag = Symbol ? Symbol.toStringTag : undefined;\n\n/**\n * The base implementation of `getTag` without fallbacks for buggy environments.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the `toStringTag`.\n */\nfunction baseGetTag(value) {\n  if (value == null) {\n    return value === undefined ? undefinedTag : nullTag;\n  }\n  return (symToStringTag && symToStringTag in Object(value))\n    ? getRawTag(value)\n    : objectToString(value);\n}\n\nexport default baseGetTag;\n", "/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */\nfunction isObjectLike(value) {\n  return value != null && typeof value == 'object';\n}\n\nexport default isObjectLike;\n", "import baseGetTag from './_baseGetTag.js';\nimport isObjectLike from './isObjectLike.js';\n\n/** `Object#toString` result references. */\nvar symbolTag = '[object Symbol]';\n\n/**\n * Checks if `value` is classified as a `Symbol` primitive or object.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a symbol, else `false`.\n * @example\n *\n * _.isSymbol(Symbol.iterator);\n * // => true\n *\n * _.isSymbol('abc');\n * // => false\n */\nfunction isSymbol(value) {\n  return typeof value == 'symbol' ||\n    (isObjectLike(value) && baseGetTag(value) == symbolTag);\n}\n\nexport default isSymbol;\n", "import baseTrim from './_baseTrim.js';\nimport isObject from './isObject.js';\nimport isSymbol from './isSymbol.js';\n\n/** Used as references for various `Number` constants. */\nvar NAN = 0 / 0;\n\n/** Used to detect bad signed hexadecimal string values. */\nvar reIsBadHex = /^[-+]0x[0-9a-f]+$/i;\n\n/** Used to detect binary string values. */\nvar reIsBinary = /^0b[01]+$/i;\n\n/** Used to detect octal string values. */\nvar reIsOctal = /^0o[0-7]+$/i;\n\n/** Built-in method references without a dependency on `root`. */\nvar freeParseInt = parseInt;\n\n/**\n * Converts `value` to a number.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to process.\n * @returns {number} Returns the number.\n * @example\n *\n * _.toNumber(3.2);\n * // => 3.2\n *\n * _.toNumber(Number.MIN_VALUE);\n * // => 5e-324\n *\n * _.toNumber(Infinity);\n * // => Infinity\n *\n * _.toNumber('3.2');\n * // => 3.2\n */\nfunction toNumber(value) {\n  if (typeof value == 'number') {\n    return value;\n  }\n  if (isSymbol(value)) {\n    return NAN;\n  }\n  if (isObject(value)) {\n    var other = typeof value.valueOf == 'function' ? value.valueOf() : value;\n    value = isObject(other) ? (other + '') : other;\n  }\n  if (typeof value != 'string') {\n    return value === 0 ? value : +value;\n  }\n  value = baseTrim(value);\n  var isBinary = reIsBinary.test(value);\n  return (isBinary || reIsOctal.test(value))\n    ? freeParseInt(value.slice(2), isBinary ? 2 : 8)\n    : (reIsBadHex.test(value) ? NAN : +value);\n}\n\nexport default toNumber;\n", "import isObject from './isObject.js';\nimport now from './now.js';\nimport toNumber from './toNumber.js';\n\n/** Error message constants. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max,\n    nativeMin = Math.min;\n\n/**\n * Creates a debounced function that delays invoking `func` until after `wait`\n * milliseconds have elapsed since the last time the debounced function was\n * invoked. The debounced function comes with a `cancel` method to cancel\n * delayed `func` invocations and a `flush` method to immediately invoke them.\n * Provide `options` to indicate whether `func` should be invoked on the\n * leading and/or trailing edge of the `wait` timeout. The `func` is invoked\n * with the last arguments provided to the debounced function. Subsequent\n * calls to the debounced function return the result of the last `func`\n * invocation.\n *\n * **Note:** If `leading` and `trailing` options are `true`, `func` is\n * invoked on the trailing edge of the timeout only if the debounced function\n * is invoked more than once during the `wait` timeout.\n *\n * If `wait` is `0` and `leading` is `false`, `func` invocation is deferred\n * until to the next tick, similar to `setTimeout` with a timeout of `0`.\n *\n * See [David Corbacho's article](https://css-tricks.com/debouncing-throttling-explained-examples/)\n * for details over the differences between `_.debounce` and `_.throttle`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Function\n * @param {Function} func The function to debounce.\n * @param {number} [wait=0] The number of milliseconds to delay.\n * @param {Object} [options={}] The options object.\n * @param {boolean} [options.leading=false]\n *  Specify invoking on the leading edge of the timeout.\n * @param {number} [options.maxWait]\n *  The maximum time `func` is allowed to be delayed before it's invoked.\n * @param {boolean} [options.trailing=true]\n *  Specify invoking on the trailing edge of the timeout.\n * @returns {Function} Returns the new debounced function.\n * @example\n *\n * // Avoid costly calculations while the window size is in flux.\n * jQuery(window).on('resize', _.debounce(calculateLayout, 150));\n *\n * // Invoke `sendMail` when clicked, debouncing subsequent calls.\n * jQuery(element).on('click', _.debounce(sendMail, 300, {\n *   'leading': true,\n *   'trailing': false\n * }));\n *\n * // Ensure `batchLog` is invoked once after 1 second of debounced calls.\n * var debounced = _.debounce(batchLog, 250, { 'maxWait': 1000 });\n * var source = new EventSource('/stream');\n * jQuery(source).on('message', debounced);\n *\n * // Cancel the trailing debounced invocation.\n * jQuery(window).on('popstate', debounced.cancel);\n */\nfunction debounce(func, wait, options) {\n  var lastArgs,\n      lastThis,\n      maxWait,\n      result,\n      timerId,\n      lastCallTime,\n      lastInvokeTime = 0,\n      leading = false,\n      maxing = false,\n      trailing = true;\n\n  if (typeof func != 'function') {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n  wait = toNumber(wait) || 0;\n  if (isObject(options)) {\n    leading = !!options.leading;\n    maxing = 'maxWait' in options;\n    maxWait = maxing ? nativeMax(toNumber(options.maxWait) || 0, wait) : maxWait;\n    trailing = 'trailing' in options ? !!options.trailing : trailing;\n  }\n\n  function invokeFunc(time) {\n    var args = lastArgs,\n        thisArg = lastThis;\n\n    lastArgs = lastThis = undefined;\n    lastInvokeTime = time;\n    result = func.apply(thisArg, args);\n    return result;\n  }\n\n  function leadingEdge(time) {\n    // Reset any `maxWait` timer.\n    lastInvokeTime = time;\n    // Start the timer for the trailing edge.\n    timerId = setTimeout(timerExpired, wait);\n    // Invoke the leading edge.\n    return leading ? invokeFunc(time) : result;\n  }\n\n  function remainingWait(time) {\n    var timeSinceLastCall = time - lastCallTime,\n        timeSinceLastInvoke = time - lastInvokeTime,\n        timeWaiting = wait - timeSinceLastCall;\n\n    return maxing\n      ? nativeMin(timeWaiting, maxWait - timeSinceLastInvoke)\n      : timeWaiting;\n  }\n\n  function shouldInvoke(time) {\n    var timeSinceLastCall = time - lastCallTime,\n        timeSinceLastInvoke = time - lastInvokeTime;\n\n    // Either this is the first call, activity has stopped and we're at the\n    // trailing edge, the system time has gone backwards and we're treating\n    // it as the trailing edge, or we've hit the `maxWait` limit.\n    return (lastCallTime === undefined || (timeSinceLastCall >= wait) ||\n      (timeSinceLastCall < 0) || (maxing && timeSinceLastInvoke >= maxWait));\n  }\n\n  function timerExpired() {\n    var time = now();\n    if (shouldInvoke(time)) {\n      return trailingEdge(time);\n    }\n    // Restart the timer.\n    timerId = setTimeout(timerExpired, remainingWait(time));\n  }\n\n  function trailingEdge(time) {\n    timerId = undefined;\n\n    // Only invoke if we have `lastArgs` which means `func` has been\n    // debounced at least once.\n    if (trailing && lastArgs) {\n      return invokeFunc(time);\n    }\n    lastArgs = lastThis = undefined;\n    return result;\n  }\n\n  function cancel() {\n    if (timerId !== undefined) {\n      clearTimeout(timerId);\n    }\n    lastInvokeTime = 0;\n    lastArgs = lastCallTime = lastThis = timerId = undefined;\n  }\n\n  function flush() {\n    return timerId === undefined ? result : trailingEdge(now());\n  }\n\n  function debounced() {\n    var time = now(),\n        isInvoking = shouldInvoke(time);\n\n    lastArgs = arguments;\n    lastThis = this;\n    lastCallTime = time;\n\n    if (isInvoking) {\n      if (timerId === undefined) {\n        return leadingEdge(lastCallTime);\n      }\n      if (maxing) {\n        // Handle invocations in a tight loop.\n        clearTimeout(timerId);\n        timerId = setTimeout(timerExpired, wait);\n        return invokeFunc(lastCallTime);\n      }\n    }\n    if (timerId === undefined) {\n      timerId = setTimeout(timerExpired, wait);\n    }\n    return result;\n  }\n  debounced.cancel = cancel;\n  debounced.flush = flush;\n  return debounced;\n}\n\nexport default debounce;\n", "import debounce from './debounce.js';\nimport isObject from './isObject.js';\n\n/** Error message constants. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/**\n * Creates a throttled function that only invokes `func` at most once per\n * every `wait` milliseconds. The throttled function comes with a `cancel`\n * method to cancel delayed `func` invocations and a `flush` method to\n * immediately invoke them. Provide `options` to indicate whether `func`\n * should be invoked on the leading and/or trailing edge of the `wait`\n * timeout. The `func` is invoked with the last arguments provided to the\n * throttled function. Subsequent calls to the throttled function return the\n * result of the last `func` invocation.\n *\n * **Note:** If `leading` and `trailing` options are `true`, `func` is\n * invoked on the trailing edge of the timeout only if the throttled function\n * is invoked more than once during the `wait` timeout.\n *\n * If `wait` is `0` and `leading` is `false`, `func` invocation is deferred\n * until to the next tick, similar to `setTimeout` with a timeout of `0`.\n *\n * See [<PERSON>'s article](https://css-tricks.com/debouncing-throttling-explained-examples/)\n * for details over the differences between `_.throttle` and `_.debounce`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Function\n * @param {Function} func The function to throttle.\n * @param {number} [wait=0] The number of milliseconds to throttle invocations to.\n * @param {Object} [options={}] The options object.\n * @param {boolean} [options.leading=true]\n *  Specify invoking on the leading edge of the timeout.\n * @param {boolean} [options.trailing=true]\n *  Specify invoking on the trailing edge of the timeout.\n * @returns {Function} Returns the new throttled function.\n * @example\n *\n * // Avoid excessively updating the position while scrolling.\n * jQuery(window).on('scroll', _.throttle(updatePosition, 100));\n *\n * // Invoke `renewToken` when the click event is fired, but not more than once every 5 minutes.\n * var throttled = _.throttle(renewToken, 300000, { 'trailing': false });\n * jQuery(element).on('click', throttled);\n *\n * // Cancel the trailing throttled invocation.\n * jQuery(window).on('popstate', throttled.cancel);\n */\nfunction throttle(func, wait, options) {\n  var leading = true,\n      trailing = true;\n\n  if (typeof func != 'function') {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n  if (isObject(options)) {\n    leading = 'leading' in options ? !!options.leading : leading;\n    trailing = 'trailing' in options ? !!options.trailing : trailing;\n  }\n  return debounce(func, wait, {\n    'leading': leading,\n    'maxWait': wait,\n    'trailing': trailing\n  });\n}\n\nexport default throttle;\n", "/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    var desc = Object.getOwnPropertyDescriptor(m, k);\r\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\r\n        desc = { enumerable: true, get: function() { return m[k]; } };\r\n    }\r\n    Object.defineProperty(o, k2, desc);\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\r\n\r\nexport function __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\r\n\r\nexport function __classPrivateFieldIn(state, receiver) {\r\n    if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\r\n    return typeof state === \"function\" ? receiver === state : state.has(receiver);\r\n}\r\n", "import type { SimpleBarOptions } from './index';\n\nexport function getElementWindow(element: Element) {\n  if (\n    !element ||\n    !element.ownerDocument ||\n    !element.ownerDocument.defaultView\n  ) {\n    return window;\n  }\n  return element.ownerDocument.defaultView;\n}\n\nexport function getElementDocument(element: Element) {\n  if (!element || !element.ownerDocument) {\n    return document;\n  }\n  return element.ownerDocument;\n}\n\n// Helper function to retrieve options from element attributes\nexport const getOptions = function (obj: any) {\n  const initialObj: SimpleBarOptions = {};\n\n  const options = Array.prototype.reduce.call(\n    obj,\n    (acc: any, attribute) => {\n      const option = attribute.name.match(/data-simplebar-(.+)/);\n      if (option) {\n        const key: keyof SimpleBarOptions = option[1].replace(\n          /\\W+(.)/g,\n          (_: any, chr: string) => chr.toUpperCase()\n        );\n\n        switch (attribute.value) {\n          case 'true':\n            acc[key] = true;\n            break;\n          case 'false':\n            acc[key] = false;\n            break;\n          case undefined:\n            acc[key] = true;\n            break;\n          default:\n            acc[key] = attribute.value;\n        }\n      }\n      return acc;\n    },\n    initialObj\n  );\n  return options as SimpleBarOptions;\n};\n\nexport function addClasses(el: HTMLElement | null, classes: string) {\n  if (!el) return;\n  el.classList.add(...classes.split(' '));\n}\n\nexport function removeClasses(el: HTMLElement | null, classes: string) {\n  if (!el) return;\n  classes.split(' ').forEach((className) => {\n    el.classList.remove(className);\n  });\n}\n\nexport function classNamesToQuery(classNames: string) {\n  return `.${classNames.split(' ').join('.')}`;\n}\n\nexport const canUseDOM = !!(\n  typeof window !== 'undefined' &&\n  window.document &&\n  window.document.createElement\n);\n", "import { canUseDOM } from './helpers';\n\nlet cachedScrollbarWidth: number | null = null;\nlet cachedDevicePixelRatio: number | null = null;\n\nif (canUseDOM) {\n  window.addEventListener('resize', () => {\n    if (cachedDevicePixelRatio !== window.devicePixelRatio) {\n      cachedDevicePixelRatio = window.devicePixelRatio;\n      cachedScrollbarWidth = null;\n    }\n  });\n}\n\nexport default function scrollbarWidth() {\n  if (cachedScrollbarWidth === null) {\n    if (typeof document === 'undefined') {\n      cachedScrollbarWidth = 0;\n      return cachedScrollbarWidth;\n    }\n\n    const body = document.body;\n    const box = document.createElement('div');\n\n    box.classList.add('simplebar-hide-scrollbar');\n\n    body.appendChild(box);\n\n    const width = box.getBoundingClientRect().right;\n\n    body.removeChild(box);\n\n    cachedScrollbarWidth = width;\n  }\n\n  return cachedScrollbarWidth;\n}\n", "import type { DebouncedFunc } from 'lodash';\nimport debounce from 'lodash-es/debounce.js';\nimport throttle from 'lodash-es/throttle.js';\n\nimport scrollbarWidth from './scrollbar-width';\nimport * as helpers from './helpers';\n\ninterface Options {\n  forceVisible: boolean | Axis;\n  clickOnTrack: boolean;\n  scrollbarMinSize: number;\n  scrollbarMaxSize: number;\n  classNames: Partial<ClassNames>;\n  ariaLabel: string;\n  tabIndex: number;\n  scrollableNode: HTMLElement | null;\n  contentNode: HTMLElement | null;\n  autoHide: boolean;\n}\n\nexport interface SimpleBarOptions extends Partial<Options> {}\n\ntype ClassNames = {\n  contentEl: string;\n  contentWrapper: string;\n  offset: string;\n  mask: string;\n  wrapper: string;\n  placeholder: string;\n  scrollbar: string;\n  track: string;\n  heightAutoObserverWrapperEl: string;\n  heightAutoObserverEl: string;\n  visible: string;\n  horizontal: string;\n  vertical: string;\n  hover: string;\n  dragging: string;\n  scrolling: string;\n  scrollable: string;\n  mouseEntered: string;\n};\n\ntype Axis = 'x' | 'y';\ntype AxisProps = {\n  scrollOffsetAttr: 'scrollLeft' | 'scrollTop';\n  sizeAttr: 'width' | 'height';\n  scrollSizeAttr: 'scrollWidth' | 'scrollHeight';\n  offsetSizeAttr: 'offsetWidth' | 'offsetHeight';\n  offsetAttr: 'left' | 'top';\n  overflowAttr: 'overflowX' | 'overflowY';\n  dragOffset: number;\n  isOverflowing: boolean;\n  forceVisible: boolean;\n  track: {\n    size: any;\n    el: HTMLElement | null;\n    rect: DOMRect | null;\n    isVisible: boolean;\n  };\n  scrollbar: {\n    size: any;\n    el: HTMLElement | null;\n    rect: DOMRect | null;\n    isVisible: boolean;\n  };\n};\ntype RtlHelpers = {\n  // determines if the scrolling is responding with negative values\n  isScrollOriginAtZero: boolean;\n  // determines if the origin scrollbar position is inverted or not (positioned on left or right)\n  isScrollingToNegative: boolean;\n} | null;\ntype DefaultOptions = Options & typeof SimpleBarCore.defaultOptions;\n\nconst {\n  getElementWindow,\n  getElementDocument,\n  getOptions,\n  addClasses,\n  removeClasses,\n  classNamesToQuery,\n} = helpers;\n\nexport default class SimpleBarCore {\n  el: HTMLElement;\n  options: DefaultOptions;\n  classNames: ClassNames;\n  axis: {\n    x: AxisProps;\n    y: AxisProps;\n  };\n  draggedAxis?: Axis;\n  removePreventClickId: null | number = null;\n  minScrollbarWidth = 20;\n  stopScrollDelay = 175;\n  isScrolling = false;\n  isMouseEntering = false;\n  isDragging = false;\n  scrollXTicking = false;\n  scrollYTicking = false;\n  wrapperEl: HTMLElement | null = null;\n  contentWrapperEl: HTMLElement | null = null;\n  contentEl: HTMLElement | null = null;\n  offsetEl: HTMLElement | null = null;\n  maskEl: HTMLElement | null = null;\n  placeholderEl: HTMLElement | null = null;\n  heightAutoObserverWrapperEl: HTMLElement | null = null;\n  heightAutoObserverEl: HTMLElement | null = null;\n  rtlHelpers: RtlHelpers = null;\n  scrollbarWidth: number = 0;\n  resizeObserver: ResizeObserver | null = null;\n  mutationObserver: MutationObserver | null = null;\n  elStyles: CSSStyleDeclaration | null = null;\n  isRtl: boolean | null = null;\n  mouseX: number = 0;\n  mouseY: number = 0;\n  onMouseMove: DebouncedFunc<any> | (() => void) = () => {};\n  onWindowResize: DebouncedFunc<any> | (() => void) = () => {};\n  onStopScrolling: DebouncedFunc<any> | (() => void) = () => {};\n  onMouseEntered: DebouncedFunc<any> | (() => void) = () => {};\n\n  static rtlHelpers: RtlHelpers = null;\n\n  static defaultOptions: Options = {\n    forceVisible: false,\n    clickOnTrack: true,\n    scrollbarMinSize: 25,\n    scrollbarMaxSize: 0,\n    ariaLabel: 'scrollable content',\n    tabIndex: 0,\n    classNames: {\n      contentEl: 'simplebar-content',\n      contentWrapper: 'simplebar-content-wrapper',\n      offset: 'simplebar-offset',\n      mask: 'simplebar-mask',\n      wrapper: 'simplebar-wrapper',\n      placeholder: 'simplebar-placeholder',\n      scrollbar: 'simplebar-scrollbar',\n      track: 'simplebar-track',\n      heightAutoObserverWrapperEl: 'simplebar-height-auto-observer-wrapper',\n      heightAutoObserverEl: 'simplebar-height-auto-observer',\n      visible: 'simplebar-visible',\n      horizontal: 'simplebar-horizontal',\n      vertical: 'simplebar-vertical',\n      hover: 'simplebar-hover',\n      dragging: 'simplebar-dragging',\n      scrolling: 'simplebar-scrolling',\n      scrollable: 'simplebar-scrollable',\n      mouseEntered: 'simplebar-mouse-entered',\n    },\n    scrollableNode: null,\n    contentNode: null,\n    autoHide: true,\n  };\n\n  constructor(element: HTMLElement, options: Partial<Options> = {}) {\n    this.el = element;\n    this.options = { ...SimpleBarCore.defaultOptions, ...options };\n    this.classNames = {\n      ...SimpleBarCore.defaultOptions.classNames,\n      ...options.classNames,\n    } as ClassNames;\n    this.axis = {\n      x: {\n        scrollOffsetAttr: 'scrollLeft',\n        sizeAttr: 'width',\n        scrollSizeAttr: 'scrollWidth',\n        offsetSizeAttr: 'offsetWidth',\n        offsetAttr: 'left',\n        overflowAttr: 'overflowX',\n        dragOffset: 0,\n        isOverflowing: true,\n        forceVisible: false,\n        track: { size: null, el: null, rect: null, isVisible: false },\n        scrollbar: { size: null, el: null, rect: null, isVisible: false },\n      },\n      y: {\n        scrollOffsetAttr: 'scrollTop',\n        sizeAttr: 'height',\n        scrollSizeAttr: 'scrollHeight',\n        offsetSizeAttr: 'offsetHeight',\n        offsetAttr: 'top',\n        overflowAttr: 'overflowY',\n        dragOffset: 0,\n        isOverflowing: true,\n        forceVisible: false,\n        track: { size: null, el: null, rect: null, isVisible: false },\n        scrollbar: { size: null, el: null, rect: null, isVisible: false },\n      },\n    };\n\n    if (typeof this.el !== 'object' || !this.el.nodeName) {\n      throw new Error(\n        `Argument passed to SimpleBar must be an HTML element instead of ${this.el}`\n      );\n    }\n\n    this.onMouseMove = throttle(this._onMouseMove, 64);\n    this.onWindowResize = debounce(this._onWindowResize, 64, { leading: true });\n    this.onStopScrolling = debounce(\n      this._onStopScrolling,\n      this.stopScrollDelay\n    );\n    this.onMouseEntered = debounce(this._onMouseEntered, this.stopScrollDelay);\n\n    this.init();\n  }\n\n  /**\n   * Static functions\n   */\n\n  static getOptions = getOptions;\n  static helpers = helpers;\n\n  /**\n   * Helper to fix browsers inconsistency on RTL:\n   *  - Firefox inverts the scrollbar initial position\n   *  - IE11 inverts both scrollbar position and scrolling offset\n   * Directly inspired by @KingSora's OverlayScrollbars https://github.com/KingSora/OverlayScrollbars/blob/master/js/OverlayScrollbars.js#L1634\n   */\n  static getRtlHelpers() {\n    if (SimpleBarCore.rtlHelpers) {\n      return SimpleBarCore.rtlHelpers;\n    }\n\n    const dummyDiv = document.createElement('div');\n    dummyDiv.innerHTML =\n      '<div class=\"simplebar-dummy-scrollbar-size\"><div></div></div>';\n\n    const scrollbarDummyEl = dummyDiv.firstElementChild;\n    const dummyChild = scrollbarDummyEl?.firstElementChild;\n\n    if (!dummyChild) return null;\n\n    document.body.appendChild(scrollbarDummyEl);\n\n    scrollbarDummyEl.scrollLeft = 0;\n\n    const dummyContainerOffset = SimpleBarCore.getOffset(scrollbarDummyEl);\n    const dummyChildOffset = SimpleBarCore.getOffset(dummyChild);\n\n    scrollbarDummyEl.scrollLeft = -999;\n    const dummyChildOffsetAfterScroll = SimpleBarCore.getOffset(dummyChild);\n\n    document.body.removeChild(scrollbarDummyEl);\n\n    SimpleBarCore.rtlHelpers = {\n      // determines if the scrolling is responding with negative values\n      isScrollOriginAtZero: dummyContainerOffset.left !== dummyChildOffset.left,\n      // determines if the origin scrollbar position is inverted or not (positioned on left or right)\n      isScrollingToNegative:\n        dummyChildOffset.left !== dummyChildOffsetAfterScroll.left,\n    };\n\n    return SimpleBarCore.rtlHelpers;\n  }\n\n  getScrollbarWidth() {\n    // Try/catch for FF 56 throwing on undefined computedStyles\n    try {\n      // Detect browsers supporting CSS scrollbar styling and do not calculate\n      if (\n        (this.contentWrapperEl &&\n          getComputedStyle(this.contentWrapperEl, '::-webkit-scrollbar')\n            .display === 'none') ||\n        'scrollbarWidth' in document.documentElement.style ||\n        '-ms-overflow-style' in document.documentElement.style\n      ) {\n        return 0;\n      } else {\n        return scrollbarWidth();\n      }\n    } catch (e) {\n      return scrollbarWidth();\n    }\n  }\n\n  static getOffset(el: Element) {\n    const rect = el.getBoundingClientRect();\n    const elDocument = getElementDocument(el);\n    const elWindow = getElementWindow(el);\n\n    return {\n      top:\n        rect.top +\n        (elWindow.pageYOffset || elDocument.documentElement.scrollTop),\n      left:\n        rect.left +\n        (elWindow.pageXOffset || elDocument.documentElement.scrollLeft),\n    };\n  }\n\n  init() {\n    // We stop here on server-side\n    if (helpers.canUseDOM) {\n      this.initDOM();\n\n      this.rtlHelpers = SimpleBarCore.getRtlHelpers();\n      this.scrollbarWidth = this.getScrollbarWidth();\n\n      this.recalculate();\n\n      this.initListeners();\n    }\n  }\n\n  initDOM() {\n    // assume that element has his DOM already initiated\n    this.wrapperEl = this.el.querySelector(\n      classNamesToQuery(this.classNames.wrapper)\n    );\n    this.contentWrapperEl =\n      this.options.scrollableNode ||\n      this.el.querySelector(classNamesToQuery(this.classNames.contentWrapper));\n    this.contentEl =\n      this.options.contentNode ||\n      this.el.querySelector(classNamesToQuery(this.classNames.contentEl));\n\n    this.offsetEl = this.el.querySelector(\n      classNamesToQuery(this.classNames.offset)\n    );\n    this.maskEl = this.el.querySelector(\n      classNamesToQuery(this.classNames.mask)\n    );\n\n    this.placeholderEl = this.findChild(\n      this.wrapperEl,\n      classNamesToQuery(this.classNames.placeholder)\n    );\n    this.heightAutoObserverWrapperEl = this.el.querySelector(\n      classNamesToQuery(this.classNames.heightAutoObserverWrapperEl)\n    );\n    this.heightAutoObserverEl = this.el.querySelector(\n      classNamesToQuery(this.classNames.heightAutoObserverEl)\n    );\n    this.axis.x.track.el = this.findChild(\n      this.el,\n      `${classNamesToQuery(this.classNames.track)}${classNamesToQuery(\n        this.classNames.horizontal\n      )}`\n    );\n    this.axis.y.track.el = this.findChild(\n      this.el,\n      `${classNamesToQuery(this.classNames.track)}${classNamesToQuery(\n        this.classNames.vertical\n      )}`\n    );\n\n    this.axis.x.scrollbar.el =\n      this.axis.x.track.el?.querySelector(\n        classNamesToQuery(this.classNames.scrollbar)\n      ) || null;\n    this.axis.y.scrollbar.el =\n      this.axis.y.track.el?.querySelector(\n        classNamesToQuery(this.classNames.scrollbar)\n      ) || null;\n\n    if (!this.options.autoHide) {\n      addClasses(this.axis.x.scrollbar.el, this.classNames.visible);\n      addClasses(this.axis.y.scrollbar.el, this.classNames.visible);\n    }\n  }\n\n  initListeners() {\n    const elWindow = getElementWindow(this.el);\n    // Event listeners\n\n    this.el.addEventListener('mouseenter', this.onMouseEnter);\n\n    this.el.addEventListener('pointerdown', this.onPointerEvent, true);\n\n    this.el.addEventListener('mousemove', this.onMouseMove);\n    this.el.addEventListener('mouseleave', this.onMouseLeave);\n\n    this.contentWrapperEl?.addEventListener('scroll', this.onScroll);\n\n    // Browser zoom triggers a window resize\n    elWindow.addEventListener('resize', this.onWindowResize);\n\n    if (!this.contentEl) return;\n\n    if (window.ResizeObserver) {\n      // Hack for https://github.com/WICG/ResizeObserver/issues/38\n      let resizeObserverStarted = false;\n      const resizeObserver = elWindow.ResizeObserver || ResizeObserver;\n      this.resizeObserver = new resizeObserver(() => {\n        if (!resizeObserverStarted) return;\n\n        elWindow.requestAnimationFrame(() => {\n          this.recalculate();\n        });\n      });\n\n      this.resizeObserver.observe(this.el);\n      this.resizeObserver.observe(this.contentEl);\n\n      elWindow.requestAnimationFrame(() => {\n        resizeObserverStarted = true;\n      });\n    }\n\n    // This is required to detect horizontal scroll. Vertical scroll only needs the resizeObserver.\n    this.mutationObserver = new elWindow.MutationObserver(() => {\n      elWindow.requestAnimationFrame(() => {\n        this.recalculate();\n      });\n    });\n\n    this.mutationObserver.observe(this.contentEl, {\n      childList: true,\n      subtree: true,\n      characterData: true,\n    });\n  }\n\n  recalculate() {\n    if (\n      !this.heightAutoObserverEl ||\n      !this.contentEl ||\n      !this.contentWrapperEl ||\n      !this.wrapperEl ||\n      !this.placeholderEl\n    )\n      return;\n\n    const elWindow = getElementWindow(this.el);\n    this.elStyles = elWindow.getComputedStyle(this.el);\n    this.isRtl = this.elStyles.direction === 'rtl';\n\n    const contentElOffsetWidth = this.contentEl.offsetWidth;\n\n    const isHeightAuto = this.heightAutoObserverEl.offsetHeight <= 1;\n    const isWidthAuto =\n      this.heightAutoObserverEl.offsetWidth <= 1 || contentElOffsetWidth > 0;\n\n    const contentWrapperElOffsetWidth = this.contentWrapperEl.offsetWidth;\n\n    const elOverflowX = this.elStyles.overflowX;\n    const elOverflowY = this.elStyles.overflowY;\n\n    this.contentEl.style.padding = `${this.elStyles.paddingTop} ${this.elStyles.paddingRight} ${this.elStyles.paddingBottom} ${this.elStyles.paddingLeft}`;\n    this.wrapperEl.style.margin = `-${this.elStyles.paddingTop} -${this.elStyles.paddingRight} -${this.elStyles.paddingBottom} -${this.elStyles.paddingLeft}`;\n\n    const contentElScrollHeight = this.contentEl.scrollHeight;\n    const contentElScrollWidth = this.contentEl.scrollWidth;\n\n    this.contentWrapperEl.style.height = isHeightAuto ? 'auto' : '100%';\n\n    // Determine placeholder size\n    this.placeholderEl.style.width = isWidthAuto\n      ? `${contentElOffsetWidth || contentElScrollWidth}px`\n      : 'auto';\n    this.placeholderEl.style.height = `${contentElScrollHeight}px`;\n\n    const contentWrapperElOffsetHeight = this.contentWrapperEl.offsetHeight;\n\n    this.axis.x.isOverflowing =\n      contentElOffsetWidth !== 0 && contentElScrollWidth > contentElOffsetWidth;\n    this.axis.y.isOverflowing =\n      contentElScrollHeight > contentWrapperElOffsetHeight;\n\n    // Set isOverflowing to false if user explicitely set hidden overflow\n    this.axis.x.isOverflowing =\n      elOverflowX === 'hidden' ? false : this.axis.x.isOverflowing;\n    this.axis.y.isOverflowing =\n      elOverflowY === 'hidden' ? false : this.axis.y.isOverflowing;\n\n    this.axis.x.forceVisible =\n      this.options.forceVisible === 'x' || this.options.forceVisible === true;\n    this.axis.y.forceVisible =\n      this.options.forceVisible === 'y' || this.options.forceVisible === true;\n    this.hideNativeScrollbar();\n\n    // Set isOverflowing to false if scrollbar is not necessary (content is shorter than offset)\n    const offsetForXScrollbar = this.axis.x.isOverflowing\n      ? this.scrollbarWidth\n      : 0;\n    const offsetForYScrollbar = this.axis.y.isOverflowing\n      ? this.scrollbarWidth\n      : 0;\n\n    this.axis.x.isOverflowing =\n      this.axis.x.isOverflowing &&\n      contentElScrollWidth > contentWrapperElOffsetWidth - offsetForYScrollbar;\n    this.axis.y.isOverflowing =\n      this.axis.y.isOverflowing &&\n      contentElScrollHeight >\n        contentWrapperElOffsetHeight - offsetForXScrollbar;\n\n    this.axis.x.scrollbar.size = this.getScrollbarSize('x');\n    this.axis.y.scrollbar.size = this.getScrollbarSize('y');\n\n    if (this.axis.x.scrollbar.el)\n      this.axis.x.scrollbar.el.style.width = `${this.axis.x.scrollbar.size}px`;\n    if (this.axis.y.scrollbar.el)\n      this.axis.y.scrollbar.el.style.height = `${this.axis.y.scrollbar.size}px`;\n\n    this.positionScrollbar('x');\n    this.positionScrollbar('y');\n\n    this.toggleTrackVisibility('x');\n    this.toggleTrackVisibility('y');\n  }\n\n  /**\n   * Calculate scrollbar size\n   */\n  getScrollbarSize(axis: Axis = 'y') {\n    if (!this.axis[axis].isOverflowing || !this.contentEl) {\n      return 0;\n    }\n\n    const contentSize = this.contentEl[this.axis[axis].scrollSizeAttr];\n    const trackSize =\n      this.axis[axis].track.el?.[this.axis[axis].offsetSizeAttr] ?? 0;\n    const scrollbarRatio = trackSize / contentSize;\n\n    let scrollbarSize;\n\n    // Calculate new height/position of drag handle.\n    scrollbarSize = Math.max(\n      ~~(scrollbarRatio * trackSize),\n      this.options.scrollbarMinSize\n    );\n\n    if (this.options.scrollbarMaxSize) {\n      scrollbarSize = Math.min(scrollbarSize, this.options.scrollbarMaxSize);\n    }\n\n    return scrollbarSize;\n  }\n\n  positionScrollbar(axis: Axis = 'y') {\n    const scrollbar = this.axis[axis].scrollbar;\n\n    if (\n      !this.axis[axis].isOverflowing ||\n      !this.contentWrapperEl ||\n      !scrollbar.el ||\n      !this.elStyles\n    ) {\n      return;\n    }\n\n    const contentSize = this.contentWrapperEl[this.axis[axis].scrollSizeAttr];\n    const trackSize =\n      this.axis[axis].track.el?.[this.axis[axis].offsetSizeAttr] || 0;\n    const hostSize = parseInt(this.elStyles[this.axis[axis].sizeAttr], 10);\n\n    let scrollOffset = this.contentWrapperEl[this.axis[axis].scrollOffsetAttr];\n\n    scrollOffset =\n      axis === 'x' &&\n      this.isRtl &&\n      SimpleBarCore.getRtlHelpers()?.isScrollOriginAtZero\n        ? -scrollOffset\n        : scrollOffset;\n\n    if (axis === 'x' && this.isRtl) {\n      scrollOffset = SimpleBarCore.getRtlHelpers()?.isScrollingToNegative\n        ? scrollOffset\n        : -scrollOffset;\n    }\n\n    const scrollPourcent = scrollOffset / (contentSize - hostSize);\n\n    let handleOffset = ~~((trackSize - scrollbar.size) * scrollPourcent);\n    handleOffset =\n      axis === 'x' && this.isRtl\n        ? -handleOffset + (trackSize - scrollbar.size)\n        : handleOffset;\n\n    scrollbar.el.style.transform =\n      axis === 'x'\n        ? `translate3d(${handleOffset}px, 0, 0)`\n        : `translate3d(0, ${handleOffset}px, 0)`;\n  }\n\n  toggleTrackVisibility(axis: Axis = 'y') {\n    const track = this.axis[axis].track.el;\n    const scrollbar = this.axis[axis].scrollbar.el;\n\n    if (!track || !scrollbar || !this.contentWrapperEl) return;\n    if (this.axis[axis].isOverflowing || this.axis[axis].forceVisible) {\n      track.style.visibility = 'visible';\n      this.contentWrapperEl.style[this.axis[axis].overflowAttr] = 'scroll';\n      this.el.classList.add(`${this.classNames.scrollable}-${axis}`);\n    } else {\n      track.style.visibility = 'hidden';\n      this.contentWrapperEl.style[this.axis[axis].overflowAttr] = 'hidden';\n      this.el.classList.remove(`${this.classNames.scrollable}-${axis}`);\n    }\n\n    // Even if forceVisible is enabled, scrollbar itself should be hidden\n    if (this.axis[axis].isOverflowing) {\n      scrollbar.style.display = 'block';\n    } else {\n      scrollbar.style.display = 'none';\n    }\n  }\n\n  showScrollbar(axis: Axis = 'y') {\n    if (this.axis[axis].isOverflowing && !this.axis[axis].scrollbar.isVisible) {\n      addClasses(this.axis[axis].scrollbar.el, this.classNames.visible);\n      this.axis[axis].scrollbar.isVisible = true;\n    }\n  }\n\n  hideScrollbar(axis: Axis = 'y') {\n    if (this.isDragging) return;\n    if (this.axis[axis].isOverflowing && this.axis[axis].scrollbar.isVisible) {\n      removeClasses(this.axis[axis].scrollbar.el, this.classNames.visible);\n      this.axis[axis].scrollbar.isVisible = false;\n    }\n  }\n\n  hideNativeScrollbar() {\n    if (!this.offsetEl) return;\n\n    this.offsetEl.style[this.isRtl ? 'left' : 'right'] =\n      this.axis.y.isOverflowing || this.axis.y.forceVisible\n        ? `-${this.scrollbarWidth}px`\n        : '0px';\n    this.offsetEl.style.bottom =\n      this.axis.x.isOverflowing || this.axis.x.forceVisible\n        ? `-${this.scrollbarWidth}px`\n        : '0px';\n  }\n\n  /**\n   * On scroll event handling\n   */\n  onScroll = () => {\n    const elWindow = getElementWindow(this.el);\n\n    if (!this.scrollXTicking) {\n      elWindow.requestAnimationFrame(this.scrollX);\n      this.scrollXTicking = true;\n    }\n\n    if (!this.scrollYTicking) {\n      elWindow.requestAnimationFrame(this.scrollY);\n      this.scrollYTicking = true;\n    }\n\n    if (!this.isScrolling) {\n      this.isScrolling = true;\n      addClasses(this.el, this.classNames.scrolling);\n    }\n\n    this.showScrollbar('x');\n    this.showScrollbar('y');\n\n    this.onStopScrolling();\n  };\n\n  scrollX = () => {\n    if (this.axis.x.isOverflowing) {\n      this.positionScrollbar('x');\n    }\n\n    this.scrollXTicking = false;\n  };\n\n  scrollY = () => {\n    if (this.axis.y.isOverflowing) {\n      this.positionScrollbar('y');\n    }\n\n    this.scrollYTicking = false;\n  };\n\n  _onStopScrolling = () => {\n    removeClasses(this.el, this.classNames.scrolling);\n    if (this.options.autoHide) {\n      this.hideScrollbar('x');\n      this.hideScrollbar('y');\n    }\n    this.isScrolling = false;\n  };\n\n  onMouseEnter = () => {\n    if (!this.isMouseEntering) {\n      addClasses(this.el, this.classNames.mouseEntered);\n      this.showScrollbar('x');\n      this.showScrollbar('y');\n      this.isMouseEntering = true;\n    }\n    this.onMouseEntered();\n  };\n\n  _onMouseEntered = () => {\n    removeClasses(this.el, this.classNames.mouseEntered);\n    if (this.options.autoHide) {\n      this.hideScrollbar('x');\n      this.hideScrollbar('y');\n    }\n    this.isMouseEntering = false;\n  };\n\n  _onMouseMove = (e: any) => {\n    this.mouseX = e.clientX;\n    this.mouseY = e.clientY;\n\n    if (this.axis.x.isOverflowing || this.axis.x.forceVisible) {\n      this.onMouseMoveForAxis('x');\n    }\n\n    if (this.axis.y.isOverflowing || this.axis.y.forceVisible) {\n      this.onMouseMoveForAxis('y');\n    }\n  };\n\n  onMouseMoveForAxis(axis: Axis = 'y') {\n    const currentAxis = this.axis[axis];\n    if (!currentAxis.track.el || !currentAxis.scrollbar.el) return;\n\n    currentAxis.track.rect = currentAxis.track.el.getBoundingClientRect();\n    currentAxis.scrollbar.rect =\n      currentAxis.scrollbar.el.getBoundingClientRect();\n\n    if (this.isWithinBounds(currentAxis.track.rect)) {\n      this.showScrollbar(axis);\n      addClasses(currentAxis.track.el, this.classNames.hover);\n\n      if (this.isWithinBounds(currentAxis.scrollbar.rect)) {\n        addClasses(currentAxis.scrollbar.el, this.classNames.hover);\n      } else {\n        removeClasses(currentAxis.scrollbar.el, this.classNames.hover);\n      }\n    } else {\n      removeClasses(currentAxis.track.el, this.classNames.hover);\n      if (this.options.autoHide) {\n        this.hideScrollbar(axis);\n      }\n    }\n  }\n\n  onMouseLeave = () => {\n    (this.onMouseMove as DebouncedFunc<any>).cancel();\n\n    if (this.axis.x.isOverflowing || this.axis.x.forceVisible) {\n      this.onMouseLeaveForAxis('x');\n    }\n\n    if (this.axis.y.isOverflowing || this.axis.y.forceVisible) {\n      this.onMouseLeaveForAxis('y');\n    }\n\n    this.mouseX = -1;\n    this.mouseY = -1;\n  };\n\n  onMouseLeaveForAxis(axis: Axis = 'y') {\n    removeClasses(this.axis[axis].track.el, this.classNames.hover);\n    removeClasses(this.axis[axis].scrollbar.el, this.classNames.hover);\n    if (this.options.autoHide) {\n      this.hideScrollbar(axis);\n    }\n  }\n\n  _onWindowResize = () => {\n    // Recalculate scrollbarWidth in case it's a zoom\n    this.scrollbarWidth = this.getScrollbarWidth();\n\n    this.hideNativeScrollbar();\n  };\n\n  onPointerEvent = (e: any) => {\n    if (\n      !this.axis.x.track.el ||\n      !this.axis.y.track.el ||\n      !this.axis.x.scrollbar.el ||\n      !this.axis.y.scrollbar.el\n    )\n      return;\n\n    let isWithinTrackXBounds, isWithinTrackYBounds;\n\n    this.axis.x.track.rect = this.axis.x.track.el.getBoundingClientRect();\n    this.axis.y.track.rect = this.axis.y.track.el.getBoundingClientRect();\n\n    if (this.axis.x.isOverflowing || this.axis.x.forceVisible) {\n      isWithinTrackXBounds = this.isWithinBounds(this.axis.x.track.rect);\n    }\n\n    if (this.axis.y.isOverflowing || this.axis.y.forceVisible) {\n      isWithinTrackYBounds = this.isWithinBounds(this.axis.y.track.rect);\n    }\n\n    // If any pointer event is called on the scrollbar\n    if (isWithinTrackXBounds || isWithinTrackYBounds) {\n      // Prevent event leaking\n      e.stopPropagation();\n\n      if (e.type === 'pointerdown' && e.pointerType !== 'touch') {\n        if (isWithinTrackXBounds) {\n          this.axis.x.scrollbar.rect =\n            this.axis.x.scrollbar.el.getBoundingClientRect();\n\n          if (this.isWithinBounds(this.axis.x.scrollbar.rect)) {\n            this.onDragStart(e, 'x');\n          } else {\n            this.onTrackClick(e, 'x');\n          }\n        }\n\n        if (isWithinTrackYBounds) {\n          this.axis.y.scrollbar.rect =\n            this.axis.y.scrollbar.el.getBoundingClientRect();\n\n          if (this.isWithinBounds(this.axis.y.scrollbar.rect)) {\n            this.onDragStart(e, 'y');\n          } else {\n            this.onTrackClick(e, 'y');\n          }\n        }\n      }\n    }\n  };\n\n  /**\n   * on scrollbar handle drag movement starts\n   */\n  onDragStart(e: any, axis: Axis = 'y') {\n    this.isDragging = true;\n    const elDocument = getElementDocument(this.el);\n    const elWindow = getElementWindow(this.el);\n    const scrollbar = this.axis[axis].scrollbar;\n\n    // Measure how far the user's mouse is from the top of the scrollbar drag handle.\n    const eventOffset = axis === 'y' ? e.pageY : e.pageX;\n    this.axis[axis].dragOffset =\n      eventOffset - (scrollbar.rect?.[this.axis[axis].offsetAttr] || 0);\n    this.draggedAxis = axis;\n\n    addClasses(this.el, this.classNames.dragging);\n\n    elDocument.addEventListener('mousemove', this.drag, true);\n    elDocument.addEventListener('mouseup', this.onEndDrag, true);\n    if (this.removePreventClickId === null) {\n      elDocument.addEventListener('click', this.preventClick, true);\n      elDocument.addEventListener('dblclick', this.preventClick, true);\n    } else {\n      elWindow.clearTimeout(this.removePreventClickId);\n      this.removePreventClickId = null;\n    }\n  }\n\n  /**\n   * Drag scrollbar handle\n   */\n  drag = (e: any) => {\n    if (!this.draggedAxis || !this.contentWrapperEl) return;\n\n    let eventOffset;\n    const track = this.axis[this.draggedAxis].track;\n    const trackSize = track.rect?.[this.axis[this.draggedAxis].sizeAttr] ?? 0;\n    const scrollbar = this.axis[this.draggedAxis].scrollbar;\n    const contentSize =\n      this.contentWrapperEl?.[this.axis[this.draggedAxis].scrollSizeAttr] ?? 0;\n    const hostSize = parseInt(\n      this.elStyles?.[this.axis[this.draggedAxis].sizeAttr] ?? '0px',\n      10\n    );\n\n    e.preventDefault();\n    e.stopPropagation();\n\n    if (this.draggedAxis === 'y') {\n      eventOffset = e.pageY;\n    } else {\n      eventOffset = e.pageX;\n    }\n\n    // Calculate how far the user's mouse is from the top/left of the scrollbar (minus the dragOffset).\n    let dragPos =\n      eventOffset -\n      (track.rect?.[this.axis[this.draggedAxis].offsetAttr] ?? 0) -\n      this.axis[this.draggedAxis].dragOffset;\n    dragPos =\n      this.draggedAxis === 'x' && this.isRtl\n        ? (track.rect?.[this.axis[this.draggedAxis].sizeAttr] ?? 0) -\n          scrollbar.size -\n          dragPos\n        : dragPos;\n    // Convert the mouse position into a percentage of the scrollbar height/width.\n    const dragPerc = dragPos / (trackSize - scrollbar.size);\n\n    // Scroll the content by the same percentage.\n    let scrollPos = dragPerc * (contentSize - hostSize);\n\n    // Fix browsers inconsistency on RTL\n    if (this.draggedAxis === 'x' && this.isRtl) {\n      scrollPos = SimpleBarCore.getRtlHelpers()?.isScrollingToNegative\n        ? -scrollPos\n        : scrollPos;\n    }\n\n    this.contentWrapperEl[this.axis[this.draggedAxis].scrollOffsetAttr] =\n      scrollPos;\n  };\n\n  /**\n   * End scroll handle drag\n   */\n  onEndDrag = (e: any) => {\n    this.isDragging = false;\n    const elDocument = getElementDocument(this.el);\n    const elWindow = getElementWindow(this.el);\n    e.preventDefault();\n    e.stopPropagation();\n\n    removeClasses(this.el, this.classNames.dragging);\n    this.onStopScrolling();\n\n    elDocument.removeEventListener('mousemove', this.drag, true);\n    elDocument.removeEventListener('mouseup', this.onEndDrag, true);\n    this.removePreventClickId = elWindow.setTimeout(() => {\n      // Remove these asynchronously so we still suppress click events\n      // generated simultaneously with mouseup.\n      elDocument.removeEventListener('click', this.preventClick, true);\n      elDocument.removeEventListener('dblclick', this.preventClick, true);\n      this.removePreventClickId = null;\n    });\n  };\n\n  /**\n   * Handler to ignore click events during drag\n   */\n  preventClick = (e: any) => {\n    e.preventDefault();\n    e.stopPropagation();\n  };\n\n  onTrackClick(e: any, axis: Axis = 'y') {\n    const currentAxis = this.axis[axis];\n    if (\n      !this.options.clickOnTrack ||\n      !currentAxis.scrollbar.el ||\n      !this.contentWrapperEl\n    )\n      return;\n\n    // Preventing the event's default to trigger click underneath\n    e.preventDefault();\n\n    const elWindow = getElementWindow(this.el);\n    this.axis[axis].scrollbar.rect =\n      currentAxis.scrollbar.el.getBoundingClientRect();\n    const scrollbar = this.axis[axis].scrollbar;\n    const scrollbarOffset = scrollbar.rect?.[this.axis[axis].offsetAttr] ?? 0;\n    const hostSize = parseInt(\n      this.elStyles?.[this.axis[axis].sizeAttr] ?? '0px',\n      10\n    );\n    let scrolled = this.contentWrapperEl[this.axis[axis].scrollOffsetAttr];\n    const t =\n      axis === 'y'\n        ? this.mouseY - scrollbarOffset\n        : this.mouseX - scrollbarOffset;\n    const dir = t < 0 ? -1 : 1;\n    const scrollSize = dir === -1 ? scrolled - hostSize : scrolled + hostSize;\n    const speed = 40;\n\n    const scrollTo = () => {\n      if (!this.contentWrapperEl) return;\n      if (dir === -1) {\n        if (scrolled > scrollSize) {\n          scrolled -= speed;\n          this.contentWrapperEl[this.axis[axis].scrollOffsetAttr] = scrolled;\n          elWindow.requestAnimationFrame(scrollTo);\n        }\n      } else {\n        if (scrolled < scrollSize) {\n          scrolled += speed;\n          this.contentWrapperEl[this.axis[axis].scrollOffsetAttr] = scrolled;\n          elWindow.requestAnimationFrame(scrollTo);\n        }\n      }\n    };\n\n    scrollTo();\n  }\n\n  /**\n   * Getter for content element\n   */\n  getContentElement() {\n    return this.contentEl;\n  }\n\n  /**\n   * Getter for original scrolling element\n   */\n  getScrollElement() {\n    return this.contentWrapperEl;\n  }\n\n  removeListeners() {\n    const elWindow = getElementWindow(this.el);\n    // Event listeners\n    this.el.removeEventListener('mouseenter', this.onMouseEnter);\n\n    this.el.removeEventListener('pointerdown', this.onPointerEvent, true);\n\n    this.el.removeEventListener('mousemove', this.onMouseMove);\n    this.el.removeEventListener('mouseleave', this.onMouseLeave);\n\n    if (this.contentWrapperEl) {\n      this.contentWrapperEl.removeEventListener('scroll', this.onScroll);\n    }\n\n    elWindow.removeEventListener('resize', this.onWindowResize);\n\n    if (this.mutationObserver) {\n      this.mutationObserver.disconnect();\n    }\n\n    if (this.resizeObserver) {\n      this.resizeObserver.disconnect();\n    }\n\n    // Cancel all debounced functions\n    (this.onMouseMove as DebouncedFunc<any>).cancel();\n    (this.onWindowResize as DebouncedFunc<any>).cancel();\n    (this.onStopScrolling as DebouncedFunc<any>).cancel();\n    (this.onMouseEntered as DebouncedFunc<any>).cancel();\n  }\n\n  /**\n   * Remove all listeners from DOM nodes\n   */\n  unMount() {\n    this.removeListeners();\n  }\n\n  /**\n   * Check if mouse is within bounds\n   */\n  isWithinBounds(bbox: DOMRect) {\n    return (\n      this.mouseX >= bbox.left &&\n      this.mouseX <= bbox.left + bbox.width &&\n      this.mouseY >= bbox.top &&\n      this.mouseY <= bbox.top + bbox.height\n    );\n  }\n\n  /**\n   * Find element children matches query\n   */\n  findChild(el: any, query: any) {\n    const matches =\n      el.matches ||\n      el.webkitMatchesSelector ||\n      el.mozMatchesSelector ||\n      el.msMatchesSelector;\n    return Array.prototype.filter.call(el.children, (child) =>\n      matches.call(child, query)\n    )[0];\n  }\n}\n"], "mappings": ";;;;;;;;AASA,YAAuB;;;ACgBvB,SAAS,SAAS,OAAO;AACvB,MAAI,OAAO,OAAO;AAClB,SAAO,SAAS,SAAS,QAAQ,YAAY,QAAQ;AACvD;AAEA,IAAO,mBAAQ;;;AC7Bf,IAAI,aAAa,OAAO,UAAU,YAAY,UAAU,OAAO,WAAW,UAAU;AAEpF,IAAO,qBAAQ;;;ACAf,IAAI,WAAW,OAAO,QAAQ,YAAY,QAAQ,KAAK,WAAW,UAAU;AAG5E,IAAI,OAAO,sBAAc,YAAY,SAAS,aAAa,EAAE;AAE7D,IAAO,eAAQ;;;ACUf,IAAI,MAAM,WAAW;AACnB,SAAO,aAAK,KAAK,IAAI;AACvB;AAEA,IAAO,cAAQ;;;ACrBf,IAAI,eAAe;AAUnB,SAAS,gBAAgB,QAAQ;AAC/B,MAAI,QAAQ,OAAO;AAEnB,SAAO,WAAW,aAAa,KAAK,OAAO,OAAO,KAAK,CAAC,GAAG;AAAA,EAAC;AAC5D,SAAO;AACT;AAEA,IAAO,0BAAQ;;;ACff,IAAI,cAAc;AASlB,SAAS,SAAS,QAAQ;AACxB,SAAO,SACH,OAAO,MAAM,GAAG,wBAAgB,MAAM,IAAI,CAAC,EAAE,QAAQ,aAAa,EAAE,IACpE;AACN;AAEA,IAAO,mBAAQ;;;ACff,IAAI,SAAS,aAAK;AAElB,IAAO,iBAAQ;;;ACFf,IAAI,cAAc,OAAO;AAGzB,IAAI,iBAAiB,YAAY;AAOjC,IAAI,uBAAuB,YAAY;AAGvC,IAAI,iBAAiB,iBAAS,eAAO,cAAc;AASnD,SAAS,UAAU,OAAO;AACxB,MAAI,QAAQ,eAAe,KAAK,OAAO,cAAc,GACjD,MAAM,MAAM,cAAc;AAE9B,MAAI;AACF,UAAM,cAAc,IAAI;AACxB,QAAI,WAAW;AAAA,EACjB,SAAS,GAAG;AAAA,EAAC;AAEb,MAAI,SAAS,qBAAqB,KAAK,KAAK;AAC5C,MAAI,UAAU;AACZ,QAAI,OAAO;AACT,YAAM,cAAc,IAAI;AAAA,IAC1B,OAAO;AACL,aAAO,MAAM,cAAc;AAAA,IAC7B;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAO,oBAAQ;;;AC5Cf,IAAIA,eAAc,OAAO;AAOzB,IAAIC,wBAAuBD,aAAY;AASvC,SAAS,eAAe,OAAO;AAC7B,SAAOC,sBAAqB,KAAK,KAAK;AACxC;AAEA,IAAO,yBAAQ;;;AChBf,IAAI,UAAU;AAAd,IACI,eAAe;AAGnB,IAAIC,kBAAiB,iBAAS,eAAO,cAAc;AASnD,SAAS,WAAW,OAAO;AACzB,MAAI,SAAS,MAAM;AACjB,WAAO,UAAU,SAAY,eAAe;AAAA,EAC9C;AACA,SAAQA,mBAAkBA,mBAAkB,OAAO,KAAK,IACpD,kBAAU,KAAK,IACf,uBAAe,KAAK;AAC1B;AAEA,IAAO,qBAAQ;;;ACHf,SAAS,aAAa,OAAO;AAC3B,SAAO,SAAS,QAAQ,OAAO,SAAS;AAC1C;AAEA,IAAO,uBAAQ;;;ACxBf,IAAI,YAAY;AAmBhB,SAAS,SAAS,OAAO;AACvB,SAAO,OAAO,SAAS,YACpB,qBAAa,KAAK,KAAK,mBAAW,KAAK,KAAK;AACjD;AAEA,IAAO,mBAAQ;;;ACvBf,IAAI,MAAM,IAAI;AAGd,IAAI,aAAa;AAGjB,IAAI,aAAa;AAGjB,IAAI,YAAY;AAGhB,IAAI,eAAe;AAyBnB,SAAS,SAAS,OAAO;AACvB,MAAI,OAAO,SAAS,UAAU;AAC5B,WAAO;AAAA,EACT;AACA,MAAI,iBAAS,KAAK,GAAG;AACnB,WAAO;AAAA,EACT;AACA,MAAI,iBAAS,KAAK,GAAG;AACnB,QAAI,QAAQ,OAAO,MAAM,WAAW,aAAa,MAAM,QAAQ,IAAI;AACnE,YAAQ,iBAAS,KAAK,IAAK,QAAQ,KAAM;AAAA,EAC3C;AACA,MAAI,OAAO,SAAS,UAAU;AAC5B,WAAO,UAAU,IAAI,QAAQ,CAAC;AAAA,EAChC;AACA,UAAQ,iBAAS,KAAK;AACtB,MAAI,WAAW,WAAW,KAAK,KAAK;AACpC,SAAQ,YAAY,UAAU,KAAK,KAAK,IACpC,aAAa,MAAM,MAAM,CAAC,GAAG,WAAW,IAAI,CAAC,IAC5C,WAAW,KAAK,KAAK,IAAI,MAAM,CAAC;AACvC;AAEA,IAAO,mBAAQ;;;AC1Df,IAAI,kBAAkB;AAGtB,IAAI,YAAY,KAAK;AAArB,IACI,YAAY,KAAK;AAwDrB,SAAS,SAAS,MAAM,MAAM,SAAS;AACrC,MAAI,UACA,UACA,SACA,QACA,SACA,cACA,iBAAiB,GACjB,UAAU,OACV,SAAS,OACT,WAAW;AAEf,MAAI,OAAO,QAAQ,YAAY;AAC7B,UAAM,IAAI,UAAU,eAAe;AAAA,EACrC;AACA,SAAO,iBAAS,IAAI,KAAK;AACzB,MAAI,iBAAS,OAAO,GAAG;AACrB,cAAU,CAAC,CAAC,QAAQ;AACpB,aAAS,aAAa;AACtB,cAAU,SAAS,UAAU,iBAAS,QAAQ,OAAO,KAAK,GAAG,IAAI,IAAI;AACrE,eAAW,cAAc,UAAU,CAAC,CAAC,QAAQ,WAAW;AAAA,EAC1D;AAEA,WAAS,WAAW,MAAM;AACxB,QAAI,OAAO,UACP,UAAU;AAEd,eAAW,WAAW;AACtB,qBAAiB;AACjB,aAAS,KAAK,MAAM,SAAS,IAAI;AACjC,WAAO;AAAA,EACT;AAEA,WAAS,YAAY,MAAM;AAEzB,qBAAiB;AAEjB,cAAU,WAAW,cAAc,IAAI;AAEvC,WAAO,UAAU,WAAW,IAAI,IAAI;AAAA,EACtC;AAEA,WAAS,cAAc,MAAM;AAC3B,QAAI,oBAAoB,OAAO,cAC3B,sBAAsB,OAAO,gBAC7B,cAAc,OAAO;AAEzB,WAAO,SACH,UAAU,aAAa,UAAU,mBAAmB,IACpD;AAAA,EACN;AAEA,WAAS,aAAa,MAAM;AAC1B,QAAI,oBAAoB,OAAO,cAC3B,sBAAsB,OAAO;AAKjC,WAAQ,iBAAiB,UAAc,qBAAqB,QACzD,oBAAoB,KAAO,UAAU,uBAAuB;AAAA,EACjE;AAEA,WAAS,eAAe;AACtB,QAAI,OAAO,YAAI;AACf,QAAI,aAAa,IAAI,GAAG;AACtB,aAAO,aAAa,IAAI;AAAA,IAC1B;AAEA,cAAU,WAAW,cAAc,cAAc,IAAI,CAAC;AAAA,EACxD;AAEA,WAAS,aAAa,MAAM;AAC1B,cAAU;AAIV,QAAI,YAAY,UAAU;AACxB,aAAO,WAAW,IAAI;AAAA,IACxB;AACA,eAAW,WAAW;AACtB,WAAO;AAAA,EACT;AAEA,WAAS,SAAS;AAChB,QAAI,YAAY,QAAW;AACzB,mBAAa,OAAO;AAAA,IACtB;AACA,qBAAiB;AACjB,eAAW,eAAe,WAAW,UAAU;AAAA,EACjD;AAEA,WAAS,QAAQ;AACf,WAAO,YAAY,SAAY,SAAS,aAAa,YAAI,CAAC;AAAA,EAC5D;AAEA,WAAS,YAAY;AACnB,QAAI,OAAO,YAAI,GACX,aAAa,aAAa,IAAI;AAElC,eAAW;AACX,eAAW;AACX,mBAAe;AAEf,QAAI,YAAY;AACd,UAAI,YAAY,QAAW;AACzB,eAAO,YAAY,YAAY;AAAA,MACjC;AACA,UAAI,QAAQ;AAEV,qBAAa,OAAO;AACpB,kBAAU,WAAW,cAAc,IAAI;AACvC,eAAO,WAAW,YAAY;AAAA,MAChC;AAAA,IACF;AACA,QAAI,YAAY,QAAW;AACzB,gBAAU,WAAW,cAAc,IAAI;AAAA,IACzC;AACA,WAAO;AAAA,EACT;AACA,YAAU,SAAS;AACnB,YAAU,QAAQ;AAClB,SAAO;AACT;AAEA,IAAO,mBAAQ;;;AC1Lf,IAAIC,mBAAkB;AA8CtB,SAAS,SAAS,MAAM,MAAM,SAAS;AACrC,MAAI,UAAU,MACV,WAAW;AAEf,MAAI,OAAO,QAAQ,YAAY;AAC7B,UAAM,IAAI,UAAUA,gBAAe;AAAA,EACrC;AACA,MAAI,iBAAS,OAAO,GAAG;AACrB,cAAU,aAAa,UAAU,CAAC,CAAC,QAAQ,UAAU;AACrD,eAAW,cAAc,UAAU,CAAC,CAAC,QAAQ,WAAW;AAAA,EAC1D;AACA,SAAO,iBAAS,MAAM,MAAM;AAAA,IAC1B,WAAW;AAAA,IACX,WAAW;AAAA,IACX,YAAY;AAAA,EACd,CAAC;AACH;AAEA,IAAO,mBAAQ;;;ACrCR,IAAI,WAAW,WAAW;AAC7B,aAAW,OAAO,UAAU,SAASC,UAAS,GAAG;AAC7C,aAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,UAAI,UAAU,CAAC;AACf,eAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;IAC9E;AACD,WAAO;EACV;AACD,SAAO,SAAS,MAAM,MAAM,SAAS;AACzC;ACtCM,SAAUC,mBAAiB,SAAgB;AAC/C,MACE,CAAC,WACD,CAAC,QAAQ,iBACT,CAAC,QAAQ,cAAc,aACvB;AACA,WAAO;EACR;AACD,SAAO,QAAQ,cAAc;AAC/B;AAEM,SAAUC,qBAAmB,SAAgB;AACjD,MAAI,CAAC,WAAW,CAAC,QAAQ,eAAe;AACtC,WAAO;EACR;AACD,SAAO,QAAQ;AACjB;AAGO,IAAMC,eAAa,SAAU,KAAQ;AAC1C,MAAM,aAA+B,CAAA;AAErC,MAAM,UAAU,MAAM,UAAU,OAAO,KACrC,KACA,SAAC,KAAU,WAAS;AAClB,QAAM,SAAS,UAAU,KAAK,MAAM,qBAAqB;AACzD,QAAI,QAAQ;AACV,UAAM,MAA8B,OAAO,CAAC,EAAE,QAC5C,WACA,SAAC,GAAQ,KAAgB;AAAA,eAAA,IAAI,YAAa;MAAA,CAAA;AAG5C,cAAQ,UAAU,OAAK;QACrB,KAAK;AACH,cAAI,GAAG,IAAI;AACX;QACF,KAAK;AACH,cAAI,GAAG,IAAI;AACX;QACF,KAAK;AACH,cAAI,GAAG,IAAI;AACX;QACF;AACE,cAAI,GAAG,IAAI,UAAU;MACxB;IACF;AACD,WAAO;EACR,GACD,UAAU;AAEZ,SAAO;AACT;AAEgB,SAAAC,aAAW,IAAwB,SAAe;;AAChE,MAAI,CAAC;AAAI;AACT,GAAA,KAAA,GAAG,WAAU,IAAO,MAAA,IAAA,QAAQ,MAAM,GAAG,CAAC;AACxC;AAEgB,SAAAC,gBAAc,IAAwB,SAAe;AACnE,MAAI,CAAC;AAAI;AACT,UAAQ,MAAM,GAAG,EAAE,QAAQ,SAAC,WAAS;AACnC,OAAG,UAAU,OAAO,SAAS;EAC/B,CAAC;AACH;AAEM,SAAUC,oBAAkB,YAAkB;AAClD,SAAO,IAAI,OAAA,WAAW,MAAM,GAAG,EAAE,KAAK,GAAG,CAAC;AAC5C;AAEO,IAAM,YAAY,CAAC,EACxB,OAAO,WAAW,eAClB,OAAO,YACP,OAAO,SAAS;;;;;;;;;;;ACxElB,IAAI,uBAAsC;AAC1C,IAAI,yBAAwC;AAE5C,IAAI,WAAW;AACb,SAAO,iBAAiB,UAAU,WAAA;AAChC,QAAI,2BAA2B,OAAO,kBAAkB;AACtD,+BAAyB,OAAO;AAChC,6BAAuB;IACxB;EACH,CAAC;AACF;AAEa,SAAU,iBAAc;AACpC,MAAI,yBAAyB,MAAM;AACjC,QAAI,OAAO,aAAa,aAAa;AACnC,6BAAuB;AACvB,aAAO;IACR;AAED,QAAM,OAAO,SAAS;AACtB,QAAM,MAAM,SAAS,cAAc,KAAK;AAExC,QAAI,UAAU,IAAI,0BAA0B;AAE5C,SAAK,YAAY,GAAG;AAEpB,QAAM,QAAQ,IAAI,sBAAqB,EAAG;AAE1C,SAAK,YAAY,GAAG;AAEpB,2BAAuB;EACxB;AAED,SAAO;AACT;ACwCE,IAAA,mBAMEC;AANF,IACA,qBAKEC;AANF,IAEA,aAIEC;AANF,IAGA,aAGEC;AANF,IAIA,gBAEEC;AANF,IAKA,oBACEC;AAEJ,IAAA;;GAAA,WAAA;AAwEE,aAAYC,eAAA,SAAsB,SAA8B;AAA9B,UAAA,YAAA,QAAA;AAAA,kBAA8B,CAAA;MAAA;AAAhE,UAmDC,QAAA;AAlHD,WAAoB,uBAAkB;AACtC,WAAiB,oBAAG;AACpB,WAAe,kBAAG;AAClB,WAAW,cAAG;AACd,WAAe,kBAAG;AAClB,WAAU,aAAG;AACb,WAAc,iBAAG;AACjB,WAAc,iBAAG;AACjB,WAAS,YAAuB;AAChC,WAAgB,mBAAuB;AACvC,WAAS,YAAuB;AAChC,WAAQ,WAAuB;AAC/B,WAAM,SAAuB;AAC7B,WAAa,gBAAuB;AACpC,WAA2B,8BAAuB;AAClD,WAAoB,uBAAuB;AAC3C,WAAU,aAAe;AACzB,WAAc,iBAAW;AACzB,WAAc,iBAA0B;AACxC,WAAgB,mBAA4B;AAC5C,WAAQ,WAA+B;AACvC,WAAK,QAAmB;AACxB,WAAM,SAAW;AACjB,WAAM,SAAW;AACjB,WAAW,cAAsC,WAAO;MAAA;AACxD,WAAc,iBAAsC,WAAO;MAAA;AAC3D,WAAe,kBAAsC,WAAO;MAAA;AAC5D,WAAc,iBAAsC,WAAO;MAAA;AAkgB3D,WAAA,WAAW,WAAA;AACT,YAAM,WAAW,iBAAiB,MAAK,EAAE;AAEzC,YAAI,CAAC,MAAK,gBAAgB;AACxB,mBAAS,sBAAsB,MAAK,OAAO;AAC3C,gBAAK,iBAAiB;QACvB;AAED,YAAI,CAAC,MAAK,gBAAgB;AACxB,mBAAS,sBAAsB,MAAK,OAAO;AAC3C,gBAAK,iBAAiB;QACvB;AAED,YAAI,CAAC,MAAK,aAAa;AACrB,gBAAK,cAAc;AACnB,qBAAW,MAAK,IAAI,MAAK,WAAW,SAAS;QAC9C;AAED,cAAK,cAAc,GAAG;AACtB,cAAK,cAAc,GAAG;AAEtB,cAAK,gBAAe;MACtB;AAEA,WAAA,UAAU,WAAA;AACR,YAAI,MAAK,KAAK,EAAE,eAAe;AAC7B,gBAAK,kBAAkB,GAAG;QAC3B;AAED,cAAK,iBAAiB;MACxB;AAEA,WAAA,UAAU,WAAA;AACR,YAAI,MAAK,KAAK,EAAE,eAAe;AAC7B,gBAAK,kBAAkB,GAAG;QAC3B;AAED,cAAK,iBAAiB;MACxB;AAEA,WAAA,mBAAmB,WAAA;AACjB,sBAAc,MAAK,IAAI,MAAK,WAAW,SAAS;AAChD,YAAI,MAAK,QAAQ,UAAU;AACzB,gBAAK,cAAc,GAAG;AACtB,gBAAK,cAAc,GAAG;QACvB;AACD,cAAK,cAAc;MACrB;AAEA,WAAA,eAAe,WAAA;AACb,YAAI,CAAC,MAAK,iBAAiB;AACzB,qBAAW,MAAK,IAAI,MAAK,WAAW,YAAY;AAChD,gBAAK,cAAc,GAAG;AACtB,gBAAK,cAAc,GAAG;AACtB,gBAAK,kBAAkB;QACxB;AACD,cAAK,eAAc;MACrB;AAEA,WAAA,kBAAkB,WAAA;AAChB,sBAAc,MAAK,IAAI,MAAK,WAAW,YAAY;AACnD,YAAI,MAAK,QAAQ,UAAU;AACzB,gBAAK,cAAc,GAAG;AACtB,gBAAK,cAAc,GAAG;QACvB;AACD,cAAK,kBAAkB;MACzB;AAEA,WAAY,eAAG,SAAC,GAAM;AACpB,cAAK,SAAS,EAAE;AAChB,cAAK,SAAS,EAAE;AAEhB,YAAI,MAAK,KAAK,EAAE,iBAAiB,MAAK,KAAK,EAAE,cAAc;AACzD,gBAAK,mBAAmB,GAAG;QAC5B;AAED,YAAI,MAAK,KAAK,EAAE,iBAAiB,MAAK,KAAK,EAAE,cAAc;AACzD,gBAAK,mBAAmB,GAAG;QAC5B;MACH;AA2BA,WAAA,eAAe,WAAA;AACZ,cAAK,YAAmC,OAAM;AAE/C,YAAI,MAAK,KAAK,EAAE,iBAAiB,MAAK,KAAK,EAAE,cAAc;AACzD,gBAAK,oBAAoB,GAAG;QAC7B;AAED,YAAI,MAAK,KAAK,EAAE,iBAAiB,MAAK,KAAK,EAAE,cAAc;AACzD,gBAAK,oBAAoB,GAAG;QAC7B;AAED,cAAK,SAAS;AACd,cAAK,SAAS;MAChB;AAUA,WAAA,kBAAkB,WAAA;AAEhB,cAAK,iBAAiB,MAAK,kBAAiB;AAE5C,cAAK,oBAAmB;MAC1B;AAEA,WAAc,iBAAG,SAAC,GAAM;AACtB,YACE,CAAC,MAAK,KAAK,EAAE,MAAM,MACnB,CAAC,MAAK,KAAK,EAAE,MAAM,MACnB,CAAC,MAAK,KAAK,EAAE,UAAU,MACvB,CAAC,MAAK,KAAK,EAAE,UAAU;AAEvB;AAEF,YAAI,sBAAsB;AAE1B,cAAK,KAAK,EAAE,MAAM,OAAO,MAAK,KAAK,EAAE,MAAM,GAAG,sBAAqB;AACnE,cAAK,KAAK,EAAE,MAAM,OAAO,MAAK,KAAK,EAAE,MAAM,GAAG,sBAAqB;AAEnE,YAAI,MAAK,KAAK,EAAE,iBAAiB,MAAK,KAAK,EAAE,cAAc;AACzD,iCAAuB,MAAK,eAAe,MAAK,KAAK,EAAE,MAAM,IAAI;QAClE;AAED,YAAI,MAAK,KAAK,EAAE,iBAAiB,MAAK,KAAK,EAAE,cAAc;AACzD,iCAAuB,MAAK,eAAe,MAAK,KAAK,EAAE,MAAM,IAAI;QAClE;AAGD,YAAI,wBAAwB,sBAAsB;AAEhD,YAAE,gBAAe;AAEjB,cAAI,EAAE,SAAS,iBAAiB,EAAE,gBAAgB,SAAS;AACzD,gBAAI,sBAAsB;AACxB,oBAAK,KAAK,EAAE,UAAU,OACpB,MAAK,KAAK,EAAE,UAAU,GAAG,sBAAqB;AAEhD,kBAAI,MAAK,eAAe,MAAK,KAAK,EAAE,UAAU,IAAI,GAAG;AACnD,sBAAK,YAAY,GAAG,GAAG;cACxB,OAAM;AACL,sBAAK,aAAa,GAAG,GAAG;cACzB;YACF;AAED,gBAAI,sBAAsB;AACxB,oBAAK,KAAK,EAAE,UAAU,OACpB,MAAK,KAAK,EAAE,UAAU,GAAG,sBAAqB;AAEhD,kBAAI,MAAK,eAAe,MAAK,KAAK,EAAE,UAAU,IAAI,GAAG;AACnD,sBAAK,YAAY,GAAG,GAAG;cACxB,OAAM;AACL,sBAAK,aAAa,GAAG,GAAG;cACzB;YACF;UACF;QACF;MACH;AAiCA,WAAI,OAAG,SAAC,GAAM;;AACZ,YAAI,CAAC,MAAK,eAAe,CAAC,MAAK;AAAkB;AAEjD,YAAI;AACJ,YAAM,QAAQ,MAAK,KAAK,MAAK,WAAW,EAAE;AAC1C,YAAM,aAAY,MAAA,KAAA,MAAM,UAAI,QAAA,OAAA,SAAA,SAAA,GAAG,MAAK,KAAK,MAAK,WAAW,EAAE,QAAQ,OAAK,QAAA,OAAA,SAAA,KAAA;AACxE,YAAM,YAAY,MAAK,KAAK,MAAK,WAAW,EAAE;AAC9C,YAAM,eACJ,MAAA,KAAA,MAAK,sBAAgB,QAAA,OAAA,SAAA,SAAA,GAAG,MAAK,KAAK,MAAK,WAAW,EAAE,cAAc,OAAK,QAAA,OAAA,SAAA,KAAA;AACzE,YAAM,WAAW,UACf,MAAA,KAAA,MAAK,cAAW,QAAA,OAAA,SAAA,SAAA,GAAA,MAAK,KAAK,MAAK,WAAW,EAAE,QAAQ,OAAC,QAAA,OAAA,SAAA,KAAI,OACzD,EAAE;AAGJ,UAAE,eAAc;AAChB,UAAE,gBAAe;AAEjB,YAAI,MAAK,gBAAgB,KAAK;AAC5B,wBAAc,EAAE;QACjB,OAAM;AACL,wBAAc,EAAE;QACjB;AAGD,YAAI,UACF,gBACC,MAAA,KAAA,MAAM,UAAI,QAAA,OAAA,SAAA,SAAA,GAAG,MAAK,KAAK,MAAK,WAAW,EAAE,UAAU,OAAC,QAAA,OAAA,SAAA,KAAI,KACzD,MAAK,KAAK,MAAK,WAAW,EAAE;AAC9B,kBACE,MAAK,gBAAgB,OAAO,MAAK,UAC5B,MAAA,KAAA,MAAM,UAAI,QAAA,OAAA,SAAA,SAAA,GAAG,MAAK,KAAK,MAAK,WAAW,EAAE,QAAQ,OAAK,QAAA,OAAA,SAAA,KAAA,KACvD,UAAU,OACV,UACA;AAEN,YAAM,WAAW,WAAW,YAAY,UAAU;AAGlD,YAAI,YAAY,YAAY,cAAc;AAG1C,YAAI,MAAK,gBAAgB,OAAO,MAAK,OAAO;AAC1C,wBAAY,KAAAA,eAAc,cAAe,OAAA,QAAA,OAAA,SAAA,SAAA,GAAE,yBACvC,CAAC,YACD;QACL;AAED,cAAK,iBAAiB,MAAK,KAAK,MAAK,WAAW,EAAE,gBAAgB,IAChE;MACJ;AAKA,WAAS,YAAG,SAAC,GAAM;AACjB,cAAK,aAAa;AAClB,YAAM,aAAa,mBAAmB,MAAK,EAAE;AAC7C,YAAM,WAAW,iBAAiB,MAAK,EAAE;AACzC,UAAE,eAAc;AAChB,UAAE,gBAAe;AAEjB,sBAAc,MAAK,IAAI,MAAK,WAAW,QAAQ;AAC/C,cAAK,gBAAe;AAEpB,mBAAW,oBAAoB,aAAa,MAAK,MAAM,IAAI;AAC3D,mBAAW,oBAAoB,WAAW,MAAK,WAAW,IAAI;AAC9D,cAAK,uBAAuB,SAAS,WAAW,WAAA;AAG9C,qBAAW,oBAAoB,SAAS,MAAK,cAAc,IAAI;AAC/D,qBAAW,oBAAoB,YAAY,MAAK,cAAc,IAAI;AAClE,gBAAK,uBAAuB;QAC9B,CAAC;MACH;AAKA,WAAY,eAAG,SAAC,GAAM;AACpB,UAAE,eAAc;AAChB,UAAE,gBAAe;MACnB;AA1wBE,WAAK,KAAK;AACV,WAAK,UAAe,SAAA,SAAA,CAAA,GAAAA,eAAc,cAAc,GAAK,OAAO;AAC5D,WAAK,aAAa,SAAA,SAAA,CAAA,GACbA,eAAc,eAAe,UAAU,GACvC,QAAQ,UAAU;AAEvB,WAAK,OAAO;QACV,GAAG;UACD,kBAAkB;UAClB,UAAU;UACV,gBAAgB;UAChB,gBAAgB;UAChB,YAAY;UACZ,cAAc;UACd,YAAY;UACZ,eAAe;UACf,cAAc;UACd,OAAO,EAAE,MAAM,MAAM,IAAI,MAAM,MAAM,MAAM,WAAW,MAAO;UAC7D,WAAW,EAAE,MAAM,MAAM,IAAI,MAAM,MAAM,MAAM,WAAW,MAAO;QAClE;QACD,GAAG;UACD,kBAAkB;UAClB,UAAU;UACV,gBAAgB;UAChB,gBAAgB;UAChB,YAAY;UACZ,cAAc;UACd,YAAY;UACZ,eAAe;UACf,cAAc;UACd,OAAO,EAAE,MAAM,MAAM,IAAI,MAAM,MAAM,MAAM,WAAW,MAAO;UAC7D,WAAW,EAAE,MAAM,MAAM,IAAI,MAAM,MAAM,MAAM,WAAW,MAAO;QAClE;;AAGH,UAAI,OAAO,KAAK,OAAO,YAAY,CAAC,KAAK,GAAG,UAAU;AACpD,cAAM,IAAI,MACR,mEAAA,OAAmE,KAAK,EAAE,CAAE;MAE/E;AAED,WAAK,cAAc,iBAAS,KAAK,cAAc,EAAE;AACjD,WAAK,iBAAiB,iBAAS,KAAK,iBAAiB,IAAI,EAAE,SAAS,KAAI,CAAE;AAC1E,WAAK,kBAAkB,iBACrB,KAAK,kBACL,KAAK,eAAe;AAEtB,WAAK,iBAAiB,iBAAS,KAAK,iBAAiB,KAAK,eAAe;AAEzE,WAAK,KAAI;IACV;AAeM,IAAAA,eAAA,gBAAP,WAAA;AACE,UAAIA,eAAc,YAAY;AAC5B,eAAOA,eAAc;MACtB;AAED,UAAM,WAAW,SAAS,cAAc,KAAK;AAC7C,eAAS,YACP;AAEF,UAAM,mBAAmB,SAAS;AAClC,UAAM,aAAa,qBAAgB,QAAhB,qBAAA,SAAA,SAAA,iBAAkB;AAErC,UAAI,CAAC;AAAY,eAAO;AAExB,eAAS,KAAK,YAAY,gBAAgB;AAE1C,uBAAiB,aAAa;AAE9B,UAAM,uBAAuBA,eAAc,UAAU,gBAAgB;AACrE,UAAM,mBAAmBA,eAAc,UAAU,UAAU;AAE3D,uBAAiB,aAAa;AAC9B,UAAM,8BAA8BA,eAAc,UAAU,UAAU;AAEtE,eAAS,KAAK,YAAY,gBAAgB;AAE1C,MAAAA,eAAc,aAAa;;QAEzB,sBAAsB,qBAAqB,SAAS,iBAAiB;;QAErE,uBACE,iBAAiB,SAAS,4BAA4B;;AAG1D,aAAOA,eAAc;;AAGvB,IAAAA,eAAA,UAAA,oBAAA,WAAA;AAEE,UAAI;AAEF,YACG,KAAK,oBACJ,iBAAiB,KAAK,kBAAkB,qBAAqB,EAC1D,YAAY,UACjB,oBAAoB,SAAS,gBAAgB,SAC7C,wBAAwB,SAAS,gBAAgB,OACjD;AACA,iBAAO;QACR,OAAM;AACL,iBAAO,eAAc;QACtB;MACF,SAAQ,GAAG;AACV,eAAO,eAAc;MACtB;;AAGI,IAAAA,eAAS,YAAhB,SAAiB,IAAW;AAC1B,UAAM,OAAO,GAAG,sBAAqB;AACrC,UAAM,aAAa,mBAAmB,EAAE;AACxC,UAAM,WAAW,iBAAiB,EAAE;AAEpC,aAAO;QACL,KACE,KAAK,OACJ,SAAS,eAAe,WAAW,gBAAgB;QACtD,MACE,KAAK,QACJ,SAAS,eAAe,WAAW,gBAAgB;;;AAI1D,IAAAA,eAAA,UAAA,OAAA,WAAA;AAEE,UAAIC,WAAmB;AACrB,aAAK,QAAO;AAEZ,aAAK,aAAaD,eAAc,cAAa;AAC7C,aAAK,iBAAiB,KAAK,kBAAiB;AAE5C,aAAK,YAAW;AAEhB,aAAK,cAAa;MACnB;;AAGH,IAAAA,eAAA,UAAA,UAAA,WAAA;;AAEE,WAAK,YAAY,KAAK,GAAG,cACvB,kBAAkB,KAAK,WAAW,OAAO,CAAC;AAE5C,WAAK,mBACH,KAAK,QAAQ,kBACb,KAAK,GAAG,cAAc,kBAAkB,KAAK,WAAW,cAAc,CAAC;AACzE,WAAK,YACH,KAAK,QAAQ,eACb,KAAK,GAAG,cAAc,kBAAkB,KAAK,WAAW,SAAS,CAAC;AAEpE,WAAK,WAAW,KAAK,GAAG,cACtB,kBAAkB,KAAK,WAAW,MAAM,CAAC;AAE3C,WAAK,SAAS,KAAK,GAAG,cACpB,kBAAkB,KAAK,WAAW,IAAI,CAAC;AAGzC,WAAK,gBAAgB,KAAK,UACxB,KAAK,WACL,kBAAkB,KAAK,WAAW,WAAW,CAAC;AAEhD,WAAK,8BAA8B,KAAK,GAAG,cACzC,kBAAkB,KAAK,WAAW,2BAA2B,CAAC;AAEhE,WAAK,uBAAuB,KAAK,GAAG,cAClC,kBAAkB,KAAK,WAAW,oBAAoB,CAAC;AAEzD,WAAK,KAAK,EAAE,MAAM,KAAK,KAAK,UAC1B,KAAK,IACL,GAAA,OAAG,kBAAkB,KAAK,WAAW,KAAK,CAAC,EAAG,OAAA,kBAC5C,KAAK,WAAW,UAAU,CAC3B,CAAE;AAEL,WAAK,KAAK,EAAE,MAAM,KAAK,KAAK,UAC1B,KAAK,IACL,GAAA,OAAG,kBAAkB,KAAK,WAAW,KAAK,CAAC,EAAG,OAAA,kBAC5C,KAAK,WAAW,QAAQ,CACzB,CAAE;AAGL,WAAK,KAAK,EAAE,UAAU,OACpB,KAAA,KAAK,KAAK,EAAE,MAAM,QAAI,QAAA,OAAA,SAAA,SAAA,GAAA,cACpB,kBAAkB,KAAK,WAAW,SAAS,CAAC,MACzC;AACP,WAAK,KAAK,EAAE,UAAU,OACpB,KAAA,KAAK,KAAK,EAAE,MAAM,QAAI,QAAA,OAAA,SAAA,SAAA,GAAA,cACpB,kBAAkB,KAAK,WAAW,SAAS,CAAC,MACzC;AAEP,UAAI,CAAC,KAAK,QAAQ,UAAU;AAC1B,mBAAW,KAAK,KAAK,EAAE,UAAU,IAAI,KAAK,WAAW,OAAO;AAC5D,mBAAW,KAAK,KAAK,EAAE,UAAU,IAAI,KAAK,WAAW,OAAO;MAC7D;;AAGH,IAAAA,eAAA,UAAA,gBAAA,WAAA;AAAA,UAkDC,QAAA;;AAjDC,UAAM,WAAW,iBAAiB,KAAK,EAAE;AAGzC,WAAK,GAAG,iBAAiB,cAAc,KAAK,YAAY;AAExD,WAAK,GAAG,iBAAiB,eAAe,KAAK,gBAAgB,IAAI;AAEjE,WAAK,GAAG,iBAAiB,aAAa,KAAK,WAAW;AACtD,WAAK,GAAG,iBAAiB,cAAc,KAAK,YAAY;AAExD,OAAA,KAAA,KAAK,sBAAgB,QAAA,OAAA,SAAA,SAAA,GAAE,iBAAiB,UAAU,KAAK,QAAQ;AAG/D,eAAS,iBAAiB,UAAU,KAAK,cAAc;AAEvD,UAAI,CAAC,KAAK;AAAW;AAErB,UAAI,OAAO,gBAAgB;AAEzB,YAAI,0BAAwB;AAC5B,YAAM,iBAAiB,SAAS,kBAAkB;AAClD,aAAK,iBAAiB,IAAI,eAAe,WAAA;AACvC,cAAI,CAAC;AAAuB;AAE5B,mBAAS,sBAAsB,WAAA;AAC7B,kBAAK,YAAW;UAClB,CAAC;QACH,CAAC;AAED,aAAK,eAAe,QAAQ,KAAK,EAAE;AACnC,aAAK,eAAe,QAAQ,KAAK,SAAS;AAE1C,iBAAS,sBAAsB,WAAA;AAC7B,oCAAwB;QAC1B,CAAC;MACF;AAGD,WAAK,mBAAmB,IAAI,SAAS,iBAAiB,WAAA;AACpD,iBAAS,sBAAsB,WAAA;AAC7B,gBAAK,YAAW;QAClB,CAAC;MACH,CAAC;AAED,WAAK,iBAAiB,QAAQ,KAAK,WAAW;QAC5C,WAAW;QACX,SAAS;QACT,eAAe;MAChB,CAAA;;AAGH,IAAAA,eAAA,UAAA,cAAA,WAAA;AACE,UACE,CAAC,KAAK,wBACN,CAAC,KAAK,aACN,CAAC,KAAK,oBACN,CAAC,KAAK,aACN,CAAC,KAAK;AAEN;AAEF,UAAM,WAAW,iBAAiB,KAAK,EAAE;AACzC,WAAK,WAAW,SAAS,iBAAiB,KAAK,EAAE;AACjD,WAAK,QAAQ,KAAK,SAAS,cAAc;AAEzC,UAAM,uBAAuB,KAAK,UAAU;AAE5C,UAAM,eAAe,KAAK,qBAAqB,gBAAgB;AAC/D,UAAM,cACJ,KAAK,qBAAqB,eAAe,KAAK,uBAAuB;AAEvE,UAAM,8BAA8B,KAAK,iBAAiB;AAE1D,UAAM,cAAc,KAAK,SAAS;AAClC,UAAM,cAAc,KAAK,SAAS;AAElC,WAAK,UAAU,MAAM,UAAU,GAAG,OAAA,KAAK,SAAS,YAAU,GAAA,EAAA,OAAI,KAAK,SAAS,cAAY,GAAA,EAAA,OAAI,KAAK,SAAS,eAAiB,GAAA,EAAA,OAAA,KAAK,SAAS,WAAW;AACpJ,WAAK,UAAU,MAAM,SAAS,IAAI,OAAA,KAAK,SAAS,YAAU,IAAA,EAAA,OAAK,KAAK,SAAS,cAAY,IAAA,EAAA,OAAK,KAAK,SAAS,eAAkB,IAAA,EAAA,OAAA,KAAK,SAAS,WAAW;AAEvJ,UAAM,wBAAwB,KAAK,UAAU;AAC7C,UAAM,uBAAuB,KAAK,UAAU;AAE5C,WAAK,iBAAiB,MAAM,SAAS,eAAe,SAAS;AAG7D,WAAK,cAAc,MAAM,QAAQ,cAC7B,GAAA,OAAG,wBAAwB,sBAAwB,IAAA,IACnD;AACJ,WAAK,cAAc,MAAM,SAAS,GAAA,OAAG,uBAAqB,IAAA;AAE1D,UAAM,+BAA+B,KAAK,iBAAiB;AAE3D,WAAK,KAAK,EAAE,gBACV,yBAAyB,KAAK,uBAAuB;AACvD,WAAK,KAAK,EAAE,gBACV,wBAAwB;AAG1B,WAAK,KAAK,EAAE,gBACV,gBAAgB,WAAW,QAAQ,KAAK,KAAK,EAAE;AACjD,WAAK,KAAK,EAAE,gBACV,gBAAgB,WAAW,QAAQ,KAAK,KAAK,EAAE;AAEjD,WAAK,KAAK,EAAE,eACV,KAAK,QAAQ,iBAAiB,OAAO,KAAK,QAAQ,iBAAiB;AACrE,WAAK,KAAK,EAAE,eACV,KAAK,QAAQ,iBAAiB,OAAO,KAAK,QAAQ,iBAAiB;AACrE,WAAK,oBAAmB;AAGxB,UAAM,sBAAsB,KAAK,KAAK,EAAE,gBACpC,KAAK,iBACL;AACJ,UAAM,sBAAsB,KAAK,KAAK,EAAE,gBACpC,KAAK,iBACL;AAEJ,WAAK,KAAK,EAAE,gBACV,KAAK,KAAK,EAAE,iBACZ,uBAAuB,8BAA8B;AACvD,WAAK,KAAK,EAAE,gBACV,KAAK,KAAK,EAAE,iBACZ,wBACE,+BAA+B;AAEnC,WAAK,KAAK,EAAE,UAAU,OAAO,KAAK,iBAAiB,GAAG;AACtD,WAAK,KAAK,EAAE,UAAU,OAAO,KAAK,iBAAiB,GAAG;AAEtD,UAAI,KAAK,KAAK,EAAE,UAAU;AACxB,aAAK,KAAK,EAAE,UAAU,GAAG,MAAM,QAAQ,GAAG,OAAA,KAAK,KAAK,EAAE,UAAU,MAAI,IAAA;AACtE,UAAI,KAAK,KAAK,EAAE,UAAU;AACxB,aAAK,KAAK,EAAE,UAAU,GAAG,MAAM,SAAS,GAAG,OAAA,KAAK,KAAK,EAAE,UAAU,MAAI,IAAA;AAEvE,WAAK,kBAAkB,GAAG;AAC1B,WAAK,kBAAkB,GAAG;AAE1B,WAAK,sBAAsB,GAAG;AAC9B,WAAK,sBAAsB,GAAG;;AAMhC,IAAAA,eAAgB,UAAA,mBAAhB,SAAiB,MAAgB;;AAAhB,UAAA,SAAA,QAAA;AAAA,eAAgB;MAAA;AAC/B,UAAI,CAAC,KAAK,KAAK,IAAI,EAAE,iBAAiB,CAAC,KAAK,WAAW;AACrD,eAAO;MACR;AAED,UAAM,cAAc,KAAK,UAAU,KAAK,KAAK,IAAI,EAAE,cAAc;AACjE,UAAM,aACJ,MAAA,KAAA,KAAK,KAAK,IAAI,EAAE,MAAM,QAAK,QAAA,OAAA,SAAA,SAAA,GAAA,KAAK,KAAK,IAAI,EAAE,cAAc,OAAK,QAAA,OAAA,SAAA,KAAA;AAChE,UAAM,iBAAiB,YAAY;AAEnC,UAAI;AAGJ,sBAAgB,KAAK,IACnB,CAAC,EAAE,iBAAiB,YACpB,KAAK,QAAQ,gBAAgB;AAG/B,UAAI,KAAK,QAAQ,kBAAkB;AACjC,wBAAgB,KAAK,IAAI,eAAe,KAAK,QAAQ,gBAAgB;MACtE;AAED,aAAO;;AAGT,IAAAA,eAAiB,UAAA,oBAAjB,SAAkB,MAAgB;;AAAhB,UAAA,SAAA,QAAA;AAAA,eAAgB;MAAA;AAChC,UAAM,YAAY,KAAK,KAAK,IAAI,EAAE;AAElC,UACE,CAAC,KAAK,KAAK,IAAI,EAAE,iBACjB,CAAC,KAAK,oBACN,CAAC,UAAU,MACX,CAAC,KAAK,UACN;AACA;MACD;AAED,UAAM,cAAc,KAAK,iBAAiB,KAAK,KAAK,IAAI,EAAE,cAAc;AACxE,UAAM,cACJ,KAAA,KAAK,KAAK,IAAI,EAAE,MAAM,QAAK,QAAA,OAAA,SAAA,SAAA,GAAA,KAAK,KAAK,IAAI,EAAE,cAAc,MAAK;AAChE,UAAM,WAAW,SAAS,KAAK,SAAS,KAAK,KAAK,IAAI,EAAE,QAAQ,GAAG,EAAE;AAErE,UAAI,eAAe,KAAK,iBAAiB,KAAK,KAAK,IAAI,EAAE,gBAAgB;AAEzE,qBACE,SAAS,OACT,KAAK,WACL,KAAAA,eAAc,cAAa,OAAE,QAAA,OAAA,SAAA,SAAA,GAAE,wBAC3B,CAAC,eACD;AAEN,UAAI,SAAS,OAAO,KAAK,OAAO;AAC9B,yBAAe,KAAAA,eAAc,cAAe,OAAA,QAAA,OAAA,SAAA,SAAA,GAAE,yBAC1C,eACA,CAAC;MACN;AAED,UAAM,iBAAiB,gBAAgB,cAAc;AAErD,UAAI,eAAe,CAAC,GAAG,YAAY,UAAU,QAAQ;AACrD,qBACE,SAAS,OAAO,KAAK,QACjB,CAAC,gBAAgB,YAAY,UAAU,QACvC;AAEN,gBAAU,GAAG,MAAM,YACjB,SAAS,MACL,eAAe,OAAA,cAAuB,WAAA,IACtC,kBAAA,OAAkB,cAAY,QAAA;;AAGtC,IAAAA,eAAqB,UAAA,wBAArB,SAAsB,MAAgB;AAAhB,UAAA,SAAA,QAAA;AAAA,eAAgB;MAAA;AACpC,UAAM,QAAQ,KAAK,KAAK,IAAI,EAAE,MAAM;AACpC,UAAM,YAAY,KAAK,KAAK,IAAI,EAAE,UAAU;AAE5C,UAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK;AAAkB;AACpD,UAAI,KAAK,KAAK,IAAI,EAAE,iBAAiB,KAAK,KAAK,IAAI,EAAE,cAAc;AACjE,cAAM,MAAM,aAAa;AACzB,aAAK,iBAAiB,MAAM,KAAK,KAAK,IAAI,EAAE,YAAY,IAAI;AAC5D,aAAK,GAAG,UAAU,IAAI,GAAA,OAAG,KAAK,WAAW,YAAU,GAAA,EAAA,OAAI,IAAI,CAAE;MAC9D,OAAM;AACL,cAAM,MAAM,aAAa;AACzB,aAAK,iBAAiB,MAAM,KAAK,KAAK,IAAI,EAAE,YAAY,IAAI;AAC5D,aAAK,GAAG,UAAU,OAAO,GAAA,OAAG,KAAK,WAAW,YAAU,GAAA,EAAA,OAAI,IAAI,CAAE;MACjE;AAGD,UAAI,KAAK,KAAK,IAAI,EAAE,eAAe;AACjC,kBAAU,MAAM,UAAU;MAC3B,OAAM;AACL,kBAAU,MAAM,UAAU;MAC3B;;AAGH,IAAAA,eAAa,UAAA,gBAAb,SAAc,MAAgB;AAAhB,UAAA,SAAA,QAAA;AAAA,eAAgB;MAAA;AAC5B,UAAI,KAAK,KAAK,IAAI,EAAE,iBAAiB,CAAC,KAAK,KAAK,IAAI,EAAE,UAAU,WAAW;AACzE,mBAAW,KAAK,KAAK,IAAI,EAAE,UAAU,IAAI,KAAK,WAAW,OAAO;AAChE,aAAK,KAAK,IAAI,EAAE,UAAU,YAAY;MACvC;;AAGH,IAAAA,eAAa,UAAA,gBAAb,SAAc,MAAgB;AAAhB,UAAA,SAAA,QAAA;AAAA,eAAgB;MAAA;AAC5B,UAAI,KAAK;AAAY;AACrB,UAAI,KAAK,KAAK,IAAI,EAAE,iBAAiB,KAAK,KAAK,IAAI,EAAE,UAAU,WAAW;AACxE,sBAAc,KAAK,KAAK,IAAI,EAAE,UAAU,IAAI,KAAK,WAAW,OAAO;AACnE,aAAK,KAAK,IAAI,EAAE,UAAU,YAAY;MACvC;;AAGH,IAAAA,eAAA,UAAA,sBAAA,WAAA;AACE,UAAI,CAAC,KAAK;AAAU;AAEpB,WAAK,SAAS,MAAM,KAAK,QAAQ,SAAS,OAAO,IAC/C,KAAK,KAAK,EAAE,iBAAiB,KAAK,KAAK,EAAE,eACrC,IAAA,OAAI,KAAK,gBAAkB,IAAA,IAC3B;AACN,WAAK,SAAS,MAAM,SAClB,KAAK,KAAK,EAAE,iBAAiB,KAAK,KAAK,EAAE,eACrC,IAAA,OAAI,KAAK,gBAAkB,IAAA,IAC3B;;AAuFR,IAAAA,eAAkB,UAAA,qBAAlB,SAAmB,MAAgB;AAAhB,UAAA,SAAA,QAAA;AAAA,eAAgB;MAAA;AACjC,UAAM,cAAc,KAAK,KAAK,IAAI;AAClC,UAAI,CAAC,YAAY,MAAM,MAAM,CAAC,YAAY,UAAU;AAAI;AAExD,kBAAY,MAAM,OAAO,YAAY,MAAM,GAAG,sBAAqB;AACnE,kBAAY,UAAU,OACpB,YAAY,UAAU,GAAG,sBAAqB;AAEhD,UAAI,KAAK,eAAe,YAAY,MAAM,IAAI,GAAG;AAC/C,aAAK,cAAc,IAAI;AACvB,mBAAW,YAAY,MAAM,IAAI,KAAK,WAAW,KAAK;AAEtD,YAAI,KAAK,eAAe,YAAY,UAAU,IAAI,GAAG;AACnD,qBAAW,YAAY,UAAU,IAAI,KAAK,WAAW,KAAK;QAC3D,OAAM;AACL,wBAAc,YAAY,UAAU,IAAI,KAAK,WAAW,KAAK;QAC9D;MACF,OAAM;AACL,sBAAc,YAAY,MAAM,IAAI,KAAK,WAAW,KAAK;AACzD,YAAI,KAAK,QAAQ,UAAU;AACzB,eAAK,cAAc,IAAI;QACxB;MACF;;AAkBH,IAAAA,eAAmB,UAAA,sBAAnB,SAAoB,MAAgB;AAAhB,UAAA,SAAA,QAAA;AAAA,eAAgB;MAAA;AAClC,oBAAc,KAAK,KAAK,IAAI,EAAE,MAAM,IAAI,KAAK,WAAW,KAAK;AAC7D,oBAAc,KAAK,KAAK,IAAI,EAAE,UAAU,IAAI,KAAK,WAAW,KAAK;AACjE,UAAI,KAAK,QAAQ,UAAU;AACzB,aAAK,cAAc,IAAI;MACxB;;AAkEH,IAAAA,eAAA,UAAA,cAAA,SAAY,GAAQ,MAAgB;;AAAhB,UAAA,SAAA,QAAA;AAAA,eAAgB;MAAA;AAClC,WAAK,aAAa;AAClB,UAAM,aAAa,mBAAmB,KAAK,EAAE;AAC7C,UAAM,WAAW,iBAAiB,KAAK,EAAE;AACzC,UAAM,YAAY,KAAK,KAAK,IAAI,EAAE;AAGlC,UAAM,cAAc,SAAS,MAAM,EAAE,QAAQ,EAAE;AAC/C,WAAK,KAAK,IAAI,EAAE,aACd,iBAAe,KAAA,UAAU,UAAI,QAAA,OAAA,SAAA,SAAA,GAAG,KAAK,KAAK,IAAI,EAAE,UAAU,MAAK;AACjE,WAAK,cAAc;AAEnB,iBAAW,KAAK,IAAI,KAAK,WAAW,QAAQ;AAE5C,iBAAW,iBAAiB,aAAa,KAAK,MAAM,IAAI;AACxD,iBAAW,iBAAiB,WAAW,KAAK,WAAW,IAAI;AAC3D,UAAI,KAAK,yBAAyB,MAAM;AACtC,mBAAW,iBAAiB,SAAS,KAAK,cAAc,IAAI;AAC5D,mBAAW,iBAAiB,YAAY,KAAK,cAAc,IAAI;MAChE,OAAM;AACL,iBAAS,aAAa,KAAK,oBAAoB;AAC/C,aAAK,uBAAuB;MAC7B;;AAyFH,IAAAA,eAAA,UAAA,eAAA,SAAa,GAAQ,MAAgB;AAArC,UAgDC,QAAA;;AAhDoB,UAAA,SAAA,QAAA;AAAA,eAAgB;MAAA;AACnC,UAAM,cAAc,KAAK,KAAK,IAAI;AAClC,UACE,CAAC,KAAK,QAAQ,gBACd,CAAC,YAAY,UAAU,MACvB,CAAC,KAAK;AAEN;AAGF,QAAE,eAAc;AAEhB,UAAM,WAAW,iBAAiB,KAAK,EAAE;AACzC,WAAK,KAAK,IAAI,EAAE,UAAU,OACxB,YAAY,UAAU,GAAG,sBAAqB;AAChD,UAAM,YAAY,KAAK,KAAK,IAAI,EAAE;AAClC,UAAM,mBAAkB,MAAA,KAAA,UAAU,UAAO,QAAA,OAAA,SAAA,SAAA,GAAA,KAAK,KAAK,IAAI,EAAE,UAAU,OAAC,QAAA,OAAA,SAAA,KAAI;AACxE,UAAM,WAAW,UACf,MAAA,KAAA,KAAK,cAAQ,QAAA,OAAA,SAAA,SAAA,GAAG,KAAK,KAAK,IAAI,EAAE,QAAQ,OAAC,QAAA,OAAA,SAAA,KAAI,OAC7C,EAAE;AAEJ,UAAI,WAAW,KAAK,iBAAiB,KAAK,KAAK,IAAI,EAAE,gBAAgB;AACrE,UAAM,IACJ,SAAS,MACL,KAAK,SAAS,kBACd,KAAK,SAAS;AACpB,UAAM,MAAM,IAAI,IAAI,KAAK;AACzB,UAAM,aAAa,QAAQ,KAAK,WAAW,WAAW,WAAW;AACjE,UAAM,QAAQ;AAEd,UAAM,WAAW,WAAA;AACf,YAAI,CAAC,MAAK;AAAkB;AAC5B,YAAI,QAAQ,IAAI;AACd,cAAI,WAAW,YAAY;AACzB,wBAAY;AACZ,kBAAK,iBAAiB,MAAK,KAAK,IAAI,EAAE,gBAAgB,IAAI;AAC1D,qBAAS,sBAAsB,QAAQ;UACxC;QACF,OAAM;AACL,cAAI,WAAW,YAAY;AACzB,wBAAY;AACZ,kBAAK,iBAAiB,MAAK,KAAK,IAAI,EAAE,gBAAgB,IAAI;AAC1D,qBAAS,sBAAsB,QAAQ;UACxC;QACF;MACH;AAEA,eAAQ;;AAMV,IAAAA,eAAA,UAAA,oBAAA,WAAA;AACE,aAAO,KAAK;;AAMd,IAAAA,eAAA,UAAA,mBAAA,WAAA;AACE,aAAO,KAAK;;AAGd,IAAAA,eAAA,UAAA,kBAAA,WAAA;AACE,UAAM,WAAW,iBAAiB,KAAK,EAAE;AAEzC,WAAK,GAAG,oBAAoB,cAAc,KAAK,YAAY;AAE3D,WAAK,GAAG,oBAAoB,eAAe,KAAK,gBAAgB,IAAI;AAEpE,WAAK,GAAG,oBAAoB,aAAa,KAAK,WAAW;AACzD,WAAK,GAAG,oBAAoB,cAAc,KAAK,YAAY;AAE3D,UAAI,KAAK,kBAAkB;AACzB,aAAK,iBAAiB,oBAAoB,UAAU,KAAK,QAAQ;MAClE;AAED,eAAS,oBAAoB,UAAU,KAAK,cAAc;AAE1D,UAAI,KAAK,kBAAkB;AACzB,aAAK,iBAAiB,WAAU;MACjC;AAED,UAAI,KAAK,gBAAgB;AACvB,aAAK,eAAe,WAAU;MAC/B;AAGA,WAAK,YAAmC,OAAM;AAC9C,WAAK,eAAsC,OAAM;AACjD,WAAK,gBAAuC,OAAM;AAClD,WAAK,eAAsC,OAAM;;AAMpD,IAAAA,eAAA,UAAA,UAAA,WAAA;AACE,WAAK,gBAAe;;AAMtB,IAAAA,eAAc,UAAA,iBAAd,SAAe,MAAa;AAC1B,aACE,KAAK,UAAU,KAAK,QACpB,KAAK,UAAU,KAAK,OAAO,KAAK,SAChC,KAAK,UAAU,KAAK,OACpB,KAAK,UAAU,KAAK,MAAM,KAAK;;AAOnC,IAAAA,eAAA,UAAA,YAAA,SAAU,IAAS,OAAU;AAC3B,UAAM,UACJ,GAAG,WACH,GAAG,yBACH,GAAG,sBACH,GAAG;AACL,aAAO,MAAM,UAAU,OAAO,KAAK,GAAG,UAAU,SAAC,OAAK;AACpD,eAAA,QAAQ,KAAK,OAAO,KAAK;MAAzB,CAA0B,EAC1B,CAAC;;AA56BE,IAAAA,eAAU,aAAe;AAEzB,IAAAA,eAAA,iBAA0B;MAC/B,cAAc;MACd,cAAc;MACd,kBAAkB;MAClB,kBAAkB;MAClB,WAAW;MACX,UAAU;MACV,YAAY;QACV,WAAW;QACX,gBAAgB;QAChB,QAAQ;QACR,MAAM;QACN,SAAS;QACT,aAAa;QACb,WAAW;QACX,OAAO;QACP,6BAA6B;QAC7B,sBAAsB;QACtB,SAAS;QACT,YAAY;QACZ,UAAU;QACV,OAAO;QACP,UAAU;QACV,WAAW;QACX,YAAY;QACZ,cAAc;MACf;MACD,gBAAgB;MAChB,aAAa;MACb,UAAU;;AA4DL,IAAAA,eAAU,aAAG;AACb,IAAAA,eAAO,UAAG;AAk1BnB,WAACA;EAAA,GAAA;;;;AnB7gCD,IAAIE,YAAW,WAAW;AACtB,EAAAA,YAAW,OAAO,UAAU,SAASA,UAAS,GAAG;AAC7C,aAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,UAAI,UAAU,CAAC;AACf,eAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAC/E;AACA,WAAO;AAAA,EACX;AACA,SAAOA,UAAS,MAAM,MAAM,SAAS;AACzC;AAEA,SAAS,OAAO,GAAG,GAAG;AAClB,MAAI,IAAI,CAAC;AACT,WAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI;AAC9E,MAAE,CAAC,IAAI,EAAE,CAAC;AACd,MAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B;AACrD,aAAS,IAAI,GAAG,IAAI,OAAO,sBAAsB,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK;AACpE,UAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAG,EAAE,CAAC,CAAC;AACzE,UAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAAA,IACxB;AACJ,SAAO;AACX;AAEA,IAAI,YAAkB,iBAAW,SAAU,IAAI,KAAK;AAChD,MAAI,WAAW,GAAG,UAAU,KAAK,GAAG,qBAAqB,sBAAsB,OAAO,SAAS,CAAC,IAAI,IAAI,aAAa,OAAO,IAAI,CAAC,YAAY,qBAAqB,CAAC;AACnK,MAAI,QAAc,aAAO;AACzB,MAAI,oBAA0B,aAAO;AACrC,MAAI,iBAAuB,aAAO;AAClC,MAAI,UAAU,CAAC;AACf,MAAI,OAAO,CAAC;AACZ,SAAO,KAAK,UAAU,EAAE,QAAQ,SAAU,KAAK;AAC3C,QAAI,OAAO,UAAU,eAAe,KAAK,cAAc,gBAAgB,GAAG,GAAG;AACzE,cAAQ,GAAG,IAAI,WAAW,GAAG;AAAA,IACjC,OACK;AACD,WAAK,GAAG,IAAI,WAAW,GAAG;AAAA,IAC9B;AAAA,EACJ,CAAC;AACD,MAAI,aAAaA,UAASA,UAAS,CAAC,GAAG,cAAc,eAAe,UAAU,GAAG,QAAQ,UAAU;AACnG,MAAI,0BAA0BA,UAASA,UAAS,CAAC,GAAG,mBAAmB,GAAG,EAAE,WAAW,GAAG,OAAO,WAAW,cAAc,EAAE,OAAO,oBAAoB,YAAY,IAAI,OAAO,oBAAoB,SAAS,IAAI,EAAE,GAAG,UAAU,QAAQ,YAAY,cAAc,eAAe,UAAU,MAAM,UAAU,cAAc,QAAQ,aAAa,cAAc,eAAe,UAAU,CAAC;AACpX,EAAM,gBAAU,WAAY;AACxB,QAAI;AACJ,sBAAkB,UAAU,wBAAwB,MAC9C,wBAAwB,IAAI,UAC5B,kBAAkB;AACxB,QAAI,MAAM,SAAS;AACf,iBAAW,IAAI,cAAc,MAAM,SAASA,UAASA,UAASA,UAAS,CAAC,GAAG,OAAO,GAAI,kBAAkB,WAAW;AAAA,QAC/G,gBAAgB,kBAAkB;AAAA,MACtC,CAAE,GAAI,eAAe,WAAW;AAAA,QAC5B,aAAa,eAAe;AAAA,MAChC,CAAE,CAAC;AACH,UAAI,OAAO,QAAQ,YAAY;AAC3B,YAAI,QAAQ;AAAA,MAChB,WACS,KAAK;AACV,YAAI,UAAU;AAAA,MAClB;AAAA,IACJ;AACA,WAAO,WAAY;AACf,mBAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,QAAQ;AACrE,iBAAW;AACX,UAAI,OAAO,QAAQ,YAAY;AAC3B,YAAI,IAAI;AAAA,MACZ;AAAA,IACJ;AAAA,EACJ,GAAG,CAAC,CAAC;AACL,SAAc;AAAA,IAAc;AAAA,IAAOA,UAAS,EAAE,kBAAkB,QAAQ,KAAK,MAAM,GAAG,IAAI;AAAA,IAChF;AAAA,MAAc;AAAA,MAAO,EAAE,WAAW,WAAW,QAAQ;AAAA,MACjD;AAAA,QAAc;AAAA,QAAO,EAAE,WAAW,WAAW,4BAA4B;AAAA,QACrE,oBAAc,OAAO,EAAE,WAAW,WAAW,qBAAqB,CAAC;AAAA,MAAC;AAAA,MACxE;AAAA,QAAc;AAAA,QAAO,EAAE,WAAW,WAAW,KAAK;AAAA,QAC9C,oBAAc,OAAO,EAAE,WAAW,WAAW,OAAO,GAAG,OAAO,aAAa,aAAc,SAAS;AAAA,UACpG;AAAA,UACA,qBAAqBA,UAASA,UAAS,CAAC,GAAG,uBAAuB,GAAG,EAAE,KAAK,kBAAkB,CAAC;AAAA,UAC/F;AAAA,UACA,kBAAkB;AAAA,YACd,WAAW,WAAW;AAAA,YACtB,KAAK;AAAA,UACT;AAAA,QACJ,CAAC,IAAY;AAAA,UAAc;AAAA,UAAOA,UAAS,CAAC,GAAG,uBAAuB;AAAA,UAC5D,oBAAc,OAAO,EAAE,WAAW,WAAW,UAAU,GAAG,QAAQ;AAAA,QAAC,CAAE;AAAA,MAAC;AAAA,MAC9E,oBAAc,OAAO,EAAE,WAAW,WAAW,YAAY,CAAC;AAAA,IAAC;AAAA,IAC/D;AAAA,MAAc;AAAA,MAAO,EAAE,WAAW,GAAG,OAAO,WAAW,OAAO,GAAG,EAAE,OAAO,WAAW,UAAU,EAAE;AAAA,MAC7F,oBAAc,OAAO,EAAE,WAAW,WAAW,UAAU,CAAC;AAAA,IAAC;AAAA,IAC7D;AAAA,MAAc;AAAA,MAAO,EAAE,WAAW,GAAG,OAAO,WAAW,OAAO,GAAG,EAAE,OAAO,WAAW,QAAQ,EAAE;AAAA,MAC3F,oBAAc,OAAO,EAAE,WAAW,WAAW,UAAU,CAAC;AAAA,IAAC;AAAA,EAAC;AAC5E,CAAC;AACD,UAAU,cAAc;", "names": ["objectProto", "nativeObjectToString", "symToStringTag", "FUNC_ERROR_TEXT", "__assign", "getElementWindow", "getElementDocument", "getOptions", "addClasses", "removeClasses", "classNamesToQuery", "helpers.getElementWindow", "helpers.getElementDocument", "helpers.getOptions", "helpers.addClasses", "helpers.removeClasses", "helpers.classNamesToQuery", "SimpleBarCore", "helpers.canUseDOM", "__assign"]}