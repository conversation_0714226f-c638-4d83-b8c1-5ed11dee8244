# API Documentation (Summary)

- Base URL: /api/v1

Auth
- POST /auth/register
- POST /auth/login
- POST /auth/logout
- POST /auth/refresh-token
- POST /auth/request-otp
- POST /auth/verify-otp
- POST /auth/reset-password
- POST /auth/setup-mfa
- POST /auth/verify-mfa

Users
- GET /me
- PATCH /me
- GET /users

Organizations
- GET /organizations
- POST /organizations
- GET /organizations/{id}
- PATCH /organizations/{id}
- DELETE /organizations/{id}
- GET /organizations/{id}/hierarchy
- GET /organizations/{id}/members

Roles & Permissions
- GET/POST/GET/{id}/PATCH/DELETE /roles
- POST/DELETE /roles/{id}/permissions/{permissionId}
- GET/POST/GET/{id}/PATCH/DELETE /permissions

Billing
- GET/POST/GET/{id}/PATCH/DELETE /subscriptions
- POST /subscriptions/{id}/upgrade
- POST /subscriptions/{id}/downgrade
- POST /subscriptions/{id}/cancel
- GET/POST/GET/{id} /payments
- POST /payments/{id}/retry
- POST /payments/{id}/refund
- POST /payments/webhook
- GET/POST/GET/{id} /invoices
- GET /invoices/{id}/pdf
- POST /invoices/{id}/send

Approvals
- GET/POST/GET/{id} /approvals
- POST /approvals/{id}/approve
- POST /approvals/{id}/reject
- POST /approvals/{id}/comments

Notifications
- GET /notifications
- GET /notifications/{id}
- PATCH /notifications/{id}/read

Reports
- GET /reports/subscriptions
- GET /reports/payments
- GET /reports/revenue
- GET /reports/users
- GET /reports/approvals

Settings
- GET /settings
- PATCH /settings
- GET /settings/{key}
- PATCH /settings/{key}
