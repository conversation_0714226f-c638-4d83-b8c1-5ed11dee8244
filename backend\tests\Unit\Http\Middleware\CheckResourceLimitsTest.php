<?php

namespace Tests\Unit\Http\Middleware;

use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;
use Illuminate\Foundation\Testing\DatabaseMigrations;
use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;
use App\Http\Middleware\CheckResourceLimits;
use App\Modules\Organizations\Domain\Models\Organization;
use App\Modules\Users\Domain\Models\User;
use App\Modules\Billing\Domain\Models\Plan;
use App\Modules\Billing\Domain\Models\Subscription;
use App\Modules\Billing\Application\Services\ResourceLimitService;

class CheckResourceLimitsTest extends TestCase
{
    use DatabaseMigrations;

    protected CheckResourceLimits $middleware;
    protected Organization $tenant;
    protected User $user;
    protected Plan $plan;
    protected Subscription $subscription;

    protected function setUp(): void
    {
        parent::setUp();

        $this->middleware = new CheckResourceLimits(app(ResourceLimitService::class));

        $portalOwner = Organization::factory()->create([
            'type' => 'portal_owner',
            'parent_id' => null,
        ]);

        $this->tenant = Organization::factory()->create([
            'type' => 'tenant',
            'parent_id' => $portalOwner->id,
        ]);

        $this->plan = Plan::factory()->create([
            'user_limit' => 5,
            'sub_org_limit' => 2,
            'storage_limit' => 10, // GB
            'modules' => ['inventory', 'accounting'],
        ]);

        $this->subscription = Subscription::factory()->create([
            'organization_id' => $this->tenant->id,
            'plan_id' => $this->plan->id,
            'status' => 'active',
            'user_count' => 0,
            'sub_org_count' => 0,
            'storage_used' => 0,
        ]);

        $this->user = User::factory()->create([
            'organization_id' => $this->tenant->id,
            'status' => 'active',
        ]);
    }
    #[Test]
    public function allows_user_creation_within_limit()
    {
        $request = Request::create('/api/users', 'POST');
        $this->actingAs($this->user);

        $response = $this->middleware->handle($request, function ($req) {
            return response()->json(['success' => true]);
        }, 'user');

        $this->assertEquals(200, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertTrue($content['success']);
    }
    #[Test]
    public function blocks_user_creation_when_limit_exceeded()
    {
        $this->subscription->update(['user_count' => 5]);
        
        $request = Request::create('/api/users', 'POST');
        $this->actingAs($this->user);

        $response = $this->middleware->handle($request, function ($req) {
            return response()->json(['success' => true]);
        }, 'user');

        $this->assertEquals(403, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertEquals('Resource Limit Exceeded', $content['error']);
        $this->assertTrue($content['requires_approval']);
    }
    #[Test]
    public function allows_sub_org_creation_within_limit()
    {
        $request = Request::create('/api/organizations', 'POST');
        $this->actingAs($this->user);

        $response = $this->middleware->handle($request, function ($req) {
            return response()->json(['success' => true]);
        }, 'sub_org');

        $this->assertEquals(200, $response->getStatusCode());
    }
    #[Test]
    public function blocks_sub_org_creation_when_limit_exceeded()
    {
        $this->subscription->update(['sub_org_count' => 2]);
        
        $request = Request::create('/api/organizations', 'POST');
        $this->actingAs($this->user);

        $response = $this->middleware->handle($request, function ($req) {
            return response()->json(['success' => true]);
        }, 'sub_org');

        $this->assertEquals(403, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('requires_approval', $content);
    }
    #[Test]
    public function allows_storage_upload_within_limit()
    {
        $file = UploadedFile::fake()->create('document.pdf', 1024); // 1 MB
        
        $request = Request::create('/api/upload', 'POST');
        $request->files->set('file', $file);
        $this->actingAs($this->user);

        $response = $this->middleware->handle($request, function ($req) {
            return response()->json(['success' => true]);
        }, 'storage');

        $this->assertEquals(200, $response->getStatusCode());
    }
    #[Test]
    public function blocks_storage_upload_when_limit_exceeded()
    {
        $tenGB = 10 * 1024 * 1024 * 1024;
        $this->subscription->update(['storage_used' => $tenGB]);
        
        $file = UploadedFile::fake()->create('large-file.zip', 102400); // 100 MB
        
        $request = Request::create('/api/upload', 'POST');
        $request->files->set('file', $file);
        $this->actingAs($this->user);

        $response = $this->middleware->handle($request, function ($req) {
            return response()->json(['success' => true]);
        }, 'storage');

        $this->assertEquals(403, $response->getStatusCode());
    }
    #[Test]
    public function allows_module_access_when_included_in_plan()
    {
        $request = Request::create('/api/inventory', 'GET');
        $request->setRouteResolver(function () use ($request) {
            $route = new \Illuminate\Routing\Route('GET', '/api/{module}', []);
            $route->bind($request);
            $route->setParameter('module', 'inventory');
            return $route;
        });
        $this->actingAs($this->user);

        $response = $this->middleware->handle($request, function ($req) {
            return response()->json(['success' => true]);
        }, 'module');

        $this->assertEquals(200, $response->getStatusCode());
    }
    #[Test]
    public function blocks_module_access_when_not_included_in_plan()
    {
        $request = Request::create('/api/hr', 'GET');
        $request->setRouteResolver(function () use ($request) {
            $route = new \Illuminate\Routing\Route('GET', '/api/{module}', []);
            $route->bind($request);
            $route->setParameter('module', 'hr');
            return $route;
        });
        $this->actingAs($this->user);

        $response = $this->middleware->handle($request, function ($req) {
            return response()->json(['success' => true]);
        }, 'module');

        $this->assertEquals(403, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertStringContainsString('not included', $content['message']);
    }
    #[Test]
    public function returns_401_when_user_not_authenticated()
    {
        $request = Request::create('/api/users', 'POST');
        // Don't authenticate

        $response = $this->middleware->handle($request, function ($req) {
            return response()->json(['success' => true]);
        }, 'user');

        $this->assertEquals(401, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertEquals('Unauthorized', $content['error']);
    }
    #[Test]
    public function allows_request_for_unknown_resource_type()
    {
        $request = Request::create('/api/some-endpoint', 'GET');
        $this->actingAs($this->user);

        $response = $this->middleware->handle($request, function ($req) {
            return response()->json(['success' => true]);
        }, 'unknown_type');

        $this->assertEquals(200, $response->getStatusCode());
    }
    #[Test]
    public function storage_check_passes_when_no_file_uploaded()
    {
        $request = Request::create('/api/data', 'POST');
        $this->actingAs($this->user);

        $response = $this->middleware->handle($request, function ($req) {
            return response()->json(['success' => true]);
        }, 'storage');

        $this->assertEquals(200, $response->getStatusCode());
    }
    #[Test]
    public function module_check_passes_when_no_module_specified()
    {
        $request = Request::create('/api/data', 'GET');
        $this->actingAs($this->user);

        $response = $this->middleware->handle($request, function ($req) {
            return response()->json(['success' => true]);
        }, 'module');

        $this->assertEquals(200, $response->getStatusCode());
    }
}
