import React, { useState, useEffect } from 'react'
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom'
import { CCard, CCardBody, CCardHeader, CCol, CRow, CForm, CFormLabel, CFormInput, CFormSelect, CButton, CSpinner, CAlert } from '@coreui/react'
import { billingAPI } from '../../api/billing'

const InvoiceDetail = () => {
  const { id } = useParams()
  const navigate = useNavigate()
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [formData, setFormData] = useState({ invoice_number: '', amount: '', status: 'draft', due_date: '' })
  const [saving, setSaving] = useState(false)

  useEffect(() => {
    if (id && id !== 'new') {
      loadInvoice()
    } else {
      setLoading(false)
    }
  }, [id])

  const loadInvoice = async () => {
    try {
      setLoading(true)
      const response = await billingAPI.getInvoice(id)
      setFormData(response.data.data)
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to load invoice')
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    try {
      setSaving(true)
      if (id && id !== 'new') {
        await billingAPI.updateInvoice(id, formData)
      } else {
        await billingAPI.createInvoice(formData)
      }
      navigate('/invoices')
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to save invoice')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return <CRow><CCol xs={12} className="text-center"><CSpinner color="primary" /></CCol></CRow>
  }

  return (
    <CRow>
      <CCol xs={12} md={8}>
        <CCard>
          <CCardHeader>
            <strong>{id && id !== 'new' ? 'Edit Invoice' : 'Create Invoice'}</strong>
          </CCardHeader>
          <CCardBody>
            {error && <CAlert color="danger">{error}</CAlert>}
            <CForm onSubmit={handleSubmit}>
              <div className="mb-3">
                <CFormLabel htmlFor="invoice_number">Invoice Number</CFormLabel>
                <CFormInput id="invoice_number" name="invoice_number" value={formData.invoice_number} onChange={handleChange} required />
              </div>
              <div className="mb-3">
                <CFormLabel htmlFor="amount">Amount</CFormLabel>
                <CFormInput id="amount" name="amount" type="number" value={formData.amount} onChange={handleChange} required />
              </div>
              <div className="mb-3">
                <CFormLabel htmlFor="due_date">Due Date</CFormLabel>
                <CFormInput id="due_date" name="due_date" type="date" value={formData.due_date} onChange={handleChange} />
              </div>
              <div className="mb-3">
                <CFormLabel htmlFor="status">Status</CFormLabel>
                <CFormSelect id="status" name="status" value={formData.status} onChange={handleChange}>
                  <option value="draft">Draft</option>
                  <option value="sent">Sent</option>
                  <option value="paid">Paid</option>
                  <option value="overdue">Overdue</option>
                </CFormSelect>
              </div>
              <div className="d-grid gap-2">
                <CButton type="submit" color="primary" disabled={saving}>
                  {saving ? <CSpinner size="sm" className="me-2" /> : null}
                  {id && id !== 'new' ? 'Update Invoice' : 'Create Invoice'}
                </CButton>
                <CButton type="button" color="secondary" onClick={() => navigate('/invoices')}>
                  Cancel
                </CButton>
              </div>
            </CForm>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  )
}

export default InvoiceDetail
