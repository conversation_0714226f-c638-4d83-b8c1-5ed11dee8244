<?php

namespace App\Modules\Shared\Domain\Events;

abstract class DomainEvent
{
    public readonly string $eventId;
    public readonly string $occurredAt;

    public function __construct(
        public readonly ?string $organizationId = null,
        public readonly ?string $userId = null,
        public readonly array $payload = []
    ) {
        $this->eventId = (string) \Illuminate\Support\Str::uuid();
        $this->occurredAt = now()->toIso8601String();
    }

    public function toArray(): array
    {
        return [
            'event_id' => $this->eventId,
            'occurred_at' => $this->occurredAt,
            'organization_id' => $this->organizationId,
            'user_id' => $this->userId,
            'payload' => $this->payload,
            'type' => static::class,
        ];
    }
}
