<?php

namespace App\Modules\Organizations\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Organizations\Domain\Services\OrganizationService;
use App\Modules\Organizations\Http\Resources\OrganizationResource;
use Illuminate\Http\Request;

class OrganizationController extends Controller
{
    public function __construct(private readonly OrganizationService $service) {}

    public function index(Request $request)
    {
        return response()->json(OrganizationResource::collection($this->service->listOrganizations()));
    }

    public function store(Request $request)
    {
        $org = $this->service->createOrganization($request->all());
        return response()->json(new OrganizationResource($org), 201);
    }

    public function show(string $id)
    {
        $org = $this->service->getOrganizationById($id);
        abort_if(!$org, 404);
        return response()->json(new OrganizationResource($org));
    }

    public function update(Request $request, string $id)
    {
        $org = $this->service->getOrganizationById($id);
        abort_if(!$org, 404);
        $org = $this->service->updateOrganization($org, $request->all());
        return response()->json(new OrganizationResource($org));
    }

    public function destroy(string $id)
    {
        $org = $this->service->getOrganizationById($id);
        abort_if(!$org, 404);
        $this->service->deleteOrganization($org);
        return response()->json(['deleted' => true]);
    }

    public function hierarchy(string $id)
    {
        return response()->json($this->service->getHierarchy($id));
    }

    public function members(string $id)
    {
        return response()->json($this->service->getMembers($id));
    }
}
