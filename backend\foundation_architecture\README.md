# Foundation Architecture Documentation

## Documentation Status

### ✅ Completed Documents

1. **00_INDEX.md** - Complete index and navigation guide
2. **01_PROJECT_OVERVIEW.md** - Comprehensive project overview covering:
   - Executive summary and business model
   - Vision, goals, and scope
   - Key stakeholders and their roles
   - Core concepts (hierarchy, limits, workflows)
   - Success metrics and boundaries
   - Technology philosophy
   - Non-functional requirements

3. **02_BUSINESS_RULES.md** - Detailed business rules including:
   - India-specific requirements
   - All 6 resource limit rules with examples
   - Approval workflow rules
   - Subscription and add-on rules
   - Resource usage tracking rules
   - Data isolation requirements
   - Audit and compliance requirements
   - Error handling and notification rules

### 📋 Remaining Documents to Create

#### Part 2: Architecture & Design
- **03_SYSTEM_ARCHITECTURE.md** - Should cover:
  - High-level architecture diagram
  - Modular monolith structure
  - Domain-Driven Design patterns
  - Layered architecture (Presentation, Application, Domain, Infrastructure)
  - Module dependencies and communication
  - Data flow diagrams

- **04_MODULE_STRUCTURE.md** - Should detail:
  - All 13 business modules
  - Each module's purpose and responsibilities
  - Module internal structure (Domain, Application, Infrastructure layers)
  - Inter-module dependencies
  - Module isolation principles

- **05_DATABASE_DESIGN.md** - Should include:
  - Entity Relationship Diagrams
  - Core tables: organizations, users, plans, subscriptions
  - Relationship tables: subscription_addon, approval_steps
  - Logging tables: resource_usage_logs, audit_logs
  - Indexes and performance optimization
  - Data retention policies

#### Part 3: Core Subsystems
- **06_MULTI_TENANCY_SYSTEM.md**
- **07_BILLING_SUBSCRIPTION_SYSTEM.md** (Priority - fully implemented)
- **08_APPROVAL_WORKFLOW_SYSTEM.md** (Priority - fully implemented)
- **09_RESOURCE_LIMIT_MANAGEMENT.md** (Priority - fully implemented)

#### Part 4: Infrastructure & Operations
- **10_API_STRUCTURE.md**
- **11_SCHEDULED_JOBS.md** (Priority - implemented)
- **12_AUTHENTICATION_AUTHORIZATION.md**

#### Part 5: Development Guidelines
- **13_TESTING_STRATEGY.md** (Priority - 97 tests documented)
- **14_DEPLOYMENT_GUIDE.md**
- **15_FUTURE_ENHANCEMENTS.md**

---

## Quick Navigation

### For New Developers
Start here: **01_PROJECT_OVERVIEW.md** → **02_BUSINESS_RULES.md**

### For Business Stakeholders  
Read: **01_PROJECT_OVERVIEW.md** (System Scope section) → **02_BUSINESS_RULES.md** (Resource Limits section)

### For Technical Leads
Priority reading order:
1. 01_PROJECT_OVERVIEW.md - Understand the vision
2. 02_BUSINESS_RULES.md - Critical business logic
3. 03_SYSTEM_ARCHITECTURE.md (to be created)
4. 07_BILLING_SUBSCRIPTION_SYSTEM.md (to be created - fully implemented feature)

---

## Implementation Status Reference

### ✅ Fully Implemented & Tested (Production Ready)

#### Core Services (97 tests, 264 assertions passing)
1. **BillingService** - 18 tests
   - Add-on purchase and cancellation
   - Proration calculations
   - Monthly cost calculations
   - Upgrade/downgrade logic

2. **UsageTrackingService** - 19 tests
   - Daily usage logging
   - Alert detection (75%, 90%, 100% thresholds)
   - Usage history and trends
   - Cleanup of old logs

3. **ResourceLimitService** - 28 tests
   - Hard limit enforcement
   - Approval workflow triggers
   - Counter management
   - Limit checking for all resource types

4. **ApprovalService (Enhanced)** - 9 tests
   - Resource limit checking before approval
   - Registration approval workflows
   - Boundary condition handling

#### Scheduled Jobs - 10 tests
1. **LogResourceUsageJob** - Runs daily at 00:00 IST
2. **CleanupOldUsageLogsJob** - Runs weekly, Sundays at 02:00

#### API Endpoints - 13 tests
**BillingController** with 8 RESTful endpoints:
- POST `/subscriptions/{id}/addons` - Add addon
- DELETE `/subscriptions/{id}/addons/{addonId}` - Remove addon
- POST `/subscriptions/{id}/upgrade-plan` - Upgrade
- POST `/subscriptions/{id}/downgrade-plan` - Downgrade
- POST `/subscriptions/{id}/cancel-subscription` - Cancel
- GET `/organizations/{id}/usage` - Current usage
- GET `/organizations/{id}/usage/alerts` - Usage alerts
- GET `/subscriptions/{id}/usage/history` - Usage history
- POST `/subscriptions/{id}/calculate-upgrade-price` - Price calculation

#### Database Models & Migrations
- ✅ Addon model (catalog of purchasable add-ons)
- ✅ subscription_addon pivot table
- ✅ Subscription model with relationships
- ✅ ResourceUsageLog model
- ✅ All factories and seeders

---

## Key Architecture Decisions

### 1. Modular Monolith
**Decision:** Use modular monolith over microservices

**Rationale:**
- Faster development in early stages
- Simpler deployment and operations
- No network latency between modules
- Clear migration path to microservices if needed
- Better for team of <20 developers

**Trade-offs:**
- All modules share same database
- Deployment is all-or-nothing
- Requires discipline to maintain module boundaries

---

### 2. Domain-Driven Design
**Decision:** Organize code by business domains, not technical layers

**Structure:**
```
app/Modules/
  ├── Billing/
  │   ├── Domain/           # Business logic, models
  │   ├── Application/      # Use cases, services
  │   ├── Infrastructure/   # Database, external APIs
  │   └── Presentation/     # Controllers, API
  └── Organizations/
      ├── Domain/
      ├── Application/
      ├── Infrastructure/
      └── Presentation/
```

**Benefits:**
- Code reflects business concepts
- Easy to locate related functionality
- Natural boundaries for future extraction
- Better collaboration with domain experts

---

### 3. Multi-Database Support
**Decision:** Support both MySQL (production) and SQLite (testing/dev)

**Implementation:**
- Use Laravel's database abstraction
- Avoid database-specific features
- Test suite runs on SQLite for speed
- Production uses MySQL for performance

---

### 4. No Payment Gateway Integration
**Decision:** Explicitly exclude payment processing

**Reason:** India-only operations with specific business requirements

**Alternative:** Manual billing process or future India-specific integration

---

## Critical Implementation Notes

### Resource Limit Enforcement
```
BEFORE creating resource:
  1. Check current count vs plan limit
  2. If at limit:
     - Block operation
     - Return user-friendly error
     - Log attempt
     - Suggest upgrade
  3. If under limit:
     - Create resource
     - Increment counter
     - Check for warning thresholds (75%, 90%)
```

### Tenant Data Isolation
```
EVERY database query must:
  1. Include organization_id filter
  2. Use global query scopes
  3. Validate user has access to organization
  4. Log access for audit trail

NEVER allow:
  - Cross-tenant data access
  - Organization ID manipulation in API
  - Direct database queries without scopes
```

### Approval Workflow Integration
```
Critical operations require approval:
  1. User registration → ApprovalService
  2. Sub-org creation → ApprovalService
  3. Resource limit exceeded → Block + Approval option

Approval process:
  - Check limits BEFORE approval
  - Reject if limits would be exceeded
  - Send notifications to approvers
  - Log full audit trail
```

---

## Testing Philosophy

### Coverage Requirements
- Unit tests: All business logic services
- Feature tests: All API endpoints
- Integration tests: Cross-module workflows
- **Current: 97 tests, 264 assertions, 100% passing**

### Test Pyramid
```
        ╱  E2E Tests (Future)
       ╱──────────────────
      ╱   Feature Tests (13)
     ╱────────────────────────
    ╱   Unit Tests (84)
   ╱────────────────────────────
```

### Critical Test Scenarios
1. **Tenant Isolation:**
   - User A cannot access Tenant B data
   - API requires valid organization context

2. **Resource Limits:**
   - Block at exact limit boundary
   - Alert at 75%, 90%, 100%
   - Approval workflow triggered correctly

3. **Subscription Lifecycle:**
   - Proration calculations accurate
   - Upgrade/downgrade logic correct
   - Add-on purchase and removal

---

## Next Steps for Documentation

### Priority 1 (Critical for Onboarding)
1. Create **03_SYSTEM_ARCHITECTURE.md** with diagrams
2. Create **07_BILLING_SUBSCRIPTION_SYSTEM.md** (detailed, feature is complete)
3. Create **04_MODULE_STRUCTURE.md** (explains codebase organization)

### Priority 2 (Important for Development)
1. Create **13_TESTING_STRATEGY.md** (document existing 97 tests)
2. Create **11_SCHEDULED_JOBS.md** (document 2 implemented jobs)
3. Create **10_API_STRUCTURE.md** (document 8 billing endpoints)

### Priority 3 (Good to Have)
1. Create **05_DATABASE_DESIGN.md** with ERD diagrams
2. Create **14_DEPLOYMENT_GUIDE.md**
3. Create **15_FUTURE_ENHANCEMENTS.md**

---

## Diagram Directory Structure (To Be Created)

```
foundation_architecture/
  ├── diagrams/
  │   ├── architecture/
  │   │   ├── high-level-architecture.png
  │   │   ├── module-dependencies.png
  │   │   └── data-flow.png
  │   ├── database/
  │   │   ├── erd-full-schema.png
  │   │   ├── billing-tables.png
  │   │   └── organization-hierarchy.png
  │   ├── workflows/
  │   │   ├── user-registration-approval.png
  │   │   ├── subscription-lifecycle.png
  │   │   └── resource-limit-enforcement.png
  │   └── sequence/
  │       ├── api-authentication.png
  │       ├── limit-check-flow.png
  │       └── approval-process.png
```

---

## How to Contribute to Documentation

### When to Update
- Adding new features
- Changing business rules
- Modifying architecture
- Before major releases

### Documentation Standards
- Use Markdown format
- Include practical examples
- Add diagrams for complex concepts
- Keep language clear and concise
- Date all major updates

### Review Process
- Technical review by lead developer
- Business review for rule changes
- Quarterly documentation audit

---

## Contact & Support

### Documentation Questions
- Check index in **00_INDEX.md**
- Review related documents
- Contact: [Project Lead]

### Technical Questions
- Review test files for implementation examples
- Check inline code documentation
- Refer to API documentation endpoint

---

**Last Updated:** October 30, 2025  
**Version:** 1.0  
**Status:** Initial documentation in progress (2/15 documents complete)  
**Next Review:** January 2026
