<?php

namespace Tests\Feature\Notifications;

use Tests\TestCase;
use App\Modules\Notifications\Domain\Services\NotificationService;
use App\Modules\Organizations\Domain\Models\Organization;
use App\Modules\Users\Domain\Models\User;

class NotificationServiceTest extends TestCase
{
    private NotificationService $notificationService;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();
        $this->notificationService = app(NotificationService::class);
        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create(['organization_id' => $this->organization->id]);
    }

    public function test_can_send_email_notification()
    {
        $notification = $this->notificationService->sendEmail(
            $this->user,
            'Test Subject',
            'Test body content',
            ['key' => 'value']
        );

        $this->assertNotNull($notification);
        $this->assertEquals('email', $notification->type);
        $this->assertEquals('sent', $notification->status);
        $this->assertNotNull($notification->sent_at);
    }

    public function test_can_send_sms_notification()
    {
        $notification = $this->notificationService->sendSms(
            $this->user,
            'Test SMS message',
            ['key' => 'value']
        );

        $this->assertNotNull($notification);
        $this->assertEquals('sms', $notification->type);
        $this->assertEquals('sent', $notification->status);
    }

    public function test_can_send_in_app_notification()
    {
        $notification = $this->notificationService->sendInApp(
            $this->user,
            'In-App Title',
            'In-App message',
            ['key' => 'value']
        );

        $this->assertNotNull($notification);
        $this->assertEquals('in_app', $notification->type);
        $this->assertEquals('sent', $notification->status);
    }

    public function test_can_get_notifications()
    {
        $this->notificationService->sendEmail($this->user, 'Subject 1', 'Body 1');
        $this->notificationService->sendEmail($this->user, 'Subject 2', 'Body 2');
        $this->notificationService->sendSms($this->user, 'SMS 1');

        $notifications = $this->notificationService->getNotifications($this->user);

        $this->assertCount(3, $notifications);
    }

    public function test_can_mark_notification_as_read()
    {
        $notification = $this->notificationService->sendEmail(
            $this->user,
            'Test',
            'Body'
        );

        $this->assertNull($notification->read_at);

        $marked = $this->notificationService->markAsRead($notification);

        $this->assertNotNull($marked->read_at);
    }

    public function test_can_filter_notifications_by_type()
    {
        $this->notificationService->sendEmail($this->user, 'Email 1', 'Body');
        $this->notificationService->sendEmail($this->user, 'Email 2', 'Body');
        $this->notificationService->sendSms($this->user, 'SMS');

        $emails = $this->notificationService->getNotifications($this->user, ['type' => 'email']);

        $this->assertCount(2, $emails);
    }

    public function test_can_filter_unread_notifications()
    {
        $n1 = $this->notificationService->sendEmail($this->user, 'Email 1', 'Body');
        $n2 = $this->notificationService->sendEmail($this->user, 'Email 2', 'Body');

        $this->notificationService->markAsRead($n1);

        $unread = $this->notificationService->getNotifications($this->user, ['read' => false]);

        $this->assertCount(1, $unread);
    }
}
