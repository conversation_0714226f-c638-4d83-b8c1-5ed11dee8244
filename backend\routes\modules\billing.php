<?php

use Illuminate\Support\Facades\Route;

Route::middleware('auth')->group(function () {
    // Billing - Subscriptions
    Route::get('subscriptions', [\App\Modules\Billing\Http\Controllers\SubscriptionController::class, 'index']);
Route::post('subscriptions', [\App\Modules\Billing\Http\Controllers\SubscriptionController::class, 'store'])->middleware('check.permission:subscriptions.create');
    Route::get('subscriptions/{id}', [\App\Modules\Billing\Http\Controllers\SubscriptionController::class, 'show']);
Route::patch('subscriptions/{id}', [\App\Modules\Billing\Http\Controllers\SubscriptionController::class, 'update'])->middleware('check.permission:subscriptions.update');
Route::delete('subscriptions/{id}', [\App\Modules\Billing\Http\Controllers\SubscriptionController::class, 'destroy'])->middleware('check.permission:subscriptions.delete');
    Route::post('subscriptions/{id}/upgrade', [\App\Modules\Billing\Http\Controllers\SubscriptionController::class, 'upgrade']);
    Route::post('subscriptions/{id}/downgrade', [\App\Modules\Billing\Http\Controllers\SubscriptionController::class, 'downgrade']);
    Route::post('subscriptions/{id}/cancel', [\App\Modules\Billing\Http\Controllers\SubscriptionController::class, 'cancel']);

    // Billing - Enhanced Subscription Management
    Route::post('subscriptions/{subscriptionId}/addons', [\App\Modules\Billing\Presentation\API\BillingController::class, 'addAddon']);
    Route::delete('subscriptions/{subscriptionId}/addons/{addonId}', [\App\Modules\Billing\Presentation\API\BillingController::class, 'removeAddon']);
    Route::post('subscriptions/{subscriptionId}/upgrade-plan', [\App\Modules\Billing\Presentation\API\BillingController::class, 'upgrade']);
    Route::post('subscriptions/{subscriptionId}/downgrade-plan', [\App\Modules\Billing\Presentation\API\BillingController::class, 'downgrade']);
    Route::post('subscriptions/{subscriptionId}/cancel-subscription', [\App\Modules\Billing\Presentation\API\BillingController::class, 'cancel']);
    Route::post('subscriptions/{subscriptionId}/calculate-upgrade-price', [\App\Modules\Billing\Presentation\API\BillingController::class, 'calculateUpgradePrice']);

    // Billing - Resource Usage Tracking
    Route::get('organizations/{organizationId}/usage', [\App\Modules\Billing\Presentation\API\BillingController::class, 'getUsage']);
    Route::get('organizations/{organizationId}/usage/alerts', [\App\Modules\Billing\Presentation\API\BillingController::class, 'getUsageAlerts']);
    Route::get('subscriptions/{subscriptionId}/usage/history', [\App\Modules\Billing\Presentation\API\BillingController::class, 'getUsageHistory']);

    // Billing - Payments
    Route::get('payments', [\App\Modules\Billing\Http\Controllers\PaymentController::class, 'index']);
    Route::post('payments', [\App\Modules\Billing\Http\Controllers\PaymentController::class, 'store'])->middleware('check.permission:payments.create');
    Route::get('payments/{id}', [\App\Modules\Billing\Http\Controllers\PaymentController::class, 'show']);
    Route::post('payments/{id}/retry', [\App\Modules\Billing\Http\Controllers\PaymentController::class, 'retry']);
    Route::post('payments/{id}/refund', [\App\Modules\Billing\Http\Controllers\PaymentController::class, 'refund'])->middleware('check.permission:payments.refund');
    Route::post('payments/webhook', [\App\Modules\Billing\Http\Controllers\PaymentController::class, 'webhook']);

    // Billing - Invoices
    Route::get('invoices', [\App\Modules\Billing\Http\Controllers\InvoiceController::class, 'index']);
Route::post('invoices', [\App\Modules\Billing\Http\Controllers\InvoiceController::class, 'store'])->middleware('check.permission:invoices.create');
    Route::get('invoices/{id}', [\App\Modules\Billing\Http\Controllers\InvoiceController::class, 'show']);
    Route::get('invoices/{id}/pdf', [\App\Modules\Billing\Http\Controllers\InvoiceController::class, 'pdf']);
Route::post('invoices/{id}/send', [\App\Modules\Billing\Http\Controllers\InvoiceController::class, 'send'])->middleware('check.permission:invoices.send');
});
