<?php

namespace App\Modules\Users\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Users\Domain\Services\AuthenticationService;
use App\Modules\Users\Http\Requests\RegisterRequest;
use App\Modules\Users\Http\Requests\LoginRequest;
use Illuminate\Http\Request;

class AuthController extends Controller
{
    public function __construct(private readonly AuthenticationService $auth) {}

    public function register(RegisterRequest $request)
    {
        $user = $this->auth->register($request->validated());
        return response()->json(['user' => $user], 201);
    }

    public function login(LoginRequest $request)
    {
        $data = $this->auth->login($request->input('email'), $request->input('password'));
        return response()->json($data);
    }

    public function logout(Request $request)
    {
        $user = $request->user();
        $token = $request->bearerToken() ?? $request->input('token');
        if ($user && $token) {
            $this->auth->logout($user, $token);
        }
        return response()->json(['message' => 'Logged out']);
    }

    public function refreshToken(Request $request)
    {
        $user = $request->user();
        $oldToken = $request->input('token');
        $data = $this->auth->refreshToken($user, $oldToken);
        return response()->json($data);
    }

    public function requestOtp(Request $request)
    {
        $otp = $this->auth->requestOtp($request->user(), $request->input('purpose', 'login'));
        return response()->json(['otp_id' => $otp->id], 201);
    }

    public function verifyOtp(Request $request)
    {
        $ok = $this->auth->verifyOtp($request->user(), $request->input('code'), $request->input('purpose', 'login'));
        return response()->json(['verified' => $ok]);
    }

    public function resetPassword(Request $request)
    {
        $ok = $this->auth->resetPassword($request->input('token'), $request->input('password'));
        return response()->json(['reset' => $ok]);
    }

    public function setupMFA(Request $request)
    {
        $ok = $this->auth->setupMFA($request->user(), $request->input('secret'));
        return response()->json(['enabled' => $ok]);
    }

    public function verifyMFA(Request $request)
    {
        $ok = $this->auth->verifyMFA($request->user(), $request->input('code'));
        return response()->json(['verified' => $ok]);
    }
}
