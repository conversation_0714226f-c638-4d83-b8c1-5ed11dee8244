<?php

namespace App\Modules\Integration\Domain\Services;

use App\Modules\Shared\Domain\Services\BaseService;
use App\Modules\Integration\Domain\Models\Webhook;
use App\Modules\Integration\Domain\Models\WebhookEvent;
use App\Modules\Integration\Domain\Repositories\WebhookRepository;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use App\Modules\Integration\Jobs\RetryWebhookEventJob;

class WebhookService extends BaseService
{
    public function __construct(private readonly WebhookRepository $webhooks)
    {
    }

    /**
     * Create webhook
     */
    public function createWebhook(array $data): Webhook
    {
        $name = $data['name'] ?? ('Webhook-' . substr(hash('crc32', $data['url'] . '-' . implode(',', $data['events'] ?? [])), 0, 8));
        return $this->webhooks->create([
            'organization_id' => $data['organization_id'] ?? $this->getCurrentOrganizationId(),
            'name' => $name,
            'url' => $data['url'],
            'events' => $data['events'] ?? [],
            'secret' => $data['secret'] ?? bin2hex(random_bytes(32)),
            'is_active' => $data['is_active'] ?? true,
            'retry_count' => $data['retry_count'] ?? 3,
            'metadata' => $data['metadata'] ?? [],
        ]);
    }

    /**
     * Update webhook
     */
    public function updateWebhook(Webhook $webhook, array $data): Webhook
    {
        return $this->webhooks->update($webhook, $data);
    }

    /**
     * Delete webhook
     */
    public function deleteWebhook(Webhook $webhook): bool
    {
        return $this->webhooks->delete($webhook);
    }

    /**
     * Trigger event
     */
    public function triggerEvent(string $event, array $payload): void
    {
        $webhooks = Webhook::where('is_active', true)
            ->where('organization_id', $this->getCurrentOrganizationId())
            ->whereJsonContains('events', $event)
            ->get();

        foreach ($webhooks as $webhook) {
            $this->deliverEvent($webhook, $event, $payload);
        }
    }

    /**
     * Deliver event to webhook
     */
    public function deliverEvent(Webhook $webhook, string $event, array $payload): void
    {
        DB::transaction(function () use ($webhook, $event, $payload) {
            $webhookEvent = WebhookEvent::create([
                'organization_id' => $webhook->organization_id,
                'webhook_id' => $webhook->id,
                'event_type' => $event,
                'payload' => $payload,
                'status' => 'pending',
                'attempts' => 1,
            ]);

            try {
                $this->sendWebhookRequest($webhook, $webhookEvent);
            } catch (\Exception $e) {
                $this->logError('Webhook delivery failed', [
                    'webhook_id' => $webhook->id,
                    'event' => $event,
                    'error' => $e->getMessage(),
                ]);

                if ($webhookEvent->attempts < $webhook->retry_count) {
                    // Schedule retry
                    $this->scheduleRetry($webhookEvent);
                } else {
                    $webhookEvent->update(['status' => 'failed']);
                }
            }
        });
    }

    /**
     * Send webhook request
     */
    private function sendWebhookRequest(Webhook $webhook, WebhookEvent $event): void
    {
        $signature = $this->generateSignature($webhook, $event);

        $meta = $webhook->metadata ?? [];
        $extraHeaders = is_array($meta) && isset($meta['headers']) && is_array($meta['headers']) ? $meta['headers'] : [];

        $headers = array_merge(
            $extraHeaders,
            [
                'X-Webhook-Signature' => $signature,
                'X-Webhook-Event' => $event->event_type,
                'X-Webhook-Delivery' => $event->id,
                'Content-Type' => 'application/json',
            ]
        );

        $timeout = is_array($meta) && isset($meta['timeout']) ? (int) $meta['timeout'] : 30;

        $response = Http::timeout($timeout)
            ->withHeaders($headers)
            ->post($webhook->url, $event->payload);

        if ($response->successful()) {
            $event->update([
                'status' => 'delivered',
                'response_code' => $response->status(),
                'response_body' => $response->body(),
                'delivered_at' => now(),
            ]);

            $this->logAction('Webhook delivered', [
                'webhook_id' => $webhook->id,
                'event' => $event->event_type,
                'status' => $response->status(),
            ]);
        } else {
            throw new \Exception("Webhook delivery failed with status {$response->status()}");
        }
    }

    /**
     * Retry event delivery
     */
    public function retryEvent(WebhookEvent $event): void
    {
        $webhook = $event->webhook;

        if ($event->attempts >= $webhook->retry_count) {
            $event->update(['status' => 'failed']);
            return;
        }

        DB::transaction(function () use ($event, $webhook) {
            $event->update(['attempts' => $event->attempts + 1]);

            try {
                $this->sendWebhookRequest($webhook, $event);
            } catch (\Exception $e) {
                if ($event->attempts < $webhook->retry_count) {
                    $this->scheduleRetry($event);
                } else {
                    $event->update(['status' => 'failed']);
                }
            }
        });
    }

    /**
     * Schedule retry
     */
    private function scheduleRetry(WebhookEvent $event): void
    {
        $event->update(['status' => 'pending_retry']);
        $delay = pow(2, max(0, ($event->attempt - 1))) * 5;
        RetryWebhookEventJob::dispatch($event->id)->delay(now()->addSeconds((int) $delay));
    }

    /**
     * Generate webhook signature
     */
    private function generateSignature(Webhook $webhook, WebhookEvent $event): string
    {
        $payload = json_encode($event->payload);
        return hash_hmac('sha256', $payload, $webhook->secret);
    }

    /**
     * List webhooks
     */
    public function listWebhooks(array $filters = []): Collection
    {
        return $this->webhooks->all($filters);
    }

    /**
     * Get webhook by ID
     */
    public function getWebhookById(string $id): ?Webhook
    {
        return $this->webhooks->findById($id);
    }

    /**
     * Get webhook events
     */
    public function getWebhookEvents(Webhook $webhook, array $filters = []): Collection
    {
        $query = WebhookEvent::where('webhook_id', $webhook->id);

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['event'])) {
            $query->where('event_type', $filters['event']);
        }

        return $query->orderByDesc('created_at')->get();
    }
}
