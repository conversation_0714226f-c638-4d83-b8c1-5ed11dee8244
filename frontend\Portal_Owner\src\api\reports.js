import client from './client'

export const reportsAPI = {
  // Reports
  getSubscriptionsReport: (params) => client.get('/reports/subscriptions', { params }),
  getPaymentsReport: (params) => client.get('/reports/payments', { params }),
  getRevenueReport: (params) => client.get('/reports/revenue', { params }),
  getUsersReport: (params) => client.get('/reports/users', { params }),
  getApprovalsReport: (params) => client.get('/reports/approvals', { params }),
}
