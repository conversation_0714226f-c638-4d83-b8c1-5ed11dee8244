import React, { useState, useEffect } from 'react'
import { use<PERSON>arams, useNavigate } from 'react-router-dom'
import { CCard, CCardBody, CCardHeader, CCol, CRow, CForm, CFormLabel, CFormInput, CFormTextarea, CButton, CSpinner, CAlert } from '@coreui/react'
import { approvalsAPI } from '../../api/approvals'

const ApprovalDetail = () => {
  const { id } = useParams()
  const navigate = useNavigate()
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [formData, setFormData] = useState({ entity_type: '', entity_id: '', status: 'pending' })
  const [saving, setSaving] = useState(false)

  useEffect(() => {
    if (id && id !== 'new') {
      loadApproval()
    } else {
      setLoading(false)
    }
  }, [id])

  const loadApproval = async () => {
    try {
      setLoading(true)
      const response = await approvalsAPI.getApproval(id)
      setFormData(response.data.data)
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to load approval')
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleApprove = async () => {
    try {
      setSaving(true)
      await approvalsAPI.approveRequest(id, {})
      navigate('/approvals')
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to approve')
    } finally {
      setSaving(false)
    }
  }

  const handleReject = async () => {
    try {
      setSaving(true)
      await approvalsAPI.rejectRequest(id, {})
      navigate('/approvals')
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to reject')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return <CRow><CCol xs={12} className="text-center"><CSpinner color="primary" /></CCol></CRow>
  }

  return (
    <CRow>
      <CCol xs={12} md={8}>
        <CCard>
          <CCardHeader>
            <strong>Approval Request</strong>
          </CCardHeader>
          <CCardBody>
            {error && <CAlert color="danger">{error}</CAlert>}
            <CForm>
              <div className="mb-3">
                <CFormLabel htmlFor="entity_type">Entity Type</CFormLabel>
                <CFormInput id="entity_type" name="entity_type" value={formData.entity_type} disabled />
              </div>
              <div className="mb-3">
                <CFormLabel htmlFor="entity_id">Entity ID</CFormLabel>
                <CFormInput id="entity_id" name="entity_id" value={formData.entity_id} disabled />
              </div>
              <div className="mb-3">
                <CFormLabel htmlFor="status">Status</CFormLabel>
                <CFormInput id="status" name="status" value={formData.status} disabled />
              </div>
              {formData.status === 'pending' && (
                <div className="d-grid gap-2">
                  <CButton type="button" color="success" onClick={handleApprove} disabled={saving}>
                    {saving ? <CSpinner size="sm" className="me-2" /> : null}
                    Approve
                  </CButton>
                  <CButton type="button" color="danger" onClick={handleReject} disabled={saving}>
                    {saving ? <CSpinner size="sm" className="me-2" /> : null}
                    Reject
                  </CButton>
                </div>
              )}
              <CButton type="button" color="secondary" className="mt-2 w-100" onClick={() => navigate('/approvals')}>
                Back
              </CButton>
            </CForm>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  )
}

export default ApprovalDetail
