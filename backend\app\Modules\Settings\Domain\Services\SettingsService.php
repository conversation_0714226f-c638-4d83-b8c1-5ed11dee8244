<?php

namespace App\Modules\Settings\Domain\Services;

use App\Modules\Shared\Domain\Services\BaseService;
use App\Modules\Settings\Domain\Models\OrganizationSetting;
use App\Modules\Settings\Domain\Models\UserPreference;
use App\Modules\Settings\Domain\Repositories\SettingsRepository;
use App\Modules\Users\Domain\Models\User;
use Illuminate\Support\Facades\Cache;

class SettingsService extends BaseService
{
    public function __construct(private readonly SettingsRepository $settings)
    {
    }

    /**
     * Get setting value
     */
    public function getSetting(string $key, $default = null)
    {
        $orgId = $this->getCurrentOrganizationId();
        
        // Portal Owner can access any organization's settings
        if (!$this->isPortalOwner() && !$orgId) {
            return $default;
        }
        
        $cacheKey = "settings.{$orgId}.{$key}";

        return Cache::remember($cacheKey, 3600, function () use ($key, $orgId, $default) {
            $query = OrganizationSetting::query();
            if ($orgId) {
                $query->where('organization_id', $orgId);
            }
            
            $setting = $query->where('key', $key)->first();
            return $setting?->value ?? $default;
        });
    }

    /**
     * Set setting value
     */
    public function setSetting(string $key, $value): OrganizationSetting
    {
        $orgId = $this->getCurrentOrganizationId();

        $setting = OrganizationSetting::updateOrCreate(
            ['organization_id' => $orgId, 'key' => $key],
            ['value' => $value]
        );

        // Invalidate cache
        Cache::forget("settings.{$orgId}.{$key}");

        $this->logAction('Setting updated', [
            'key' => $key,
            'organization_id' => $orgId,
        ]);

        return $setting;
    }

    /**
     * Get organization settings
     */
    public function getOrganizationSettings(array $filters = []): array
    {
        // Portal Owner can get any organization's settings
        if ($this->isPortalOwner()) {
            $orgId = $filters['organization_id'] ?? null;
        } else {
            $orgId = $filters['organization_id'] ?? $this->getCurrentOrganizationId();
        }

        $query = OrganizationSetting::query();
        if ($orgId) {
            $query->where('organization_id', $orgId);
        }
        
        $settings = $query->get();

        return $settings->mapWithKeys(fn($s) => [$s->key => $s->value])->toArray();
    }

    /**
     * Update organization settings
     */
    public function updateOrganizationSettings(array $settings): array
    {
        $orgId = $this->getCurrentOrganizationId();

        foreach ($settings as $key => $value) {
            $this->setSetting($key, $value);
        }

        return $this->getOrganizationSettings();
    }

    /**
     * Get user preferences
     */
    public function getUserPreferences(User $user): array
    {
        $preferences = UserPreference::where('user_id', $user->id)->get();

        return $preferences->mapWithKeys(fn($p) => [$p->key => $p->value])->toArray();
    }

    /**
     * Update user preferences
     */
    public function updateUserPreferences(User $user, array $preferences): array
    {
        foreach ($preferences as $key => $value) {
            UserPreference::updateOrCreate(
                ['user_id' => $user->id, 'key' => $key],
                ['value' => $value]
            );
        }

        return $this->getUserPreferences($user);
    }

    /**
     * Get setting by key
     */
    public function getSettingByKey(string $key): ?OrganizationSetting
    {
        $orgId = $this->getCurrentOrganizationId();
        
        $query = OrganizationSetting::query();
        if ($orgId) {
            $query->where('organization_id', $orgId);
        }
        
        return $query->where('key', $key)->first();
    }

    /**
     * Delete setting
     */
    public function deleteSetting(string $key): bool
    {
        $orgId = $this->getCurrentOrganizationId();

        $query = OrganizationSetting::query();
        if ($orgId) {
            $query->where('organization_id', $orgId);
        }
        
        $deleted = $query->where('key', $key)->delete();

        if ($deleted) {
            Cache::forget("settings.{$orgId}.{$key}");
        }

        return $deleted > 0;
    }
}
