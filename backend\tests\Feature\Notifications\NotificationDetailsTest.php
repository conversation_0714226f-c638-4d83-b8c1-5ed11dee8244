<?php

namespace Tests\Feature\Notifications;

use Tests\TestCase;
use App\Modules\Users\Domain\Models\User;
use App\Modules\Organizations\Domain\Models\Organization;
use App\Modules\Notifications\Domain\Models\Notification;
use Illuminate\Foundation\Testing\RefreshDatabase;

class NotificationDetailsTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Organization $org;

    protected function setUp(): void
    {
        parent::setUp();
        $this->org = Organization::factory()->create();
        $this->user = User::factory()->create(['organization_id' => $this->org->id]);
    }

    public function test_can_show_notification()
    {
        $notification = Notification::factory()->create([
            'organization_id' => $this->org->id,
            'user_id' => $this->user->id,
        ]);
        $response = $this->actingAs($this->user)->getJson("/api/v1/notifications/{$notification->id}");
        $response->assertStatus(200);
        $response->assertJsonPath('id', $notification->id);
    }

    public function test_cannot_show_other_users_notification()
    {
        $other_user = User::factory()->create(['organization_id' => $this->org->id]);
        $notification = Notification::factory()->create([
            'organization_id' => $this->org->id,
            'user_id' => $other_user->id,
        ]);
        $response = $this->actingAs($this->user)->getJson("/api/v1/notifications/{$notification->id}");
        $response->assertStatus(404);
    }

    public function test_can_list_notifications()
    {
        Notification::factory()->count(3)->create([
            'organization_id' => $this->org->id,
            'user_id' => $this->user->id,
        ]);
        $response = $this->actingAs($this->user)->getJson('/api/v1/notifications');
        $response->assertStatus(200);
    }

    public function test_can_filter_notifications_by_type()
    {
        Notification::factory()->create([
            'organization_id' => $this->org->id,
            'user_id' => $this->user->id,
            'type' => 'email',
        ]);
        $response = $this->actingAs($this->user)->getJson('/api/v1/notifications?type=email');
        $response->assertStatus(200);
    }

    public function test_can_mark_notification_as_read()
    {
        $notification = Notification::factory()->create([
            'organization_id' => $this->org->id,
            'user_id' => $this->user->id,
            'read_at' => null,
        ]);
        $response = $this->actingAs($this->user)->patchJson("/api/v1/notifications/{$notification->id}/read");
        $response->assertStatus(200);
        $notification->refresh();
        $this->assertNotNull($notification->read_at);
    }
}
