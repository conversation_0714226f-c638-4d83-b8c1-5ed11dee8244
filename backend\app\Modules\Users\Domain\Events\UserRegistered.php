<?php

namespace App\Modules\Users\Domain\Events;

use App\Modules\Shared\Domain\Events\DomainEvent;

class UserRegistered extends DomainEvent
{
    public function __construct(
        public readonly string $userId,
        ?string $organizationId = null,
        ?string $actorUserId = null,
        array $payload = []
    ) {
        parent::__construct($organizationId, $actorUserId, array_merge($payload, ['user_id' => $userId]));
    }
}
