<?php

namespace Database\Factories\Modules\Billing\Domain\Models;

use App\Modules\Billing\Domain\Models\SubscriptionAddOn;
use App\Modules\Billing\Domain\Models\Subscription;
use Illuminate\Database\Eloquent\Factories\Factory;

class SubscriptionAddOnFactory extends Factory
{
    protected $model = SubscriptionAddOn::class;

    public function definition(): array
    {
        $unitPrice = $this->faker->randomElement([500, 1000, 1500, 2000, 2500, 3000]);
        $quantity = $this->faker->numberBetween(1, 10);

        return [
            'subscription_id' => Subscription::factory(),
            'add_on_type' => $this->faker->randomElement(['user', 'sub_org', 'storage', 'hierarchy_level', 'module', 'api_calls']),
            'quantity' => $quantity,
            'unit_price' => $unitPrice,
            'total_price' => $unitPrice * $quantity,
            'starts_at' => now(),
            'ends_at' => null,
            'metadata' => [],
        ];
    }

    public function user(): self
    {
        return $this->state([
            'add_on_type' => 'user',
            'unit_price' => 500,
        ]);
    }

    public function storage(): self
    {
        return $this->state([
            'add_on_type' => 'storage',
            'unit_price' => 100,
        ]);
    }

    public function active(): self
    {
        return $this->state([
            'starts_at' => now()->subDays(5),
            'ends_at' => null,
        ]);
    }

    public function expired(): self
    {
        return $this->state([
            'starts_at' => now()->subDays(30),
            'ends_at' => now()->subDays(5),
        ]);
    }

    public function pending(): self
    {
        return $this->state([
            'starts_at' => now()->addDays(5),
            'ends_at' => null,
        ]);
    }
}
