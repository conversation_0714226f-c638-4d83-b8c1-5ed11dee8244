<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscription_addon', function (Blueprint $table) {
            $table->uuid('subscription_id');
            $table->uuid('addon_id');
            $table->integer('quantity')->default(1);
            $table->decimal('price', 10, 2)->comment('Total price for this addon quantity');
            $table->timestamp('attached_at')->nullable();
            $table->timestamps();
            
            $table->primary(['subscription_id', 'addon_id']);
            
            $table->foreign('subscription_id')->references('id')->on('subscriptions')->onDelete('cascade');
            $table->foreign('addon_id')->references('id')->on('addons')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscription_addon');
    }
};
