<?php

namespace App\Modules\Settings\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Settings\Domain\Services\SettingsService;
use Illuminate\Http\Request;

class SettingsController extends Controller
{
    public function __construct(private readonly SettingsService $settingsService)
    {
    }

    /**
     * Get all organization settings
     */
    public function index(Request $request)
    {
        try {
            $settings = $this->settingsService->getOrganizationSettings([
                'organization_id' => auth()->user()->organization_id,
            ]);

            return response()->json(['data' => $settings]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Update multiple settings
     */
    public function update(Request $request)
    {
        try {
            $settings = $this->settingsService->updateOrganizationSettings($request->all());

            return response()->json(['data' => $settings, 'message' => 'Settings updated']);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Get single setting by key
     */
    public function showKey(string $key, Request $request)
    {
        try {
            $setting = $this->settingsService->getSettingByKey($key);

            if (!$setting) {
                return response()->json(['error' => 'Setting not found'], 404);
            }

            return response()->json(['key' => $key, 'value' => $setting->value]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Update single setting by key
     */
    public function updateKey(string $key, Request $request)
    {
        try {
            $validated = $request->validate([
                'value' => 'required',
            ]);

            $this->settingsService->setSetting($key, $validated['value']);

            return response()->json(['key' => $key, 'value' => $validated['value'], 'message' => 'Setting updated']);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json(['errors' => $e->errors()], 422);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }
}
