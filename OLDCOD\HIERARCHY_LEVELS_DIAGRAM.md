# ERP System Hierarchy Levels & Authorization Structure

## 📊 Complete System Architecture Diagram

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                         SYSTEM HIERARCHY LEVELS                              │
└─────────────────────────────────────────────────────────────────────────────┘

                              ┌──────────────────┐
                              │   PORTAL LEVEL   │
                              │  (System Admin)  │
                              └────────┬─────────┘
                                       │
                    ┌──────────────────┼──────────────────┐
                    │                  │                  │
                    ▼                  ▼                  ▼
            ┌──────────────┐  ┌──────────────┐  ┌──────────────┐
            │ ORGANIZATION │  │ ORGANIZATION │  │ ORGANIZATION │
            │   LEVEL 1    │  │   LEVEL 2    │  │   LEVEL 3    │
            │  (Root Org)  │  │ (Sub-Org)    │  │ (Sub-Org)    │
            └──────┬───────┘  └──────┬───────┘  └──────┬───────┘
                   │                 │                 │
        ┌──────────┼─────────┐       │                 │
        │          │         │       │                 │
        ▼          ▼         ▼       ▼                 ▼
    ┌────────┐ ┌────────┐ ┌────────┐ ┌────────┐ ┌────────┐
    │ DEPT 1 │ │ DEPT 2 │ │ DEPT 3 │ │ DEPT 4 │ │ DEPT 5 │
    │(Scope) │ │(Scope) │ │(Scope) │ │(Scope) │ │(Scope) │
    └───┬────┘ └───┬────┘ └───┬────┘ └───┬────┘ └───┬────┘
        │          │          │          │          │
    ┌───┴──┐   ┌───┴──┐   ┌───┴──┐   ┌───┴──┐   ┌───┴──┐
    │TEAM 1│   │TEAM 2│   │TEAM 3│   │TEAM 4│   │TEAM 5│
    │(Scope)   │(Scope)   │(Scope)   │(Scope)   │(Scope)
    └───┬──┘   └───┬──┘   └───┬──┘   └───┬──┘   └───┬──┘
        │          │          │          │          │
    ┌───┴──────────┴──────────┴──────────┴──────────┴───┐
    │                                                    │
    ▼                                                    ▼
┌──────────┐  ┌──────────┐  ┌──────────┐  ┌──────────┐
│  USER 1  │  │  USER 2  │  │  USER 3  │  │  USER N  │
│(Individual)│  │(Individual)│  │(Individual)│  │(Individual)│
└──────────┘  └──────────┘  └──────────┘  └──────────┘
```

---

## 🏢 Detailed Hierarchy Levels

### Level 1: PORTAL LEVEL (System-Wide)
**Scope**: Global system administration  
**Access**: System administrators only  
**Responsibilities**:
- Manage all organizations
- System-wide configuration
- Global user management
- System monitoring and maintenance

**Available Roles**:
- System Administrator
- System Auditor
- System Support

---

### Level 2: ORGANIZATION LEVEL (Root Organization)
**Scope**: `organization` (organization_id)  
**Access**: Organization members with appropriate roles  
**Responsibilities**:
- Organization management
- Sub-organization creation
- Organization-wide settings
- Organization member management

**Available Roles**:
- Organization Admin
- Organization Manager
- Organization Member

**Key Features**:
- Parent-child relationships via `parent_id`
- Closure table for hierarchical queries (`org_unit_closure`)
- Organization-scoped data isolation

---

### Level 3: SUB-ORGANIZATION LEVEL (Nested Organizations)
**Scope**: `organization` (inherits parent organization_id)  
**Access**: Sub-organization members  
**Responsibilities**:
- Sub-organization specific tasks
- Department management
- Team coordination

**Available Roles**:
- Sub-Org Admin
- Sub-Org Manager
- Sub-Org Member

**Hierarchy Features**:
```
Organization (Root)
├── Sub-Organization 1
│   ├── Sub-Organization 1.1
│   └── Sub-Organization 1.2
├── Sub-Organization 2
└── Sub-Organization 3
```

---

### Level 4: DEPARTMENT LEVEL (Functional Grouping)
**Scope**: `department` (custom scope_id)  
**Access**: Department members  
**Responsibilities**:
- Department operations
- Team management
- Resource allocation

**Available Roles**:
- Department Head
- Department Manager
- Department Lead
- Department Member

**Scope Assignment**:
```php
// Role assignment with department scope
RoleAssignment::create([
    'user_id' => $user->id,
    'role_id' => $role->id,
    'scope' => 'department',      // Scope type
    'scope_id' => $dept_id,       // Specific department ID
    'organization_id' => $org_id,
]);
```

---

### Level 5: TEAM LEVEL (Project/Functional Teams)
**Scope**: `team` (custom scope_id)  
**Access**: Team members  
**Responsibilities**:
- Team task execution
- Team collaboration
- Team reporting

**Available Roles**:
- Team Lead
- Team Member
- Team Contributor

**Scope Assignment**:
```php
// Role assignment with team scope
RoleAssignment::create([
    'user_id' => $user->id,
    'role_id' => $role->id,
    'scope' => 'team',            // Scope type
    'scope_id' => $team_id,       // Specific team ID
    'organization_id' => $org_id,
]);
```

---

### Level 6: INDIVIDUAL USER LEVEL (Personal)
**Scope**: `user` (custom scope_id)  
**Access**: Individual user  
**Responsibilities**:
- Personal task execution
- Personal settings
- Personal permissions

**Available Roles**:
- User (basic permissions)
- Power User (extended permissions)

**Scope Assignment**:
```php
// Role assignment with user scope
RoleAssignment::create([
    'user_id' => $user->id,
    'role_id' => $role->id,
    'scope' => 'user',            // Scope type
    'scope_id' => $user->id,      // User's own ID
    'organization_id' => $org_id,
]);
```

---

## 🔐 Authorization & Scope System

### Scope Types in RoleAssignment

```
┌─────────────────────────────────────────────────────────────┐
│                    SCOPE HIERARCHY                          │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  GLOBAL (Portal)                                           │
│    └─ ORGANIZATION (organization_id)                       │
│         └─ DEPARTMENT (scope='department', scope_id)       │
│              └─ TEAM (scope='team', scope_id)              │
│                   └─ USER (scope='user', scope_id)         │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### Scope Assignment Fields

| Field | Type | Purpose | Example |
|-------|------|---------|---------|
| `scope` | string | Scope type | 'global', 'organization', 'department', 'team', 'user' |
| `scope_id` | string | Specific scope identifier | dept_id, team_id, user_id |
| `organization_id` | string | Organization context | org_id |
| `expires_at` | datetime | Role expiration | 2025-12-31 |

---

## 👥 Role & Permission Hierarchy

### Role Assignment Model
```php
RoleAssignment {
    id: UUID
    organization_id: UUID          // Organization context
    user_id: UUID                  // User receiving role
    role_id: UUID                  // Role being assigned
    assigned_by: UUID              // Admin who assigned
    assigned_at: datetime          // When assigned
    expires_at: datetime|null      // Optional expiration
    scope: string                  // Scope level
    scope_id: string|null          // Scope identifier
}
```

### Permission Hierarchy
```
Permission (Global)
├── Module: users
│   ├── users.view
│   ├── users.create
│   ├── users.update
│   └── users.delete
├── Module: roles
│   ├── roles.view
│   ├── roles.create
│   ├── roles.update
│   └── roles.delete
├── Module: organizations
│   ├── organizations.view
│   ├── organizations.create
│   ├── organizations.update
│   └── organizations.delete
└── Module: [other modules...]
    ├── [module].view
    ├── [module].create
    ├── [module].update
    └── [module].delete
```

---

## 🔄 Authorization Flow

```
┌──────────────────────────────────────────────────────────────┐
│                 USER MAKES REQUEST                           │
└────────────────────┬─────────────────────────────────────────┘
                     │
                     ▼
        ┌────────────────────────────┐
        │ Authenticate User          │
        │ (Bearer Token)             │
        └────────────┬───────────────┘
                     │
                     ▼
        ┌────────────────────────────┐
        │ Check Organization Scoping │
        │ (organization_id match)    │
        └────────────┬───────────────┘
                     │
                     ▼
        ┌────────────────────────────┐
        │ Load User Roles            │
        │ (from role_assignments)    │
        └────────────┬───────────────┘
                     │
                     ▼
        ┌────────────────────────────┐
        │ Check Scope Match          │
        │ (scope & scope_id)         │
        └────────────┬───────────────┘
                     │
                     ▼
        ┌────────────────────────────┐
        │ Load Permissions           │
        │ (from roles + direct)      │
        └────────────┬───────────────┘
                     │
                     ▼
        ┌────────────────────────────┐
        │ Verify Permission          │
        │ (permission slug)          │
        └────────────┬───────────────┘
                     │
        ┌────────────┴───────────────┐
        │                            │
        ▼                            ▼
    ✅ ALLOWED                   ❌ DENIED
    (Execute action)             (Return 403)
```

---

## 📋 Data Isolation by Level

### Organization-Level Isolation
```php
// All data scoped to organization_id
User::forOrganization($org_id)->get();
Role::forOrganization($org_id)->get();
Permission::forOrganization($org_id)->get();
```

### Department-Level Isolation
```php
// Roles scoped to specific department
RoleAssignment::where('scope', 'department')
               ->where('scope_id', $dept_id)
               ->get();
```

### Team-Level Isolation
```php
// Roles scoped to specific team
RoleAssignment::where('scope', 'team')
               ->where('scope_id', $team_id)
               ->get();
```

### User-Level Isolation
```php
// Direct user permissions
UserPermission::where('user_id', $user_id)->get();
```

---

## 🗂️ Database Schema for Hierarchy

### Organizations Table
```sql
organizations {
    id: UUID (PK)
    parent_id: UUID (FK) -- NULL for root orgs
    type: string         -- 'organization', 'department', etc.
    name: string
    code: string
    status: string       -- 'active', 'inactive'
    ...
}
```

### OrgUnitClosure Table (Adjacency List)
```sql
org_unit_closure {
    ancestor_id: UUID    -- Parent/ancestor
    descendant_id: UUID  -- Child/descendant
    depth: integer       -- Distance (0 = self)
}
```

### RoleAssignments Table
```sql
role_assignments {
    id: UUID (PK)
    organization_id: UUID (FK)
    user_id: UUID (FK)
    role_id: UUID (FK)
    assigned_by: UUID (FK)
    assigned_at: datetime
    expires_at: datetime (nullable)
    scope: string        -- 'global', 'organization', 'department', 'team', 'user'
    scope_id: string     -- Specific scope identifier
}
```

### Roles Table
```sql
roles {
    id: UUID (PK)
    organization_id: UUID (FK)
    name: string
    slug: string
    description: text
    is_system: boolean   -- System roles cannot be deleted
    level: integer       -- Role hierarchy level
}
```

### Permissions Table
```sql
permissions {
    id: UUID (PK)
    organization_id: UUID (FK)
    name: string
    slug: string
    module: string       -- Module name (users, roles, etc.)
    description: text
}
```

---

## 🎯 Common Use Cases

### Use Case 1: Assign Role to User at Organization Level
```php
// User gets Admin role for entire organization
RoleAssignment::create([
    'user_id' => $user->id,
    'role_id' => $admin_role->id,
    'scope' => 'organization',
    'scope_id' => null,  // Null = entire organization
    'organization_id' => $org->id,
    'assigned_by' => auth()->id(),
    'assigned_at' => now(),
]);
```

### Use Case 2: Assign Role to User at Department Level
```php
// User gets Manager role for specific department
RoleAssignment::create([
    'user_id' => $user->id,
    'role_id' => $manager_role->id,
    'scope' => 'department',
    'scope_id' => $department_id,  // Specific department
    'organization_id' => $org->id,
    'assigned_by' => auth()->id(),
    'assigned_at' => now(),
]);
```

### Use Case 3: Assign Role with Expiration
```php
// Temporary role assignment (e.g., project lead)
RoleAssignment::create([
    'user_id' => $user->id,
    'role_id' => $project_lead_role->id,
    'scope' => 'team',
    'scope_id' => $team_id,
    'organization_id' => $org->id,
    'assigned_by' => auth()->id(),
    'assigned_at' => now(),
    'expires_at' => now()->addMonths(3),  // 3-month assignment
]);
```

### Use Case 4: Check User Permission at Specific Scope
```php
// Check if user can edit in specific department
$canEdit = $authService->checkPermissionAtLevel(
    user: $user,
    permissionSlug: 'users.update',
    scope: 'department',
    scopeId: $department_id
);
```

---

## 📊 Hierarchy Summary Table

| Level | Scope Type | Scope ID | Isolation | Typical Roles |
|-------|-----------|----------|-----------|---------------|
| 1 | Portal | - | System-wide | System Admin |
| 2 | Organization | organization_id | Organization | Org Admin, Manager |
| 3 | Sub-Org | organization_id | Sub-Org | Sub-Org Admin |
| 4 | Department | dept_id | Department | Dept Head, Manager |
| 5 | Team | team_id | Team | Team Lead, Member |
| 6 | User | user_id | Individual | User, Power User |

---

## 🔑 Key Features

✅ **Multi-level Hierarchy**: Support for unlimited organizational depth  
✅ **Flexible Scoping**: Roles can be assigned at any level  
✅ **Time-based Expiration**: Roles can expire automatically  
✅ **Organization Isolation**: Complete data isolation per organization  
✅ **Closure Table**: Efficient hierarchical queries  
✅ **Permission Inheritance**: Permissions flow through role hierarchy  
✅ **Direct Permissions**: Users can have permissions independent of roles  
✅ **Audit Trail**: Track who assigned roles and when  

---

## 🚀 Implementation Notes

### Authorization Service Methods
```php
// Check if user has role
$authService->userHasRole($user, 'admin');

// Check if user has permission
$authService->userHasPermission($user, 'users.create');

// Get all effective permissions
$permissions = $authService->getEffectivePermissions($user);

// Check permission at specific scope
$authService->checkPermissionAtLevel(
    $user, 
    'users.update', 
    'department', 
    $dept_id
);
```

### Organization Service Methods
```php
// Get organization hierarchy
$hierarchy = $orgService->getHierarchy($org_id);

// Get ancestors (parents)
$ancestors = $orgService->getAncestors($org_id);

// Get descendants (children)
$descendants = $orgService->getDescendants($org_id);

// Move organization in hierarchy
$orgService->moveOrganization($org, $new_parent_id);
```

---

**Document Generated**: October 29, 2025  
**System**: ERP Backend API  
**Architecture**: DDD Modular Monolith with Multi-Level Hierarchy
