<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Modules\Billing\Application\Services\UsageTrackingService;
use Illuminate\Support\Facades\Log;

class LogResourceUsageJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Execute the job.
     */
    public function handle(UsageTrackingService $usageTrackingService): void
    {
        Log::info('Starting daily resource usage logging job');

        try {
            $result = $usageTrackingService->logAllActiveSubscriptions();

            Log::info('Daily resource usage logging completed', [
                'total_subscriptions' => $result['total'],
                'successful' => $result['success'],
                'failed' => $result['failed'],
            ]);

            if ($result['failed'] > 0) {
                Log::warning('Some subscriptions failed to log usage', [
                    'errors' => $result['errors'],
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Daily resource usage logging job failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('LogResourceUsageJob failed permanently', [
            'error' => $exception->getMessage(),
        ]);
    }
}
