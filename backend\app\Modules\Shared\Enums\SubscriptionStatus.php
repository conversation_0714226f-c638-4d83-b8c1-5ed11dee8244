<?php

namespace App\Modules\Shared\Enums;

enum SubscriptionStatus: string
{
    case ACTIVE = 'active';
    case CANCELLED = 'cancelled';
    case EXPIRED = 'expired';
    case TRIAL = 'trial';
    case SUSPENDED = 'suspended';
    case PENDING = 'pending';

    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    public function label(): string
    {
        return match($this) {
            self::ACTIVE => 'Active',
            self::CANCELLED => 'Cancelled',
            self::EXPIRED => 'Expired',
            self::TRIAL => 'Trial Period',
            self::SUSPENDED => 'Suspended',
            self::PENDING => 'Pending Payment',
        };
    }
}
