<?php

namespace Tests\Feature\Reports;

use Tests\TestCase;
use App\Modules\Users\Domain\Models\User;
use App\Modules\Organizations\Domain\Models\Organization;
use App\Modules\Billing\Domain\Models\Subscription;
use App\Modules\Billing\Domain\Models\Payment;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ReportEndpointsTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Organization $org;

    protected function setUp(): void
    {
        parent::setUp();
        $this->org = Organization::factory()->create();
        $this->user = User::factory()->create(['organization_id' => $this->org->id]);
    }

    public function test_can_get_subscription_report()
    {
        Subscription::factory()->create(['organization_id' => $this->org->id]);
        $response = $this->actingAs($this->user)->getJson('/api/v1/reports/subscriptions');
        $response->assertStatus(200);
    }

    public function test_can_get_payment_report()
    {
        Payment::factory()->create(['organization_id' => $this->org->id]);
        $response = $this->actingAs($this->user)->getJson('/api/v1/reports/payments');
        $response->assertStatus(200);
    }

    public function test_can_get_revenue_report()
    {
        Payment::factory()->create(['organization_id' => $this->org->id, 'status' => 'completed']);
        $response = $this->actingAs($this->user)->getJson('/api/v1/reports/revenue');
        $response->assertStatus(200);
    }

    public function test_can_get_user_activity_report()
    {
        $response = $this->actingAs($this->user)->getJson('/api/v1/reports/users');
        $response->assertStatus(200);
    }

    public function test_can_get_approval_report()
    {
        $response = $this->actingAs($this->user)->getJson('/api/v1/reports/approvals');
        $response->assertStatus(200);
    }

    public function test_reports_accept_date_filters()
    {
        $response = $this->actingAs($this->user)->getJson('/api/v1/reports/payments?start_date=2025-01-01&end_date=2025-12-31');
        $response->assertStatus(200);
    }
}
