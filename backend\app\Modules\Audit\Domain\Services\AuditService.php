<?php

namespace App\Modules\Audit\Domain\Services;

use App\Modules\Shared\Domain\Services\BaseService;
use App\Modules\Audit\Domain\Models\AuditTrail;
use App\Modules\Audit\Domain\Repositories\AuditRepository;
use App\Modules\Users\Domain\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class AuditService extends BaseService
{
    public function __construct(private readonly AuditRepository $audits)
    {
    }

    /**
     * Log activity
     */
    public function logActivity(string $action, string $entityType, string $entityId, array $changes = [], ?User $user = null): AuditTrail
    {
        $user = $user ?? auth()->user();

        return $this->audits->create([
            'organization_id' => $user?->organization_id ?? $this->getCurrentOrganizationId(),
            'user_id' => $user?->id,
            'action' => $action,
            'entity_type' => $entityType,
            'entity_id' => $entityId,
            'changes' => $changes,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now(),
        ]);
    }

    /**
     * Log user action
     */
    public function logUserAction(User $user, string $action, array $context = []): AuditTrail
    {
        return $this->audits->create([
            'organization_id' => $user->organization_id,
            'user_id' => $user->id,
            'action' => $action,
            'entity_type' => 'User',
            'entity_id' => $user->id,
            'changes' => $context,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now(),
        ]);
    }

    /**
     * Get activity log
     */
    public function getActivityLog(array $filters = []): Collection
    {
        $query = $this->audits->query()
            ->where('organization_id', $filters['organization_id'] ?? $this->getCurrentOrganizationId());

        if (isset($filters['user_id'])) {
            $query->where('user_id', $filters['user_id']);
        }

        if (isset($filters['action'])) {
            $query->where('action', $filters['action']);
        }

        if (isset($filters['entity_type'])) {
            $query->where('entity_type', $filters['entity_type']);
        }

        if (isset($filters['start_date'])) {
            $query->where('timestamp', '>=', $filters['start_date']);
        }

        if (isset($filters['end_date'])) {
            $query->where('timestamp', '<=', $filters['end_date']);
        }

        return $query->orderByDesc('timestamp')->get();
    }

    /**
     * Get audit trail
     */
    public function getAuditTrail(string $entityType, string $entityId): Collection
    {
        return $this->audits->query()
            ->where('entity_type', $entityType)
            ->where('entity_id', $entityId)
            ->orderByDesc('timestamp')
            ->get();
    }

    /**
     * Generate audit report
     */
    public function generateAuditReport(array $filters = []): array
    {
        $orgId = $filters['organization_id'] ?? $this->getCurrentOrganizationId();
        $startDate = $filters['start_date'] ?? now()->subMonth();
        $endDate = $filters['end_date'] ?? now();

        $logs = $this->audits->query()
            ->where('organization_id', $orgId)
            ->whereBetween('timestamp', [$startDate, $endDate])
            ->get();

        return [
            'total_actions' => $logs->count(),
            'by_action' => $logs->groupBy('action')->map(fn($group) => $group->count()),
            'by_entity_type' => $logs->groupBy('entity_type')->map(fn($group) => $group->count()),
            'by_user' => $logs->groupBy('user_id')->map(fn($group) => [
                'count' => $group->count(),
                'user' => $group->first()->user,
            ]),
            'recent_activities' => $logs->take(10)->map(fn($log) => [
                'action' => $log->action,
                'entity_type' => $log->entity_type,
                'user' => $log->user?->name,
                'timestamp' => $log->timestamp,
            ]),
        ];
    }

    /**
     * Export audit log
     */
    public function exportAuditLog(array $filters = []): string
    {
        $logs = $this->getActivityLog($filters);

        $csv = "Timestamp,User,Action,Entity Type,Entity ID,IP Address,Changes\n";

        foreach ($logs as $log) {
            $csv .= sprintf(
                "%s,%s,%s,%s,%s,%s,%s\n",
                $log->timestamp,
                $log->user?->name ?? 'Unknown',
                $log->action,
                $log->entity_type,
                $log->entity_id,
                $log->ip_address,
                json_encode($log->changes)
            );
        }

        return $csv;
    }
}
