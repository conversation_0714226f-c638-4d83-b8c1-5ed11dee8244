<?php

namespace App\Modules\Users\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateUserRequest extends FormRequest
{
    public function authorize(): bool { return true; }
    public function rules(): array
    {
        return [
            'name' => ['sometimes','string','max:100'],
            'email' => ['sometimes','email','max:255'],
            'mobile' => ['sometimes','nullable','string','max:20'],
            'password' => ['sometimes','string','min:8'],
            'status' => ['sometimes','in:active,inactive,suspended'],
        ];
    }
}
