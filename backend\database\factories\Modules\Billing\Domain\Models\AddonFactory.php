<?php

namespace Database\Factories\Modules\Billing\Domain\Models;

use App\Modules\Billing\Domain\Models\Addon;
use App\Modules\Organizations\Domain\Models\Organization;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class AddonFactory extends Factory
{
    protected $model = Addon::class;

    public function definition(): array
    {
        $name = fake()->words(3, true);
        
        return [
            'organization_id' => Organization::factory(),
            'name' => ucfirst($name),
            'slug' => Str::slug($name),
            'description' => fake()->sentence(),
            'type' => fake()->randomElement(['storage', 'user', 'sub_org', 'module', 'api_calls']),
            'price' => fake()->randomFloat(2, 5, 100),
            'value' => fake()->numberBetween(1, 100),
            'unit' => fake()->randomElement(['GB', 'users', 'orgs', 'calls']),
            'is_active' => true,
            'metadata' => [],
        ];
    }
}
