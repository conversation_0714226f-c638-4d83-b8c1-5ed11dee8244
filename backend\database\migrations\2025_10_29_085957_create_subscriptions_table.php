<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscriptions', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('organization_id');
            $table->uuid('plan_id');
            $table->string('status', 50)->default('active')->comment('active, cancelled, expired, suspended');
            $table->timestamp('starts_at');
            $table->timestamp('ends_at')->nullable();
            $table->timestamp('trial_ends_at')->nullable();
            $table->timestamp('cancelled_at')->nullable();
            $table->boolean('auto_renew')->default(true);
            $table->decimal('price', 10, 2);
            $table->string('billing_period', 20)->default('monthly');
            $table->integer('user_count')->default(0)->comment('Current total users');
            $table->integer('sub_org_count')->default(0)->comment('Current number of sub-organizations');
            $table->bigInteger('storage_used')->default(0)->comment('Storage used in bytes');
            $table->integer('hierarchy_depth')->default(0)->comment('Current maximum hierarchy depth');
            $table->json('metadata')->nullable();
            $table->json('add_ons')->nullable()->comment('Active add-ons');
            $table->timestamps();
            $table->softDeletes();
            
            $table->index('organization_id');
            $table->index('plan_id');
            $table->index('status');
            $table->index('starts_at');
            $table->index('ends_at');
            
            $table->foreign('organization_id')->references('id')->on('organizations')->onDelete('cascade');
            $table->foreign('plan_id')->references('id')->on('plans')->onDelete('restrict');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscriptions');
    }
};
