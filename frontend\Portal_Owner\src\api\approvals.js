import client from './client'

export const approvalsAPI = {
  // Approvals CRUD
  getApprovals: (params) => client.get('/approvals', { params }),
  createApproval: (data) => client.post('/approvals', data),
  getApproval: (id) => client.get(`/approvals/${id}`),
  
  // Approval actions
  approveRequest: (id, data) => client.post(`/approvals/${id}/approve`, data),
  rejectRequest: (id, data) => client.post(`/approvals/${id}/reject`, data),
  addComment: (id, data) => client.post(`/approvals/${id}/comments`, data),
}
