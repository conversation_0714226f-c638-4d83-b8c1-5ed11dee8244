╔═══════════════════════════════════════════════════════════════════════════════╗
║                   ERP SYSTEM HIERARCHY LEVELS - VISUAL DIAGRAM                ║
║                                                                               ║
║                    Complete Multi-Level Authorization Structure              ║
╚═══════════════════════════════════════════════════════════════════════════════╝


═══════════════════════════════════════════════════════════════════════════════
                              LEVEL 1: PORTAL
═══════════════════════════════════════════════════════════════════════════════

                              ┌─────────────────┐
                              │  PORTAL LEVEL   │
                              │ (System Admin)  │
                              │                 │
                              │ Roles:          │
                              │ • System Admin  │
                              │ • System Auditor│
                              │ • System Support│
                              └────────┬────────┘
                                       │
                    ┌──────────────────┼──────────────────┐
                    │                  │                  │
                    ▼                  ▼                  ▼


═══════════════════════════════════════════════════════════════════════════════
                    LEVEL 2: ORGANIZATION (Root Level)
═══════════════════════════════════════════════════════════════════════════════

            ┌──────────────────┐  ┌──────────────────┐  ┌──────────────────┐
            │  ORGANIZATION 1  │  │  ORGANIZATION 2  │  │  ORGANIZATION 3  │
            │  (Root Org)      │  │  (Root Org)      │  │  (Root Org)      │
            │                  │  │                  │  │                  │
            │ Scope:           │  │ Scope:           │  │ Scope:           │
            │ organization_id  │  │ organization_id  │  │ organization_id  │
            │                  │  │                  │  │                  │
            │ Roles:           │  │ Roles:           │  │ Roles:           │
            │ • Org Admin      │  │ • Org Admin      │  │ • Org Admin      │
            │ • Org Manager    │  │ • Org Manager    │  │ • Org Manager    │
            │ • Org Member     │  │ • Org Member     │  │ • Org Member     │
            └────────┬─────────┘  └────────┬─────────┘  └────────┬─────────┘
                     │                     │                     │
                     │                     │                     │
        ┌────────────┼─────────┐           │                     │
        │            │         │           │                     │
        ▼            ▼         ▼           ▼                     ▼


═══════════════════════════════════════════════════════════════════════════════
                   LEVEL 3: SUB-ORGANIZATION (Nested)
═══════════════════════════════════════════════════════════════════════════════

    ┌──────────────┐ ┌──────────────┐ ┌──────────────┐ ┌──────────────┐
    │ SUB-ORG 1.1  │ │ SUB-ORG 1.2  │ │ SUB-ORG 2.1  │ │ SUB-ORG 3.1  │
    │ (Child Org)  │ │ (Child Org)  │ │ (Child Org)  │ │ (Child Org)  │
    │              │ │              │ │              │ │              │
    │ Scope:       │ │ Scope:       │ │ Scope:       │ │ Scope:       │
    │ organization │ │ organization │ │ organization │ │ organization │
    │              │ │              │ │              │ │              │
    │ Roles:       │ │ Roles:       │ │ Roles:       │ │ Roles:       │
    │ • Sub Admin  │ │ • Sub Admin  │ │ • Sub Admin  │ │ • Sub Admin  │
    │ • Sub Manager│ │ • Sub Manager│ │ • Sub Manager│ │ • Sub Manager│
    └────────┬─────┘ └────────┬─────┘ └────────┬─────┘ └────────┬─────┘
             │                │                │                │
             │                │                │                │
    ┌────────┴────┐    ┌──────┴──────┐    ┌───┴────┐    ┌──────┴──────┐
    │             │    │             │    │        │    │             │
    ▼             ▼    ▼             ▼    ▼        ▼    ▼             ▼


═══════════════════════════════════════════════════════════════════════════════
                     LEVEL 4: DEPARTMENT (Functional Groups)
═══════════════════════════════════════════════════════════════════════════════

┌──────────────┐ ┌──────────────┐ ┌──────────────┐ ┌──────────────┐ ┌──────────────┐
│ DEPARTMENT 1 │ │ DEPARTMENT 2 │ │ DEPARTMENT 3 │ │ DEPARTMENT 4 │ │ DEPARTMENT 5 │
│              │ │              │ │              │ │              │ │              │
│ Scope:       │ │ Scope:       │ │ Scope:       │ │ Scope:       │ │ Scope:       │
│ scope='dept' │ │ scope='dept' │ │ scope='dept' │ │ scope='dept' │ │ scope='dept' │
│ scope_id=ID  │ │ scope_id=ID  │ │ scope_id=ID  │ │ scope_id=ID  │ │ scope_id=ID  │
│              │ │              │ │              │ │              │ │              │
│ Roles:       │ │ Roles:       │ │ Roles:       │ │ Roles:       │ │ Roles:       │
│ • Dept Head  │ │ • Dept Head  │ │ • Dept Head  │ │ • Dept Head  │ │ • Dept Head  │
│ • Dept Mgr   │ │ • Dept Mgr   │ │ • Dept Mgr   │ │ • Dept Mgr   │ │ • Dept Mgr   │
│ • Dept Lead  │ │ • Dept Lead  │ │ • Dept Lead  │ │ • Dept Lead  │ │ • Dept Lead  │
└────────┬─────┘ └────────┬─────┘ └────────┬─────┘ └────────┬─────┘ └────────┬─────┘
         │                │                │                │                │
         │                │                │                │                │
    ┌────┴──┐         ┌───┴──┐        ┌───┴──┐        ┌───┴──┐        ┌───┴──┐
    │       │         │      │        │      │        │      │        │      │
    ▼       ▼         ▼      ▼        ▼      ▼        ▼      ▼        ▼      ▼


═══════════════════════════════════════════════════════════════════════════════
                        LEVEL 5: TEAM (Project Teams)
═══════════════════════════════════════════════════════════════════════════════

┌──────────┐ ┌──────────┐ ┌──────────┐ ┌──────────┐ ┌──────────┐ ┌──────────┐
│  TEAM 1  │ │  TEAM 2  │ │  TEAM 3  │ │  TEAM 4  │ │  TEAM 5  │ │  TEAM 6  │
│          │ │          │ │          │ │          │ │          │ │          │
│ Scope:   │ │ Scope:   │ │ Scope:   │ │ Scope:   │ │ Scope:   │ │ Scope:   │
│ scope=   │ │ scope=   │ │ scope=   │ │ scope=   │ │ scope=   │ │ scope=   │
│ 'team'   │ │ 'team'   │ │ 'team'   │ │ 'team'   │ │ 'team'   │ │ 'team'   │
│          │ │          │ │          │ │          │ │          │ │          │
│ Roles:   │ │ Roles:   │ │ Roles:   │ │ Roles:   │ │ Roles:   │ │ Roles:   │
│ • Lead   │ │ • Lead   │ │ • Lead   │ │ • Lead   │ │ • Lead   │ │ • Lead   │
│ • Member │ │ • Member │ │ • Member │ │ • Member │ │ • Member │ │ • Member │
│ • Contrib│ │ • Contrib│ │ • Contrib│ │ • Contrib│ │ • Contrib│ │ • Contrib│
└────┬─────┘ └────┬─────┘ └────┬─────┘ └────┬─────┘ └────┬─────┘ └────┬─────┘
     │            │            │            │            │            │
     │            │            │            │            │            │
  ┌──┴──┐      ┌──┴──┐      ┌──┴──┐      ┌──┴──┐      ┌──┴──┐      ┌──┴──┐
  │     │      │     │      │     │      │     │      │     │      │     │
  ▼     ▼      ▼     ▼      ▼     ▼      ▼     ▼      ▼     ▼      ▼     ▼


═══════════════════════════════════════════════════════════════════════════════
                      LEVEL 6: INDIVIDUAL USERS (Personal)
═══════════════════════════════════════════════════════════════════════════════

┌──────────┐ ┌──────────┐ ┌──────────┐ ┌──────────┐ ┌──────────┐ ┌──────────┐
│  USER 1  │ │  USER 2  │ │  USER 3  │ │  USER 4  │ │  USER 5  │ │  USER N  │
│          │ │          │ │          │ │          │ │          │ │          │
│ Scope:   │ │ Scope:   │ │ Scope:   │ │ Scope:   │ │ Scope:   │ │ Scope:   │
│ scope=   │ │ scope=   │ │ scope=   │ │ scope=   │ │ scope=   │ │ scope=   │
│ 'user'   │ │ 'user'   │ │ 'user'   │ │ 'user'   │ │ 'user'   │ │ 'user'   │
│          │ │          │ │          │ │          │ │          │ │          │
│ Roles:   │ │ Roles:   │ │ Roles:   │ │ Roles:   │ │ Roles:   │ │ Roles:   │
│ • User   │ │ • User   │ │ • User   │ │ • User   │ │ • User   │ │ • User   │
│ • Power  │ │ • Power  │ │ • Power  │ │ • Power  │ │ • Power  │ │ • Power  │
│   User   │ │   User   │ │   User   │ │   User   │ │   User   │ │   User   │
└──────────┘ └──────────┘ └──────────┘ └──────────┘ └──────────┘ └──────────┘


═══════════════════════════════════════════════════════════════════════════════
                         AUTHORIZATION FLOW DIAGRAM
═══════════════════════════════════════════════════════════════════════════════

                            USER REQUEST
                                  │
                                  ▼
                    ┌─────────────────────────┐
                    │  Authenticate User      │
                    │  (Bearer Token)         │
                    └────────────┬────────────┘
                                 │
                                 ▼
                    ┌─────────────────────────┐
                    │  Verify Organization   │
                    │  Scoping               │
                    └────────────┬────────────┘
                                 │
                                 ▼
                    ┌─────────────────────────┐
                    │  Load User Roles       │
                    │  (role_assignments)    │
                    └────────────┬────────────┘
                                 │
                                 ▼
                    ┌─────────────────────────┐
                    │  Check Scope Match     │
                    │  (scope & scope_id)    │
                    └────────────┬────────────┘
                                 │
                                 ▼
                    ┌─────────────────────────┐
                    │  Load Permissions      │
                    │  (from roles + direct) │
                    └────────────┬────────────┘
                                 │
                                 ▼
                    ┌─────────────────────────┐
                    │  Verify Permission     │
                    │  (permission slug)     │
                    └────────────┬────────────┘
                                 │
                    ┌────────────┴────────────┐
                    │                        │
                    ▼                        ▼
            ┌──────────────┐        ┌──────────────┐
            │ ✅ ALLOWED   │        │ ❌ DENIED    │
            │ Execute      │        │ Return 403   │
            │ Action       │        │ Forbidden    │
            └──────────────┘        └──────────────┘


═══════════════════════════════════════════════════════════════════════════════
                      SCOPE & PERMISSION HIERARCHY
═══════════════════════════════════════════════════════════════════════════════

                          PERMISSION TREE

                         Permission (Global)
                                 │
                ┌────────────────┼────────────────┐
                │                │                │
                ▼                ▼                ▼
            Module:          Module:          Module:
            Users            Roles            Orgs
                │                │                │
        ┌───────┼───────┐   ┌────┼────┐   ┌─────┼─────┐
        │       │       │   │    │    │   │     │     │
        ▼       ▼       ▼   ▼    ▼    ▼   ▼     ▼     ▼
      view   create  update delete view create update delete
      users  users   users  users  roles roles  roles  roles


                         SCOPE HIERARCHY

                    GLOBAL (Portal Level)
                            │
                    ORGANIZATION (Org ID)
                            │
                ┌───────────┼───────────┐
                │           │           │
                ▼           ▼           ▼
            DEPARTMENT  TEAM        USER
            (Dept ID)   (Team ID)   (User ID)


═══════════════════════════════════════════════════════════════════════════════
                    ROLE ASSIGNMENT STRUCTURE
═══════════════════════════════════════════════════════════════════════════════

RoleAssignment {
    ├─ id: UUID
    ├─ organization_id: UUID ─────────────────┐
    │                                         │
    ├─ user_id: UUID ──────────────────┐     │
    │                                  │     │
    ├─ role_id: UUID ──────────────────┤     │
    │                                  │     │
    ├─ assigned_by: UUID ──────────────┤     │
    │                                  │     │
    ├─ assigned_at: datetime ──────────┤     │
    │                                  │     │
    ├─ expires_at: datetime|null ──────┤     │
    │                                  │     │
    ├─ scope: string ───────────────┐  │     │
    │  Values:                       │  │     │
    │  • 'global'                    │  │     │
    │  • 'organization'              │  │     │
    │  • 'department'                │  │     │
    │  • 'team'                      │  │     │
    │  • 'user'                      │  │     │
    │                                │  │     │
    └─ scope_id: string|null ────────┘  │     │
       (Specific scope identifier)       │     │
                                        │     │
                                        └─────┴─ Organization Context


═══════════════════════════════════════════════════════════════════════════════
                         COMMON ROLE EXAMPLES
═══════════════════════════════════════════════════════════════════════════════

PORTAL LEVEL:
├─ System Administrator    [Permissions: All system-wide]
├─ System Auditor          [Permissions: View all, audit logs]
└─ System Support          [Permissions: Support operations]

ORGANIZATION LEVEL:
├─ Organization Admin      [Permissions: Manage org, users, settings]
├─ Organization Manager    [Permissions: Manage teams, projects]
└─ Organization Member     [Permissions: Basic operations]

DEPARTMENT LEVEL:
├─ Department Head         [Permissions: Manage dept, approve requests]
├─ Department Manager      [Permissions: Manage teams, resources]
├─ Department Lead         [Permissions: Lead projects, assign tasks]
└─ Department Member       [Permissions: Execute tasks]

TEAM LEVEL:
├─ Team Lead               [Permissions: Lead team, assign work]
├─ Team Member             [Permissions: Execute team tasks]
└─ Team Contributor        [Permissions: Contribute to team]

USER LEVEL:
├─ Power User              [Permissions: Extended personal permissions]
└─ User                    [Permissions: Basic personal operations]


═══════════════════════════════════════════════════════════════════════════════
                    DATA ISOLATION BY ORGANIZATION
═══════════════════════════════════════════════════════════════════════════════

Organization A                          Organization B
│                                       │
├─ Users (org_id = A)                  ├─ Users (org_id = B)
│  ├─ User 1                           │  ├─ User 5
│  ├─ User 2                           │  ├─ User 6
│  └─ User 3                           │  └─ User 7
│                                       │
├─ Roles (org_id = A)                  ├─ Roles (org_id = B)
│  ├─ Admin                            │  ├─ Manager
│  ├─ Manager                          │  └─ Contributor
│  └─ Member                           │
│                                       │
├─ Permissions (org_id = A)            ├─ Permissions (org_id = B)
│  ├─ users.view                       │  ├─ users.view
│  ├─ users.create                     │  └─ users.update
│  └─ users.delete                     │
│                                       │
└─ RoleAssignments (org_id = A)        └─ RoleAssignments (org_id = B)
   ├─ User 1 → Admin                      ├─ User 5 → Manager
   ├─ User 2 → Manager                    ├─ User 6 → Contributor
   └─ User 3 → Member                     └─ User 7 → Contributor

⚠️  IMPORTANT: Users from Org A CANNOT access data from Org B
    All queries are automatically scoped to organization_id


═══════════════════════════════════════════════════════════════════════════════
                         KEY FEATURES SUMMARY
═══════════════════════════════════════════════════════════════════════════════

✅ Multi-Level Hierarchy
   └─ Support for unlimited organizational depth

✅ Flexible Scoping
   └─ Roles assignable at any level (global, org, dept, team, user)

✅ Time-Based Expiration
   └─ Roles automatically expire on specified date

✅ Organization Isolation
   └─ Complete data separation per organization

✅ Closure Table
   └─ Efficient hierarchical queries (ancestors, descendants)

✅ Permission Inheritance
   └─ Permissions flow through role hierarchy

✅ Direct Permissions
   └─ Users can have permissions independent of roles

✅ Audit Trail
   └─ Track who assigned roles and when (assigned_by, assigned_at)

✅ Scope-Based Access Control
   └─ Fine-grained control at department/team/user level


═══════════════════════════════════════════════════════════════════════════════
Document Generated: October 29, 2025
System: ERP Backend API
Architecture: DDD Modular Monolith with Multi-Level Hierarchy
═══════════════════════════════════════════════════════════════════════════════
