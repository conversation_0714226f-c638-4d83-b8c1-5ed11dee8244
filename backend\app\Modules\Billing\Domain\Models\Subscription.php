<?php

namespace App\Modules\Billing\Domain\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Modules\Shared\Domain\Traits\HasUUID;
use App\Modules\Shared\Domain\Traits\HasOrganizationId;
use App\Modules\Organizations\Domain\Models\Organization;

class Subscription extends Model
{
    use HasFactory, SoftDeletes, HasUUID, HasOrganizationId;

    protected $table = 'subscriptions';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'organization_id',
        'plan_id',
        'status',
        'starts_at',
        'ends_at',
        'trial_ends_at',
        'cancelled_at',
        'auto_renew',
        'price',
        'billing_period',
        'user_count',
        'sub_org_count',
        'storage_used',
        'hierarchy_depth',
        'metadata',
        'add_ons',
    ];

    protected $casts = [
        'starts_at' => 'datetime',
        'ends_at' => 'datetime',
        'trial_ends_at' => 'datetime',
        'cancelled_at' => 'datetime',
        'auto_renew' => 'boolean',
        'price' => 'decimal:2',
        'user_count' => 'integer',
        'sub_org_count' => 'integer',
        'storage_used' => 'integer',
        'hierarchy_depth' => 'integer',
        'metadata' => 'array',
        'add_ons' => 'array',
    ];

    // Relationships
    public function organization()
    {
        return $this->belongsTo(Organization::class, 'organization_id');
    }

    public function plan()
    {
        return $this->belongsTo(Plan::class, 'plan_id');
    }

    public function payments()
    {
        return $this->hasMany(Payment::class, 'subscription_id');
    }

    public function invoices()
    {
        return $this->hasMany(Invoice::class, 'subscription_id');
    }

    public function addOns()
    {
        return $this->hasMany(
            \App\Modules\Billing\Domain\Models\SubscriptionAddOn::class,
            'subscription_id'
        );
    }

    public function addonCatalog()
    {
        return $this->belongsToMany(Addon::class, 'subscription_addon')
            ->withPivot('quantity', 'price', 'attached_at')
            ->withTimestamps();
    }

    public function resourceUsageLogs()
    {
        return $this->hasMany(
            \App\Modules\Billing\Domain\Models\ResourceUsageLog::class,
            'subscription_id'
        );
    }

    // Helper methods
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    public function isOnTrial(): bool
    {
        return $this->trial_ends_at && $this->trial_ends_at->isFuture();
    }

    public function isCancelled(): bool
    {
        return !is_null($this->cancelled_at);
    }

    public function isExpired(): bool
    {
        return $this->ends_at && $this->ends_at->isPast();
    }

    public function hasActiveAddOn(string $type): bool
    {
        return $this->addOns()
                    ->where('add_on_type', $type)
                    ->where('starts_at', '<=', now())
                    ->where(function($q) {
                        $q->whereNull('ends_at')
                          ->orWhere('ends_at', '>', now());
                    })
                    ->exists();
    }

    public function getTotalMonthlyPrice(): float
    {
        $basePrice = (float) $this->price;
        $addOnsTotal = $this->addOns()
                            ->where('starts_at', '<=', now())
                            ->where(function($q) {
                                $q->whereNull('ends_at')
                                  ->orWhere('ends_at', '>', now());
                            })
                            ->sum('total_price');
        
        return $basePrice + $addOnsTotal;
    }

    public function isUserLimitExceeded(): bool
    {
        $limit = $this->plan->user_limit;
        if ($limit === 0) return false; // unlimited
        return $this->user_count > $limit;
    }

    public function isSubOrgLimitExceeded(): bool
    {
        $limit = $this->plan->sub_org_limit;
        if ($limit === 0) return false; // unlimited
        return $this->sub_org_count > $limit;
    }

    public function isStorageLimitExceeded(): bool
    {
        $limit = $this->plan->storage_limit * 1024 * 1024 * 1024; // Convert GB to bytes
        if ($limit === 0) return false; // unlimited
        return $this->storage_used > $limit;
    }

    public function isHierarchyDepthExceeded(): bool
    {
        $limit = $this->plan->hierarchy_depth_limit;
        if ($limit === 0) return false; // unlimited
        return $this->hierarchy_depth > $limit;
    }

    public function getStorageUsagePercentage(): float
    {
        $limit = $this->plan->storage_limit * 1024 * 1024 * 1024;
        if ($limit === 0) return 0;
        return ($this->storage_used / $limit) * 100;
    }

    public function getUserUsagePercentage(): float
    {
        $limit = $this->plan->user_limit;
        if ($limit === 0) return 0;
        return ($this->user_count / $limit) * 100;
    }

    public function getSubOrgUsagePercentage(): float
    {
        $limit = $this->plan->sub_org_limit;
        if ($limit === 0) return 0;
        return ($this->sub_org_count / $limit) * 100;
    }
}
