<?php

namespace Database\Factories\Modules\Approvals\Domain\Models;

use App\Modules\Approvals\Domain\Models\ApprovalComment;
use App\Modules\Approvals\Domain\Models\ApprovalRequest;
use App\Modules\Users\Domain\Models\User;
use App\Modules\Organizations\Domain\Models\Organization;
use Illuminate\Database\Eloquent\Factories\Factory;

class ApprovalCommentFactory extends Factory
{
    protected $model = ApprovalComment::class;

    public function definition(): array
    {
        return [
            'id' => $this->faker->uuid(),
            'organization_id' => Organization::factory(),
            'approval_request_id' => ApprovalRequest::factory(),
            'user_id' => User::factory(),
            'comment' => $this->faker->sentence(),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}
