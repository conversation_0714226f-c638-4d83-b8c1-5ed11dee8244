import client from './client'

export const rolesAPI = {
  // Roles CRUD
  getRoles: (params) => client.get('/roles', { params }),
  createRole: (data) => client.post('/roles', data),
  getRole: (id) => client.get(`/roles/${id}`),
  updateRole: (id, data) => client.patch(`/roles/${id}`, data),
  deleteRole: (id) => client.delete(`/roles/${id}`),
  
  // Role-Permission management
  attachPermission: (roleId, permissionId) =>
    client.post(`/roles/${roleId}/permissions/${permissionId}`),
  detachPermission: (roleId, permissionId) =>
    client.delete(`/roles/${roleId}/permissions/${permissionId}`),
  
  // Permissions CRUD
  getPermissions: (params) => client.get('/permissions', { params }),
  createPermission: (data) => client.post('/permissions', data),
  getPermission: (id) => client.get(`/permissions/${id}`),
  updatePermission: (id, data) => client.patch(`/permissions/${id}`, data),
  deletePermission: (id) => client.delete(`/permissions/${id}`),
  
  // Role Assignments CRUD
  getRoleAssignments: (params) => client.get('/role-assignments', { params }),
  createRoleAssignment: (data) => client.post('/role-assignments', data),
  getRoleAssignment: (id) => client.get(`/role-assignments/${id}`),
  updateRoleAssignment: (id, data) => client.patch(`/role-assignments/${id}`, data),
  deleteRoleAssignment: (id) => client.delete(`/role-assignments/${id}`),
}
