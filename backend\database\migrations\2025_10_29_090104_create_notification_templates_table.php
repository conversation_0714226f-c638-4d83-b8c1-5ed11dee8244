<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('notification_templates', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('organization_id')->nullable()->comment('Null for system-wide templates');
            $table->string('name');
            $table->string('slug')->unique();
            $table->string('type', 50)->comment('email, sms, in_app');
            $table->string('subject')->nullable();
            $table->text('body');
            $table->json('variables')->nullable()->comment('Available template variables');
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            $table->softDeletes();
            
            $table->index('organization_id');
            $table->index('slug');
            $table->index('type');
            $table->index('is_active');
            
            $table->foreign('organization_id')->references('id')->on('organizations')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notification_templates');
    }
};
