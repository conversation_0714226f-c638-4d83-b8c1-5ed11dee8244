<?php

namespace App\Modules\Billing\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Billing\Domain\Services\PaymentService;
use App\Modules\Billing\Domain\Models\Payment;
use App\Modules\Billing\Http\Requests\CreatePaymentRequest;
use App\Modules\Billing\Http\Resources\PaymentResource;
use Illuminate\Http\Request;

class PaymentController extends Controller
{
    public function __construct(private readonly PaymentService $service) {}

    public function index()
    {
        return response()->json(PaymentResource::collection($this->service->listPayments()));
    }

    public function store(CreatePaymentRequest $request)
    {
        $payment = $this->service->processPayment($request->validated());
        return response()->json(new PaymentResource($payment), 201);
    }

    public function show(string $id)
    {
        $payment = $this->service->getPaymentById($id);
        abort_if(!$payment, 404);
        return response()->json(new PaymentResource($payment));
    }

    public function retry(string $id)
    {
        $payment = Payment::findOrFail($id);
        return response()->json(new PaymentResource($this->service->retryPayment($payment)));
    }

    public function refund(string $id)
    {
        $payment = Payment::findOrFail($id);
        return response()->json(new PaymentResource($this->service->refundPayment($payment)));
    }

    public function webhook(Request $request)
    {
        $this->service->handleWebhook($request->all());
        return response()->json(['received' => true]);
    }
}
