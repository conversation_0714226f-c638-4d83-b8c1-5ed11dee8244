import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useDispatch } from 'react-redux';

// MUI Components
import { SimpleTreeView } from '@mui/x-tree-view/SimpleTreeView';
import { TreeItem } from '@mui/x-tree-view/TreeItem';
import {
  ExpandMore as ExpandMoreIcon,
  ChevronRight as ChevronRightIcon,
  Person as PersonIcon,
  Business as BusinessIcon,
  LocationCity as LocationCityIcon,
  Group as GroupIcon,
  MoreVert as MoreVertIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  Search as SearchIcon,
  FilterList as FilterListIcon
} from '@mui/icons-material';

// CoreUI Components
import {
  CCard, CCardBody, CCardHeader, CCol, CRow, CForm, CFormLabel,
  CFormInput, CFormSelect, CButton, CSpinner, CAlert, CNav, CNavItem,
  CNavLink, CTabContent, CTabPane, CTable, CTableBody, CTableDataCell,
  CTableHead, CTableHeaderCell, CTableRow, CFormCheck, CBadge,
  CAccordion, CAccordionItem, CAccordionHeader, CAccordionBody,
  CInputGroup, CModal, CModalHeader, CModalTitle, CModalBody,
  CModalFooter, CFormText, CInputGroupText, CListGroup, CListGroupItem,
  CFormFloating, CFormTextarea, CFormSwitch, CToaster, CToast,
  CToastBody, CToastClose, CDropdown, CDropdownToggle, CDropdownMenu,
  CDropdownItem, CTooltip, CProgress, CProgressBar, CFormSelect as CSelect,
  CBreadcrumb, CBreadcrumbItem
} from '@coreui/react';

// CoreUI Icons
import CIcon from '@coreui/icons-react';
import {
  cilUserPlus, cilX, cilSearch, cilTrash, cilCheckCircle,
  cilWarning, cilInfo, cilArrowCircleBottom, cilArrowCircleTop,
  cilFilterX, cilCloudDownload, cilCloudUpload, cilPlus,
  cilMinus, cilChevronRight, cilChevronBottom
} from '@coreui/icons';

// API
import { organizationsAPI, usersAPI } from '../../api';
import { addToast } from '../../store/slices/toastSlice';

const OrganizationDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  // UI State
  const [activeTab, setActiveTab] = useState('details');
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  
  // Organization State
  const [organization, setOrganization] = useState({
    name: '',
    code: '',
    email: '',
    phone: '',
    address: '',
    timezone: 'UTC',
    currency: 'USD',
    language: 'en',
    status: 'active',
    parent_id: null,
    settings: {}
  });
  
  // Form State
  const [formData, setFormData] = useState({
    name: '',
    code: '',
    email: '',
    phone: '',
    address: '',
    timezone: 'UTC',
    currency: 'USD',
    language: 'en',
    status: 'active',
    parent_id: null,
  });
  
  // Hierarchy State
  const [hierarchy, setHierarchy] = useState([]);
  const [isLoadingHierarchy, setIsLoadingHierarchy] = useState(false);
  const [hierarchyError, setHierarchyError] = useState('');
  
  // Members State
  const [members, setMembers] = useState([]);
  const [isLoadingMembers, setIsLoadingMembers] = useState(false);
  const [membersError, setMembersError] = useState('');
  
  // Member Management State
  const [showAddMemberModal, setShowAddMemberModal] = useState(false);
  const [availableUsers, setAvailableUsers] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [isSearchingUsers, setIsSearchingUsers] = useState(false);

  useEffect(() => {
    if (id && id !== 'new') {
      loadOrganization()
    } else {
      setIsLoading(false)
    }
  }, [id])

  const loadOrganization = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await organizationsAPI.getOrganization(id);
      // Backend returns object directly from JsonResource
      setOrganization(response.data);
      setFormData(response.data);
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to load organization');
    } finally {
      setIsLoading(false);
    }
  }, [id]);

  const loadMembers = useCallback(async () => {
    try {
      setIsLoadingMembers(true);
      const response = await organizationsAPI.getMembers(id);
      // Backend returns array directly from JsonResource::collection()
      setMembers(response.data || []);
    } catch (err) {
      setMembersError('Failed to load members');
    } finally {
      setIsLoadingMembers(false);
    }
  }, [id]);

  const loadHierarchy = useCallback(async () => {
    try {
      setIsLoadingHierarchy(true);
      const response = await organizationsAPI.getHierarchy(id);
      // Backend returns array directly from JsonResource::collection()
      setHierarchy(response.data || []);
    } catch (err) {
      setHierarchyError('Failed to load organization hierarchy');
    } finally {
      setIsLoadingHierarchy(false);
    }
  }, [id]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      setIsSaving(true);
      if (id && id !== 'new') {
        await organizationsAPI.updateOrganization(id, formData);
      } else {
        const response = await organizationsAPI.createOrganization(formData);
        // Backend returns object directly from JsonResource
        navigate(`/organizations/${response.data.id}`);
        return;
      }
      setSuccess('Organization saved successfully');
      setTimeout(() => setSuccess(''), 3000);
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to save organization');
    } finally {
      setIsSaving(false);
    }
  };

  const handleRemoveMember = async (userId) => {
    try {
      setIsSaving(true);
      await organizationsAPI.removeMember(id, userId);
      await loadMembers();
    } catch (err) {
      setError('Failed to remove member');
    } finally {
      setIsSaving(false);
    }
  };

  const loadAvailableUsers = useCallback(async (search = '') => {
    try {
      const response = await usersAPI.getUsers({ search, exclude_org: id });
      setAvailableUsers(response.data.data || []);
    } catch (err) {
      setError('Failed to load available users');
    }
  }, [id]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleViewDetails = (node) => {
    // Navigate to the node's detail page
    navigate(`/organizations/${node.id}`);
  };

  const handleAddMembers = (orgId) => {
    setSelectedOrgForMembers(orgId);
    setShowAddMemberModal(true);
  };

  const handleDeleteNode = async (orgId) => {
    if (window.confirm('Are you sure you want to delete this organization? This action cannot be undone.')) {
      try {
        await organizationsAPI.deleteOrganization(orgId);
        setSuccess('Organization deleted successfully');
        navigate('/organizations');
      } catch (err) {
        setError(err.response?.data?.message || 'Failed to delete organization');
      }
    }
  };
  
  const [selectedOrgForMembers, setSelectedOrgForMembers] = useState(null);

  const toggleUserSelection = (userId) => {
    setSelectedUsers(prev =>
      prev.includes(userId)
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    );
  };

  useEffect(() => {
    if (showAddMemberModal) {
      loadAvailableUsers(searchTerm)
    }
  }, [showAddMemberModal, searchTerm]);

  const getOrgIcon = (type) => {
    switch(type) {
      case 'enterprise':
        return <LocationCityIcon className="text-primary me-2" />;
      case 'department':
        return <BusinessIcon className="text-info me-2" />;
      case 'team':
        return <GroupIcon className="text-success me-2" />;
      default:
        return <BusinessIcon className="text-secondary me-2" />;
    }
  };

  const getOrgTypeBadge = (type) => {
    const typeLabels = {
      'enterprise': { label: 'Enterprise', color: 'primary' },
      'department': { label: 'Department', color: 'info' },
      'team': { label: 'Team', color: 'success' },
      'unit': { label: 'Unit', color: 'warning' }
    };
    
    const typeInfo = typeLabels[type] || { label: type, color: 'secondary' };
    return (
      <CBadge color={typeInfo.color} className="ms-2">
        {typeInfo.label}
      </CBadge>
    );
  };

  const getMemberCountBadge = (count) => (
    <CBadge color="secondary" className="ms-2 d-inline-flex align-items-center">
      <PersonIcon fontSize="small" className="me-1" />
      {count || 0}
    </CBadge>
  );

  const getStatusBadge = (status) => {
    const statusMap = {
      active: { color: 'success', label: 'Active' },
      inactive: { color: 'secondary', label: 'Inactive' },
      suspended: { color: 'danger', label: 'Suspended' },
      pending: { color: 'warning', label: 'Pending' }
    };
    
    const statusInfo = statusMap[status] || { color: 'secondary', label: status };
    return (
      <CBadge color={statusInfo.color} className="ms-2">
        {statusInfo.label}
      </CBadge>
    );
  };

  const renderHierarchyNode = (node) => {
    const hasChildren = node.children && node.children.length > 0;
    
    return (
      <TreeItem
        key={node.id}
        nodeId={node.id.toString()}
        label={
          <div className="d-flex align-items-center">
            {getOrgIcon(node.type)}
            <span className="ms-2">{node.name}</span>
            <div className="ms-3">
              {getOrgTypeBadge(node.type)}
              {getStatusBadge(node.status)}
              {getMemberCountBadge(node.member_count)}
            </div>
            <div className="ms-2 d-flex">
              <CTooltip content="Add Child">
                <CButton size="sm" color="light" variant="ghost" className="p-1 me-1">
                  <AddIcon fontSize="small" />
                </CButton>
              </CTooltip>
              <CTooltip content="Edit">
                <CButton size="sm" color="light" variant="ghost" className="p-1 me-1">
                  <EditIcon fontSize="small" />
                </CButton>
              </CTooltip>
              <CDropdown direction="dropend" className="d-inline-block">
                <CDropdownToggle color="light" size="sm" variant="ghost" className="p-1">
                  <MoreVertIcon fontSize="small" />
                </CDropdownToggle>
                <CDropdownMenu>
                  <CDropdownItem>Edit Details</CDropdownItem>
                  <CDropdownItem onClick={() => handleViewDetails(node)}>View Details</CDropdownItem>
                  <CDropdownItem onClick={() => handleAddMembers(node.id)}>Add Members</CDropdownItem>
                  <CDropdownItem divider />
                  <CDropdownItem className="text-danger" onClick={() => handleDeleteNode(node.id)}>
                    Delete
                  </CDropdownItem>
                </CDropdownMenu>
              </CDropdown>
            </div>
          </div>
        }
        sx={{
          '& .MuiTreeItem-content': {
            padding: '8px',
            borderRadius: '4px',
            margin: '2px 0',
            '&:hover': {
              backgroundColor: 'rgba(0, 0, 0, 0.04)'
            },
            '&.Mui-selected, &.Mui-selected.Mui-focused': {
              backgroundColor: 'rgba(13, 110, 253, 0.1)',
              '&:hover': {
                backgroundColor: 'rgba(13, 110, 253, 0.15)'
              }
            }
          }
        }}
      >
        {hasChildren && node.children.map(child => renderHierarchyNode(child))}
      </TreeItem>
    );
  };
  
  // Load data when component mounts or when ID changes
  useEffect(() => {
    loadOrganization();
    if (id && id !== 'new') {
      loadHierarchy();
      loadMembers();
    }
  }, [id, loadOrganization, loadHierarchy, loadMembers]);
  
  // Load available users when modal is shown
  useEffect(() => {
    if (showAddMemberModal) {
      loadAvailableUsers(searchTerm);
    }
  }, [showAddMemberModal, searchTerm, loadAvailableUsers]);
  
  return (
    <CRow>
      <CCol xs={12}>
        <CCard>
          <CCardHeader>
            <div className="d-flex justify-content-between align-items-center">
              <div>
                <strong>{id && id !== 'new' ? 'Edit Organization' : 'Create Organization'}</strong>
                {id && id !== 'new' && (
                  <CBadge color="primary" className="ms-2">ID: {id}</CBadge>
                )}
              </div>
              <div>
                <CButton 
                  color="secondary" 
                  variant="outline" 
                  size="sm" 
                  className="me-2"
                  onClick={() => navigate('/organizations')}
                >
                  Back to List
                </CButton>
                <CButton 
                  color="primary" 
                  size="sm"
                  onClick={handleSubmit}
                  disabled={isSaving}
                >
                  {isSaving ? (
                    <>
                      <CSpinner size="sm" className="me-2" />
                      {id && id !== 'new' ? 'Updating...' : 'Creating...'}
                    </>
                  ) : (
                    <>{id && id !== 'new' ? 'Update' : 'Create'} Organization</>
                  )}
                </CButton>
              </div>
            </div>
          </CCardHeader>
          <CCardBody>
            {error && (
              <CAlert color="danger" className="mb-4" onClose={() => setError('')} dismissible>
                <strong>Error!</strong> {error}
              </CAlert>
            )}
            {success && (
              <CAlert color="success" className="mb-4" onClose={() => setSuccess('')} dismissible>
                <strong>Success!</strong> {success}
              </CAlert>
            )}
            
            <CNav variant="tabs" role="tablist">
              <CNavItem role="presentation">
                <CNavLink
                  active={activeTab === 'details'}
                  href="#"
                  onClick={() => setActiveTab('details')}
                >
                  Details
                </CNavLink>
              </CNavItem>
              {id && id !== 'new' && (
                <>
                <CNavItem role="presentation">
                  <CNavLink
                    active={activeTab === 'members'}
                    href="#"
                    onClick={() => setActiveTab('members')}
                  >
                    Members ({members.length})
                  </CNavLink>
                </CNavItem>
                <CNavItem role="presentation">
                  <CNavLink
                    active={activeTab === 'hierarchy'}
                    href="#"
                    onClick={() => setActiveTab('hierarchy')}
                  >
                    Hierarchy
                  </CNavLink>
                </CNavItem>
              </>
              )}
            </CNav>

            <CTabContent>
              <CTabPane role="tabpanel" aria-labelledby="details-tab" visible={activeTab === 'details'}>
                <div className="p-3">
                  <CForm onSubmit={handleSubmit}>
                    <CRow className="mb-3">
                      <CCol md={6}>
                        <div className="mb-3">
                          <CFormLabel htmlFor="name">Organization Name</CFormLabel>
                          <CFormInput
                            id="name"
                            name="name"
                            value={formData.name}
                            onChange={(event) => setFormData({ ...formData, name: event.target.value })}
                            placeholder="Enter organization name"
                            required
                          />
                        </div>
                      </CCol>
                      <CCol md={6}>
                        <div className="mb-3">
                          <CFormLabel htmlFor="type">Type</CFormLabel>
                        <CFormSelect
                          id="type"
                          name="type"
                          value={formData.type}
                          onChange={(event) => setFormData({ ...formData, type: event.target.value })}
                          required
                        >
                          <option value="">Select type</option>
                          <option value="enterprise">Enterprise</option>
                          <option value="department">Department</option>
                          <option value="team">Team</option>
                          <option value="unit">Unit</option>
                        </CFormSelect>
                        </div>
                    </CCol>
                  </CRow>
                  <CRow className="mb-3">
                    <CCol md={6}>
                      <div className="mb-3">
                        <CFormLabel htmlFor="email">Email</CFormLabel>
                        <CFormInput
                          type="email"
                          id="email"
                          name="email"
                          value={formData.email}
                          onChange={(event) => setFormData({ ...formData, email: event.target.value })}
                          placeholder="Enter organization email"
                        />
                      </div>
                    </CCol>
                    <CCol md={6}>
                      <div className="mb-3">
                        <CFormLabel htmlFor="phone">Phone</CFormLabel>
                        <CFormInput
                          type="tel"
                          id="phone"
                          name="phone"
                          value={formData.phone}
                          onChange={(event) => setFormData({ ...formData, phone: event.target.value })}
                          placeholder="Enter organization phone"
                        />
                      </div>
                    </CCol>
                  </CRow>
                  <CRow>
                    <CCol>
                      <div className="mb-3">
                        <CFormLabel htmlFor="description">Description</CFormLabel>
                        <CFormTextarea
                          id="description"
                          name="description"
                          value={formData.description}
                          onChange={(event) => setFormData({ ...formData, description: event.target.value })}
                          placeholder="Enter organization description"
                          rows={4}
                        />
                      </div>
                    </CCol>
                  </CRow>
                  </CForm>
                </div>
                </CTabPane>

                {id !== 'new' && (
                  <>
                    <CTabPane role="tabpanel" aria-labelledby="members-tab" visible={activeTab === 'members'}>
                      <div className="p-3">
                      <CRow className="mb-3">
                        <CCol className="d-flex justify-content-end">
                          <CButton
                            color="primary"
                            onClick={() => setShowAddMemberModal(true)}
                          >
                            <CIcon icon={cilUserPlus} className="me-2" />
                            Add Members
                          </CButton>
                        </CCol>
                      </CRow>
                      <CRow>
                        <CCol>
                          <CCard>
                            <CCardBody>
                              {members.length === 0 ? (
                                <CAlert color="info">No members found for this organization.</CAlert>
                              ) : (
                                <CTable striped hover responsive>
                                  <CTableHead>
                                    <CTableRow>
                                      <CTableHeaderCell>Name</CTableHeaderCell>
                                      <CTableHeaderCell>Email</CTableHeaderCell>
                                      <CTableHeaderCell>Role</CTableHeaderCell>
                                      <CTableHeaderCell>Status</CTableHeaderCell>
                                      <CTableHeaderCell>Actions</CTableHeaderCell>
                                    </CTableRow>
                                  </CTableHead>
                                  <CTableBody>
                                    {members.map((member) => (
                                      <CTableRow key={member.id}>
                                        <CTableDataCell>{member.name}</CTableDataCell>
                                        <CTableDataCell>{member.email}</CTableDataCell>
                                        <CTableDataCell>
                                          <CBadge color="primary">
                                            {member.role || 'Member'}
                                          </CBadge>
                                        </CTableDataCell>
                                        <CTableDataCell>
                                          <CBadge color={member.status === 'active' ? 'success' : 'secondary'}>
                                            {member.status || 'inactive'}
                                          </CBadge>
                                        </CTableDataCell>
                                        <CTableDataCell>
                                          <CButton
                                            color="danger"
                                            size="sm"
                                            variant="ghost"
                                            onClick={() => handleRemoveMember(member.id)}
                                            disabled={isSaving}
                                          >
                                            <CIcon icon={cilTrash} />
                                          </CButton>
                                        </CTableDataCell>
                                      </CTableRow>
                                    ))}
                                  </CTableBody>
                                </CTable>
                              )}
                            </CCardBody>
                          </CCard>
                        </CCol>
                      </CRow>
                      </div>
                    </CTabPane>

                    <CTabPane role="tabpanel" aria-labelledby="hierarchy-tab" visible={activeTab === 'hierarchy'}>
                      <div className="p-3">
                      {hierarchyLoading ? (
                        <div className="text-center py-5">
                          <CSpinner color="primary" className="me-2" />
                          <span>Loading organization hierarchy...</span>
                        </div>
                      ) : hierarchy ? (
                        <div className="border rounded p-3 bg-white shadow-sm">
                          <div className="mb-3 d-flex justify-content-between align-items-center">
                            <div>
                              <span className="text-muted small">
                                Showing {hierarchy.member_count || 0} members across {hierarchy.children?.length || 0} child organizations
                              </span>
                            </div>
                            <div className="d-flex align-items-center text-muted small">
                        <span className="me-3">
                          <span className="badge bg-primary me-1">Enterprise</span>
                          <span className="badge bg-info me-1">Department</span>
                          <span className="badge bg-success me-1">Team</span>
                          <span className="badge bg-warning">Unit</span>
                        </span>
                      </div>
                          </div>

                          <div className="border rounded p-3" style={{ minHeight: '400px' }}>
                            <SimpleTreeView
                              aria-label="organization hierarchy"
                              defaultExpandedItems={[hierarchy.id.toString()]}
                              sx={{
                                '& .MuiTreeItem-label': {
                                  width: '100%'
                                },
                                '& .MuiTreeItem-content': {
                                  padding: '4px 0'
                                }
                              }}
                            >
                              {renderHierarchyNode(hierarchy)}
                            </SimpleTreeView>
                          </div>
                        </div>
                      ) : (
                        <CAlert color="info" className="d-flex align-items-center">
                          <InfoIcon className="me-2" />
                          <div>
                            <h6>No hierarchy data available</h6>
                            <p className="mb-0">
                              This organization doesn't have any child organizations yet. 
                              Click 'Add Child Organization' to get started.
                            </p>
                          </div>
                        </CAlert>
                      )}
                      
                      <div className="mt-4 p-3 bg-light rounded">
                        <h6 className="mb-2">About Organization Hierarchy</h6>
                        <p className="small text-muted mb-2">
                          The organization hierarchy shows the complete structure of your organization, 
                          including all departments, teams, and units. You can expand/collapse nodes, 
                          search for specific organizations, and filter by type or status.
                        </p>
                        <div className="d-flex flex-wrap gap-2">
                          <CBadge color="primary">
                            <BusinessIcon fontSize="small" className="me-1" />
                            Enterprise: Top-level organization
                          </CBadge>
                          <CBadge color="info">
                            <LocationCityIcon fontSize="small" className="me-1" />
                            Department: Main divisions
                          </CBadge>
                          <CBadge color="success">
                            <GroupIcon fontSize="small" className="me-1" />
                            Team: Working groups
                          </CBadge>
                        </div>
                      </div>
                    </div>
                  </CTabPane>
                </>
              )}
            </CTabContent>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  )
}

export default OrganizationDetail
