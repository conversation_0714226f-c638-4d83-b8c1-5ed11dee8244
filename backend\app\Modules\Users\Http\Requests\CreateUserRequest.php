<?php

namespace App\Modules\Users\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CreateUserRequest extends FormRequest
{
    public function authorize(): bool { return true; }
    public function rules(): array
    {
        return [
            'organization_id' => ['required','uuid'],
            'name' => ['required','string','max:100'],
            'email' => ['required','email','max:255'],
            'mobile' => ['nullable','string','max:20'],
            'password' => ['required','string','min:8'],
            'status' => ['sometimes','in:active,inactive,suspended'],
        ];
    }
}
