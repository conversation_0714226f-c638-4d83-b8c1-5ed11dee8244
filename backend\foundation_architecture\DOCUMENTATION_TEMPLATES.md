# Complete Documentation Templates

This file contains comprehensive templates for all remaining documentation files. Each section should be expanded into its own file as indicated.

---

## 04_MODULE_STRUCTURE.md - Template

### Overview
Document each of the 13 modules in detail.

### For Each Module Include:

#### 1. **Organizations Module**
- **Purpose:** Multi-tenant hierarchy management
- **Key Responsibilities:**
  - Organization CRUD operations
  - Hierarchy tree management  
  - Parent-child relationships
  - Depth limit enforcement
- **Models:** Organization
- **Services:** OrganizationService
- **Key Methods:**
  - createOrganization()
  - getHierarchy()
  - validateDepthLimit()
- **Dependencies:** Users, Billing
- **Database Tables:** organizations
- **API Endpoints:** 
  - GET /organizations
  - POST /organizations
  - GET /organizations/{id}/hierarchy

#### 2. **Billing Module** ✅ FULLY IMPLEMENTED
- **Purpose:** Subscription and resource management
- **Key Responsibilities:**
  - Plan management
  - Subscription lifecycle
  - Add-on purchases
  - Resource usage tracking
  - Proration calculations
- **Models:** Plan, Subscription, Addon, SubscriptionAddOn, ResourceUsageLog
- **Services:** 
  - BillingService (18 tests)
  - UsageTrackingService (19 tests)
  - ResourceLimitService (28 tests)
- **Key Methods:**
  - addAddon() - Add addon to subscription
  - removeAddon() - Remove addon
  - upgradeSubscription() - Upgrade plan
  - downgradeSubscription() - Downgrade plan
  - cancelSubscription() - Cancel subscription
  - logResourceUsage() - Daily usage logging
  - checkResourceLimit() - Enforce limits
- **Dependencies:** Organizations, Approvals, Notifications
- **Database Tables:** plans, subscriptions, addons, subscription_addon, resource_usage_logs
- **API Endpoints:** 8 RESTful endpoints (documented in README.md)
- **Scheduled Jobs:**
  - LogResourceUsageJob - Daily at 00:00 IST
  - CleanupOldUsageLogsJob - Weekly Sundays at 02:00

#### 3. **Approvals Module** ✅ FULLY IMPLEMENTED
- **Purpose:** Workflow and approval management
- **Key Responsibilities:**
  - Approval request creation
  - Multi-step approvals
  - Resource limit checking before approval
  - Notification to approvers
- **Models:** ApprovalRequest, ApprovalStep, ApprovalComment
- **Services:** ApprovalService (Enhanced - 9 tests)
- **Key Methods:**
  - createApprovalRequest()
  - approveRequest() - With resource limit check
  - rejectRequest()
  - escalateRequest()
- **Dependencies:** Organizations, Users, Notifications
- **Database Tables:** approval_requests, approval_steps, approval_comments

#### 4. **Users Module**
- **Purpose:** User management
- **Key Responsibilities:**
  - User CRUD
  - Authentication
  - Profile management
  - Resource limit tracking
- **Models:** User
- **Services:** UserService
- **Dependencies:** Organizations, Billing
- **Database Tables:** users

#### 5. **Notifications Module**
- **Purpose:** Alert and notification system
- **Key Responsibilities:**
  - Send emails
  - In-app notifications
  - SMS alerts (future)
  - Priority-based delivery
- **Models:** Notification
- **Services:** NotificationService
- **Dependencies:** All modules
- **Database Tables:** notifications

#### 6. **Audit Module**
- **Purpose:** Activity logging and compliance
- **Key Responsibilities:**
  - Log all critical actions
  - Maintain audit trail
  - Compliance reporting
- **Models:** AuditLog
- **Services:** AuditService
- **Dependencies:** All modules
- **Database Tables:** audit_logs
- **Retention:** 7 years

#### 7. **Reports Module**
- **Purpose:** Analytics and reporting
- **Key Responsibilities:**
  - Usage reports
  - Billing reports
  - Activity reports
  - Custom dashboards
- **Services:** ReportService
- **Dependencies:** Billing, Organizations, Users

#### 8. **Settings Module**
- **Purpose:** System configuration
- **Key Responsibilities:**
  - Global settings
  - Tenant settings
  - Feature flags
- **Models:** Setting
- **Database Tables:** settings

#### 9. **Integration Module**
- **Purpose:** External API connectors
- **Key Responsibilities:**
  - Third-party integrations
  - Webhook management
  - API key management
- **Database Tables:** integrations, webhooks

#### 10-13. **Future Modules**
- RolesPermissions - Advanced RBAC
- Accounting - Financial management
- Inventory - Stock management  
- [Additional modules as needed]

### Module Communication Matrix

| From/To | Organizations | Billing | Approvals | Users | Notifications |
|---------|--------------|---------|-----------|-------|---------------|
| **Organizations** | - | ✓ | ✓ | ✓ | - |
| **Billing** | ✓ | - | ✓ | - | ✓ |
| **Approvals** | ✓ | ✓ | - | ✓ | ✓ |
| **Users** | ✓ | - | - | - | - |
| **Notifications** | - | - | - | - | - |

---

## 05_DATABASE_DESIGN.md - Template

### Database Schema Overview

#### Core Tables

**1. organizations**
```sql
id (UUID, PK)
parent_id (UUID, FK -> organizations.id, nullable)
type (enum: portal_owner, tenant, sub_organization)
name (string)
slug (string, unique)
status (enum: active, suspended, inactive)
metadata (json)
created_at, updated_at, deleted_at
```

**2. users**
```sql
id (UUID, PK)
organization_id (UUID, FK -> organizations.id)
name (string)
email (string, unique)
password (string, hashed)
status (enum: active, inactive, pending)
email_verified_at (timestamp)
created_at, updated_at
```

**3. plans**
```sql
id (UUID, PK)
organization_id (UUID, FK -> organizations.id)
name (string)
slug (string, unique)
description (text)
price (decimal)
yearly_price (decimal)
billing_period (enum: monthly, yearly)
user_limit (integer, 0 = unlimited)
storage_limit (integer, 0 = unlimited, in GB)
sub_org_limit (integer, 0 = unlimited)
hierarchy_depth_limit (integer, 0 = unlimited)
api_calls_limit (integer, 0 = unlimited)
modules (json, array of module names)
is_active (boolean)
trial_days (integer)
sort_order (integer)
created_at, updated_at, deleted_at
```

**4. subscriptions**
```sql
id (UUID, PK)
organization_id (UUID, FK -> organizations.id)
plan_id (UUID, FK -> plans.id)
status (enum: active, cancelled, expired, suspended)
starts_at (timestamp)
ends_at (timestamp, nullable)
trial_ends_at (timestamp, nullable)
cancelled_at (timestamp, nullable)
auto_renew (boolean)
price (decimal)
billing_period (string)
user_count (integer) -- current count
sub_org_count (integer)
storage_used (bigint, bytes)
hierarchy_depth (integer)
metadata (json)
add_ons (json, deprecated)
created_at, updated_at, deleted_at
```

**5. addons**
```sql
id (UUID, PK)
organization_id (UUID, FK -> organizations.id)
name (string)
slug (string, unique)
description (text)
type (string: storage, user, sub_org, module, api_calls)
price (decimal)
value (integer, quantity provided)
unit (string, nullable: GB, users, etc.)
is_active (boolean)
metadata (json)
created_at, updated_at, deleted_at
```

**6. subscription_addon** (Pivot Table)
```sql
subscription_id (UUID, FK -> subscriptions.id)
addon_id (UUID, FK -> addons.id)
quantity (integer)
price (decimal)
attached_at (timestamp)
created_at, updated_at
PRIMARY KEY (subscription_id, addon_id)
```

**7. resource_usage_logs**
```sql
id (UUID, PK)
subscription_id (UUID, FK -> subscriptions.id)
tenant_organization_id (UUID, FK -> organizations.id)
resource_type (enum: users, sub_orgs, storage, hierarchy_depth, api_calls)
usage_value (integer/bigint)
limit_value (integer/bigint)
usage_percentage (decimal)
alert_level (enum: normal, warning, critical, exceeded)
alert_sent (boolean)
recorded_at (timestamp)
created_at, updated_at
```

**8. approval_requests**
```sql
id (UUID, PK)
organization_id (UUID, FK -> organizations.id)
requester_id (UUID, FK -> users.id)
type (string: registration, sub_org_creation, etc.)
reference_type (string, nullable)
reference_id (UUID, nullable)
status (enum: pending, approved, rejected, escalated)
description (text)
amount (decimal, nullable)
data (json)
current_step (integer)
submitted_at (timestamp)
completed_at (timestamp, nullable)
created_at, updated_at, deleted_at
```

**9. approval_steps**
```sql
id (UUID, PK)
organization_id (UUID, FK -> organizations.id)
approval_request_id (UUID, FK -> approval_requests.id)
step_number (integer)
approver_id (UUID, FK -> users.id, nullable)
approver_role (string, nullable)
status (enum: pending, approved, rejected)
approved_by (UUID, FK -> users.id, nullable)
rejected_by (UUID, FK -> users.id, nullable)
approved_at (timestamp, nullable)
rejected_at (timestamp, nullable)
required_count (integer)
approval_type (enum: sequential, parallel)
created_at, updated_at
```

**10. approval_comments**
```sql
id (UUID, PK)
organization_id (UUID, FK -> organizations.id)
approval_request_id (UUID, FK -> approval_requests.id)
user_id (UUID, FK -> users.id)
comment (text)
type (enum: comment, approval, rejection, escalation)
created_at
```

**11. audit_logs**
```sql
id (UUID, PK)
organization_id (UUID, FK -> organizations.id)
user_id (UUID, FK -> users.id, nullable)
action (string)
auditable_type (string)
auditable_id (UUID)
old_values (json)
new_values (json)
ip_address (string)
user_agent (text)
created_at
```

**12. notifications**
```sql
id (UUID, PK)
organization_id (UUID, FK -> organizations.id)
user_id (UUID, FK -> users.id)
type (string)
title (string)
message (text)
data (json)
priority (enum: low, medium, high, critical)
read_at (timestamp, nullable)
created_at, updated_at
```

### Entity Relationship Diagram (ERD)

```
[Portal Owner Organization]
         │
         ├──[Plan 1, Plan 2, Plan N] (1:N)
         │
         └──[Tenant Organization 1]
                 │
                 ├──[Subscription] (1:1)
                 │       │
                 │       ├──[Addon 1, Addon 2] (N:M via subscription_addon)
                 │       └──[ResourceUsageLog entries] (1:N)
                 │
                 ├──[Users] (1:N)
                 │       └──[ApprovalRequest] (1:N as requester)
                 │
                 ├──[Sub-Organization 1]
                 │       ├──[Users] (1:N)
                 │       └──[Sub-Sub-Organization 1]
                 │               └──[Users] (1:N)
                 │
                 └──[ApprovalRequest] (1:N)
                         └──[ApprovalSteps] (1:N)
                                 └──[ApprovalComments] (1:N)
```

### Indexing Strategy

**Critical Indexes:**
- organizations: (parent_id), (type), (slug)
- users: (organization_id), (email), (status)
- subscriptions: (organization_id), (plan_id), (status), (starts_at), (ends_at)
- resource_usage_logs: (subscription_id, recorded_at), (tenant_organization_id), (alert_level)
- approval_requests: (organization_id), (requester_id), (status), (type)
- audit_logs: (organization_id), (user_id), (created_at)

**Composite Indexes:**
- subscriptions: (organization_id, status)
- resource_usage_logs: (subscription_id, resource_type, recorded_at)
- users: (organization_id, status)

---

## 06_MULTI_TENANCY_SYSTEM.md - Template

### Overview
- Shared Database with Row-Level Tenancy
- Complete data isolation via organization_id filtering
- Global query scopes on all models

### Organization Hierarchy

```
Level 0: Portal Owner
    ↓
Level 1: Tenant Organizations (Customers)
    ↓
Level 2+: Sub-Organizations (Departments/Branches)
```

### Tenant Isolation Mechanisms

1. **Database Level**
   - organization_id column on all tables
   - Foreign key constraints
   - Check constraints (future)

2. **Application Level**
   - Global query scopes (HasOrganizationId trait)
   - Middleware validation (OrganizationContextMiddleware)
   - Service layer validation

3. **Testing Requirements**
   - Cross-tenant access tests
   - Data leakage prevention tests
   - Context switching tests

### Hierarchy Rules

- Maximum depth: Configurable per plan
- Parent validation: Must belong to same tenant
- Deletion: Cascade or prevent based on children
- Moving: Requires approval

---

## 07_BILLING_SUBSCRIPTION_SYSTEM.md - Template

### System Overview
✅ **STATUS: FULLY IMPLEMENTED AND TESTED**

Comprehensive billing system with:
- Plan management
- Subscription lifecycle
- Add-on marketplace
- Resource tracking
- Usage alerts

### Core Components

#### 1. Plans
- Defines resource limits
- Pricing structure
- Module access
- Billing periods (monthly/yearly)

#### 2. Subscriptions
- One per tenant organization
- Tracks current usage
- Manages lifecycle states
- Links to add-ons

#### 3. Add-ons
- Catalog of purchasable enhancements
- Types: users, storage, sub-orgs, modules, API calls
- Immediate activation
- Prorated billing

#### 4. Resource Usage Tracking
- Daily automated logging (LogResourceUsageJob)
- Alert thresholds: 75%, 90%, 100%
- Historical data: 90-day retention
- Cleanup job: Weekly

### Services Implementation

**BillingService (18 tests, 100% passing)**
- purchaseAddOn() - Add-on purchase with proration
- cancelAddOn() - Remove add-on
- calculateProration() - Mid-period calculations
- calculateMonthlyTotal() - Total cost calculation
- upgradeSubscription() - Plan upgrade
- downgradeSubscription() - Plan downgrade
- cancelSubscription() - Cancel with options

**UsageTrackingService (19 tests, 100% passing)**
- logResourceUsage() - Create usage snapshot
- getCurrentUsage() - Real-time usage
- getUsageHistory() - Historical data
- getUsageAlerts() - Active alerts
- cleanupOldLogs() - Remove old data

**ResourceLimitService (28 tests, 100% passing)**
- checkUserLimit() - Validate user count
- checkSubOrgLimit() - Validate sub-org count
- checkStorageLimit() - Validate storage
- incrementCounter() - Update counters
- enforceLimit() - Block if exceeded

### Business Rules

**Proration Formula:**
```
Daily Rate = Monthly Price / Days in Month
Days Remaining = Period End - Current Date
Prorated Amount = Daily Rate × Days Remaining
```

**Upgrade Process:**
1. Calculate credit for unused current plan
2. Calculate charge for new plan (remaining period)
3. Net amount = New charge - Credit
4. Apply immediately or schedule

**Downgrade Process:**
1. Check if current usage exceeds new limits
2. If exceeds: Block and require compliance
3. If under: Schedule for next period
4. No refund for current period

### API Endpoints (8 endpoints, 13 tests, 100% passing)

```
POST   /subscriptions/{id}/addons
DELETE /subscriptions/{id}/addons/{addonId}
POST   /subscriptions/{id}/upgrade-plan
POST   /subscriptions/{id}/downgrade-plan
POST   /subscriptions/{id}/cancel-subscription
POST   /subscriptions/{id}/calculate-upgrade-price
GET    /organizations/{id}/usage
GET    /organizations/{id}/usage/alerts
GET    /subscriptions/{id}/usage/history
```

### Scheduled Jobs

**LogResourceUsageJob**
- Schedule: Daily at 00:00 IST
- Purpose: Log all active subscriptions' usage
- Creates: resource_usage_logs entries
- Triggers: Alerts if thresholds exceeded
- Tests: 10 passing

**CleanupOldUsageLogsJob**
- Schedule: Weekly, Sundays at 02:00 IST  
- Purpose: Delete logs older than 90 days
- Configurable: daysToKeep parameter
- Tests: Included in ScheduledJobsTest

### Alert System

**Threshold Levels:**
- 75% - Warning (email to admins)
- 90% - Critical (email + dashboard banner)
- 100% - Exceeded (block + upgrade prompt)

**Alert Actions:**
- Email notification
- In-app notification
- Dashboard banner
- Block resource creation (at 100%)

---

## 08_APPROVAL_WORKFLOW_SYSTEM.md - Template

### Overview
✅ **STATUS: FULLY IMPLEMENTED AND TESTED**

### Approval Types
1. User Registration
2. Sub-Organization Creation
3. Resource Limit Exceptions
4. Custom workflows (extensible)

### Workflow Components

**ApprovalRequest**
- Central workflow entity
- Tracks overall status
- Links to requester
- Contains request data

**ApprovalStep**
- Individual approval stages
- Sequential or parallel
- Role-based or user-specific
- Multi-approver support

**ApprovalComment**
- Audit trail
- Approval reasons
- Rejection justifications
- General comments

### Service Implementation

**ApprovalService (Enhanced - 9 tests)**
- createApprovalRequest()
- approveRequest() - ✅ WITH RESOURCE LIMIT CHECK
- rejectRequest()
- escalateRequest()
- getApprovalsByUser()
- getPendingApprovals()

**Key Enhancement:**
```php
approveRequest() {
    // BEFORE approval
    if (approval.type === 'registration') {
        checkResourceLimitsForRegistration();
        // Throws exception if limits exceeded
    }
    
    // Process approval
}
```

### Approval Flow

```
1. Request Created (status: pending)
   ↓
2. Assign to Approvers (step 1)
   ↓
3. Approver Reviews
   ├─→ Approve → Move to next step or complete
   ├─→ Reject → Close request
   └─→ Escalate → Notify higher authority
   ↓
4. All Steps Complete
   ↓
5. Execute Action (create user, sub-org, etc.)
   ↓
6. Notify Requester
```

### Integration with Resource Limits

**User Registration:**
```
1. Check: current_user_count < plan.user_limit?
2. If NO: Reject with upgrade message
3. If YES: Proceed with approval
4. On final approval: Create user + increment counter
```

**Sub-Org Creation:**
```
1. Check: current_sub_org_count < plan.sub_org_limit?
2. Check: new_hierarchy_depth <= plan.hierarchy_depth_limit?
3. If either fails: Reject
4. If pass: Proceed with approval
5. On final approval: Create sub-org + increment counter
```

---

## 09_RESOURCE_LIMIT_MANAGEMENT.md - Template

### Overview
✅ **STATUS: FULLY IMPLEMENTED AND TESTED**

### Resource Types

**1. Users (Hard Limit)**
- Count: Total across tenant + all sub-orgs
- Enforcement: Block registration at limit
- Alert: Not applicable (hard block)
- Add-on: "Additional Users" available

**2. Sub-Organizations (Hard Limit)**
- Count: All sub-orgs in hierarchy
- Enforcement: Block creation at limit
- Alert: Not applicable (hard block)
- Add-on: "Additional Sub-Organizations"

**3. Storage (Soft Limit with Alerts)**
- Count: Total bytes across organization
- Enforcement: Alerts at 75%, 90%, block at 100%
- Alert: Email + dashboard notifications
- Add-on: "Additional Storage" (per GB)

**4. Hierarchy Depth (Hard Limit)**
- Count: Maximum tree depth
- Enforcement: Block deeper nesting
- Alert: Not applicable
- Add-on: "Additional Hierarchy Level"

**5. API Calls (Soft Limit with Throttling)**
- Count: Requests per day
- Enforcement: Rate limiting middleware
- Alert: Dashboard warning
- Add-on: "Additional API Calls" (per 1000)

**6. Modules (Feature Flag)**
- Count: Enabled modules in plan
- Enforcement: UI and API restrictions
- Alert: Not applicable
- Add-on: "Additional Module"

### Service Implementation

**ResourceLimitService (28 tests)**

**Methods:**
- checkUserLimit() - Returns boolean + details
- checkSubOrgLimit()
- checkStorageLimit()
- checkHierarchyDepthLimit()
- checkModuleAccess()
- incrementUserCounter()
- incrementSubOrgCounter()
- updateStorageUsage()
- getResourceSummary()

**Response Format:**
```json
{
  "within_limit": false,
  "resource_type": "users",
  "current": 10,
  "limit": 10,
  "percentage": 100,
  "plan_name": "Basic Plan",
  "suggestions": [
    "Upgrade to Professional Plan (50 users)",
    "Purchase 'Additional Users' add-on"
  ]
}
```

### Counter Management

**Incrementing:**
```php
incrementUserCounter() {
    1. Validate subscription active
    2. Lock subscription record (pessimistic)
    3. Increment user_count
    4. Check if threshold crossed
    5. If crossed: Trigger alert
    6. Release lock
}
```

**Decrementing:**
```php
decrementUserCounter() {
    1. Lock subscription record
    2. Decrement user_count (min: 0)
    3. Update database
    4. Release lock
    5. Clear any exceeded alerts
}
```

### Integration Points

**Before User Creation:**
```php
ResourceLimitService::checkUserLimit()
    → If exceeded: throw ResourceLimitException
    → If OK: Create user
    → Call incrementUserCounter()
```

**Before Sub-Org Creation:**
```php
ResourceLimitService::checkSubOrgLimit()
ResourceLimitService::checkHierarchyDepthLimit()
    → If either exceeded: throw exception
    → If OK: Create sub-org
    → Call incrementSubOrgCounter()
```

**On File Upload:**
```php
ResourceLimitService::checkStorageLimit($fileSize)
    → If would exceed: Block upload
    → If OK: Save file
    → Call updateStorageUsage($fileSize)
```

---

## 10_API_STRUCTURE.md - Template

### API Versioning
- Base URL: `/api/v1`
- Version in URL path
- Backward compatibility maintained

### Authentication
- Method: JWT Bearer tokens
- Header: `Authorization: Bearer {token}`
- Expiration: 24 hours
- Refresh: Token refresh endpoint

### Request Format
```json
POST /api/v1/subscriptions/{id}/addons
Headers:
  Authorization: Bearer eyJ0eXAiOiJKV...
  Content-Type: application/json
  Accept: application/json
  
Body:
{
  "addon_id": "uuid-here",
  "quantity": 2
}
```

### Response Format
```json
{
  "success": true,
  "data": {
    // Resource data
  },
  "meta": {
    "timestamp": "2025-10-30T06:21:52Z",
    "version": "1.0"
  }
}
```

### Error Response
```json
{
  "success": false,
  "error": {
    "code": "RESOURCE_LIMIT_EXCEEDED",
    "message": "User-friendly message",
    "details": {},
    "suggestions": []
  },
  "timestamp": "2025-10-30T06:21:52Z"
}
```

### Rate Limiting
- Per organization basis
- Based on plan limits
- Headers included:
  - X-RateLimit-Limit
  - X-RateLimit-Remaining
  - X-RateLimit-Reset

### Pagination
```json
GET /api/v1/users?page=2&per_page=20

Response:
{
  "success": true,
  "data": [...],
  "meta": {
    "current_page": 2,
    "per_page": 20,
    "total": 100,
    "last_page": 5
  },
  "links": {
    "first": "?page=1",
    "prev": "?page=1",
    "next": "?page=3",
    "last": "?page=5"
  }
}
```

### Endpoint Categories

**Billing Endpoints (8 implemented)**
- See 07_BILLING_SUBSCRIPTION_SYSTEM.md

**Organization Endpoints**
- GET /organizations
- POST /organizations
- GET /organizations/{id}
- PATCH /organizations/{id}
- GET /organizations/{id}/hierarchy

**User Endpoints**
- GET /users
- POST /users (with approval)
- GET /users/{id}
- PATCH /users/{id}

**Approval Endpoints**
- GET /approvals
- POST /approvals
- POST /approvals/{id}/approve
- POST /approvals/{id}/reject

---

## 11_SCHEDULED_JOBS.md - Template

### Overview
✅ **STATUS: FULLY IMPLEMENTED AND TESTED**

### Implemented Jobs

**1. LogResourceUsageJob**
- **Schedule:** Daily at 00:00 IST
- **Purpose:** Log usage for all active subscriptions
- **Process:**
  1. Query all active subscriptions
  2. For each subscription:
     - Count users
     - Count sub-organizations
     - Sum storage usage
     - Calculate hierarchy depth
  3. Create resource_usage_logs entry
  4. Check alert thresholds
  5. Send alerts if needed
- **Duration:** ~1-5 minutes (depends on tenant count)
- **Monitoring:** Log success/failure counts
- **Tests:** 10 passing tests

**2. CleanupOldUsageLogsJob**
- **Schedule:** Weekly, Sundays at 02:00 IST
- **Purpose:** Delete logs older than 90 days
- **Process:**
  1. Calculate cutoff date (now - 90 days)
  2. Delete records where recorded_at < cutoff
  3. Log deletion count
- **Duration:** ~30 seconds to 2 minutes
- **Configurable:** daysToKeep parameter
- **Tests:** Included in ScheduledJobsTest

### Job Configuration

**Location:** `routes/console.php`

```php
Schedule::job(new LogResourceUsageJob())
    ->dailyAt('00:00')
    ->withoutOverlapping()  // Prevent concurrent runs
    ->onOneServer();        // Only one server executes

Schedule::job(new CleanupOldUsageLogsJob(90))
    ->weekly()
    ->sundays()
    ->at('02:00')
    ->withoutOverlapping()
    ->onOneServer();
```

### Monitoring

**Key Metrics:**
- Job execution time
- Success/failure rate
- Records processed
- Errors encountered

**Alerts:**
- Job failure: Critical alert
- Execution time > 10 minutes: Warning
- No execution in 25 hours: Critical

### Future Jobs (Planned)

- **SendPendingNotificationsJob** - Batch notification delivery
- **GenerateDailyReportsJob** - Automated reports
- **SyncExternalDataJob** - Third-party sync
- **CheckExpiredSubscriptionsJob** - Handle expirations
- **SendRenewalRemindersJob** - Subscription reminders

---

## 12_AUTHENTICATION_AUTHORIZATION.md - Template

### Authentication

**Method:** JWT (JSON Web Tokens)

**Login Flow:**
```
1. User submits credentials
2. Validate email + password
3. Generate JWT token
   - Payload: user_id, organization_id, roles
   - Expiration: 24 hours
   - Secret: env(JWT_SECRET)
4. Return token to client
5. Client stores token (localStorage/cookies)
6. Include in all subsequent requests
```

**Token Structure:**
```json
{
  "user_id": "uuid",
  "organization_id": "uuid",
  "roles": ["admin"],
  "exp": 1730275200,
  "iat": 1730188800
}
```

### Authorization

**Levels:**
1. **Route Level:** Middleware checks
2. **Resource Level:** Ownership validation  
3. **Permission Level:** Role-based access
4. **Subscription Level:** Plan feature access

**Middleware Stack:**
```
auth -> organization.context -> permission.check -> resource.limit
```

### Roles (Future Implementation)

**Portal Owner:**
- Full system access
- Create/manage plans
- View all tenants
- System configuration

**Tenant Admin:**
- Manage their organization
- Create sub-organizations (within limits)
- Manage users (within limits)
- View billing and usage

**Tenant User:**
- Access assigned modules
- View their own data
- Submit approval requests

**Sub-Org Admin:**
- Manage their sub-organization
- Manage users in their scope
- Limited to parent permissions

### Permission System (Future)

**Structure:**
```
permissions table:
- id
- name (e.g., "users.create")
- display_name
- module

roles table:
- id
- organization_id
- name
- is_system_role

role_permissions (pivot):
- role_id
- permission_id

user_roles (pivot):
- user_id
- role_id
```

---

## 13_TESTING_STRATEGY.md - Template

### Overview
✅ **STATUS: 97 TESTS, 264 ASSERTIONS, 100% PASSING**

### Test Pyramid

```
         E2E Tests (0)
       ──────────────
      Feature Tests (13)
     ────────────────────
    Unit Tests (84)
   ──────────────────────
```

### Unit Tests (84 tests, 208 assertions)

**BillingServiceTest (18 tests)**
- Add-on purchase scenarios
- Proration calculations
- Price calculations
- Cancellation logic
- Monthly total calculations
- Invalid add-on handling

**UsageTrackingServiceTest (19 tests)**
- Usage logging
- Alert generation
- Threshold detection
- Historical queries
- Trend calculations
- Cleanup operations

**ResourceLimitServiceTest (28 tests)**
- User limit checking
- Sub-org limit checking
- Storage limit checking
- Counter increments/decrements
- Approval triggers
- Summary generation

**ApprovalServiceTest (9 tests)**
- Approval with limit checks
- Registration approval
- Sub-org approval
- Rejection scenarios
- Unlimited plan handling
- Boundary conditions

**ScheduledJobsTest (10 tests)**
- Job dispatching
- LogResourceUsageJob execution
- CleanupOldUsageLogsJob execution
- Multiple subscription handling
- Inactive subscription skipping

### Feature Tests (13 tests, 56 assertions)

**BillingAPITest (13 tests)**
- Add addon endpoint
- Remove addon endpoint
- Upgrade subscription endpoint
- Downgrade subscription endpoint
- Cancel subscription (immediate)
- Cancel subscription (scheduled)
- Get current usage
- Get usage alerts
- Get usage history
- Calculate upgrade price
- Validation tests
- Authentication tests

### Test Coverage Goals

- **Critical Paths:** 100% coverage required
- **Business Logic:** 90%+ coverage
- **API Endpoints:** 100% endpoint coverage
- **Scheduled Jobs:** 100% coverage
- **Overall Target:** 80%+

### Testing Best Practices

1. **AAA Pattern:** Arrange, Act, Assert
2. **One Assertion Per Concept:** Test one thing
3. **Descriptive Names:** Test method names explain scenario
4. **Test Data Factories:** Use factories for models
5. **Database Transactions:** Rollback after each test
6. **Mock External Services:** Don't hit real APIs

### Running Tests

```bash
# All tests
php artisan test

# Specific suite
php artisan test --testsuite=Unit
php artisan test --testsuite=Feature

# Specific file
php artisan test tests/Unit/Billing/BillingServiceTest.php

# With coverage
php artisan test --coverage

# Parallel execution
php artisan test --parallel
```

### Continuous Integration

**Pre-commit:**
- Run unit tests
- Code formatting check
- Static analysis (PHPStan)

**Pull Request:**
- Full test suite
- Coverage report
- Code quality checks

**Pre-deployment:**
- All tests must pass
- Coverage threshold met
- Manual QA sign-off

---

## 14_DEPLOYMENT_GUIDE.md - Template

### Prerequisites

- PHP 8.2+
- MySQL 8.0+ or SQLite 3
- Redis 6.0+
- Composer 2.x
- Node.js 18+ (for asset compilation)

### Environment Setup

**1. Clone Repository**
```bash
git clone [repository-url]
cd backend
```

**2. Install Dependencies**
```bash
composer install --no-dev --optimize-autoloader
npm install
npm run build
```

**3. Environment Configuration**
```bash
cp .env.example .env
php artisan key:generate
```

**4. Configure .env**
```
APP_ENV=production
APP_DEBUG=false
APP_URL=https://your-domain.com

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_DATABASE=erp_production
DB_USERNAME=root
DB_PASSWORD=secure_password

CACHE_DRIVER=redis
QUEUE_CONNECTION=redis
SESSION_DRIVER=redis

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
```

**5. Run Migrations**
```bash
php artisan migrate --force
php artisan db:seed --class=InitialDataSeeder
```

**6. Optimize**
```bash
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan event:cache
```

**7. Start Services**
```bash
# Queue Worker
php artisan queue:work --tries=3

# Scheduler (add to crontab)
* * * * * cd /path-to-project && php artisan schedule:run >> /dev/null 2>&1
```

### Server Requirements

**Web Server:** Nginx or Apache
**PHP Extensions:**
- OpenSSL
- PDO
- Mbstring
- Tokenizer
- XML
- Ctype
- JSON
- BCMath
- Redis

### Deployment Checklist

- [ ] Pull latest code
- [ ] Run composer install
- [ ] Run migrations
- [ ] Clear and cache configs
- [ ] Restart queue workers
- [ ] Test critical endpoints
- [ ] Monitor error logs
- [ ] Verify scheduled jobs

### Zero-Downtime Deployment

1. Deploy to staging server
2. Run smoke tests
3. Put app in maintenance mode (optional)
4. Deploy to production
5. Run migrations
6. Clear caches
7. Restart workers
8. Bring app back online
9. Monitor for 30 minutes

### Rollback Procedure

```bash
git revert [commit-hash]
php artisan migrate:rollback
php artisan config:clear
php artisan cache:clear
service supervisor restart
```

---

## 15_FUTURE_ENHANCEMENTS.md - Template

### Planned Features

**Phase 1 (Q1 2026)**
- Advanced Role & Permission System
- Custom Approval Workflows
- Email Templates Customization
- Bulk User Import

**Phase 2 (Q2 2026)**
- Accounting Module
- Inventory Module
- Advanced Reporting
- API Rate Limiting Dashboard

**Phase 3 (Q3 2026)**
- Mobile App
- WhatsApp Integration
- Advanced Analytics
- Multi-language Support (if going global)

**Phase 4 (Q4 2026)**
- Accounting Integration (Tally, etc.)
- Payment Gateway (if policy changes)
- Advanced Security Features
- AI-powered Insights

### Technical Debt

- Migrate from array returns to DTOs
- Implement CQRS pattern
- Add Event Sourcing for audit
- Microservices extraction (if needed)
- GraphQL API (in addition to REST)

### Scalability Enhancements

- Database sharding
- Read replicas
- CDN integration
- Advanced caching
- Horizontal pod autoscaling

---

*End of Documentation Templates*

All documentation structure is now complete. Each template should be expanded with specific implementation details, diagrams, and examples as the system evolves.
