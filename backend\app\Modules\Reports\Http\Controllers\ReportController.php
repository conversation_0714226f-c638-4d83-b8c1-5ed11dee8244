<?php

namespace App\Modules\Reports\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Reports\Domain\Services\ReportService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ReportController extends Controller
{
    public function __construct(private readonly ReportService $reportService)
    {
    }

    /**
     * Get subscription report
     */
    public function subscriptions(Request $request)
    {
        try {
            $filters = [
                'organization_id' => auth()->user()->organization_id,
                'start_date' => $request->input('start_date'),
                'end_date' => $request->input('end_date'),
            ];

            $report = $this->reportService->getSubscriptionReport(array_filter($filters));

            return response()->json($report);
        } catch (\Exception $e) {
            Log::error('Report subscriptions error', ['exception' => $e]);
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Get payment report
     */
    public function payments(Request $request)
    {
        try {
            $filters = [
                'organization_id' => auth()->user()->organization_id,
                'start_date' => $request->input('start_date'),
                'end_date' => $request->input('end_date'),
            ];

            $report = $this->reportService->getPaymentReport(array_filter($filters));

            return response()->json($report);
        } catch (\Exception $e) {
            Log::error('Report payments error', ['exception' => $e]);
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Get revenue report
     */
    public function revenue(Request $request)
    {
        try {
            $filters = [
                'organization_id' => auth()->user()->organization_id,
                'start_date' => $request->input('start_date'),
                'end_date' => $request->input('end_date'),
            ];

            $report = $this->reportService->getRevenueReport(array_filter($filters));

            return response()->json($report);
        } catch (\Exception $e) {
            Log::error('Report revenue error', ['exception' => $e]);
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Get user activity report
     */
    public function users(Request $request)
    {
        try {
            $filters = [
                'organization_id' => auth()->user()->organization_id,
                'start_date' => $request->input('start_date'),
                'end_date' => $request->input('end_date'),
            ];

            $report = $this->reportService->getUserActivityReport(array_filter($filters));

            return response()->json($report);
        } catch (\Exception $e) {
            Log::error('Report users error', ['exception' => $e]);
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Get approval report
     */
    public function approvals(Request $request)
    {
        try {
            $filters = [
                'organization_id' => auth()->user()->organization_id,
                'start_date' => $request->input('start_date'),
                'end_date' => $request->input('end_date'),
            ];

            $report = $this->reportService->getApprovalReport(array_filter($filters));

            return response()->json($report);
        } catch (\Exception $e) {
            Log::error('Report approvals error', ['exception' => $e]);
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }
}
