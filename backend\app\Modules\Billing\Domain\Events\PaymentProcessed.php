<?php

namespace App\Modules\Billing\Domain\Events;

use App\Modules\Shared\Domain\Events\DomainEvent;

class PaymentProcessed extends DomainEvent
{
    public function __construct(public readonly string $paymentId, ?string $organizationId = null, ?string $userId = null, array $payload = [])
    {
        parent::__construct($organizationId, $userId, array_merge($payload, ['payment_id' => $paymentId]));
    }
}
