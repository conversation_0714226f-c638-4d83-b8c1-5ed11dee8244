<?php

namespace App\Modules\RolesPermissions\Domain\Services;

use App\Modules\Shared\Domain\Services\BaseService;
use App\Modules\RolesPermissions\Domain\Models\Permission;
use App\Modules\RolesPermissions\Domain\Models\Role;
use App\Modules\RolesPermissions\Domain\Models\UserPermission;
use App\Modules\Users\Domain\Models\User;
use Illuminate\Support\Facades\DB;

class AuthorizationService extends BaseService
{
    public function userHasRole(User $user, string $roleSlug): bool
    {
        $query = DB::table('role_assignments as ra')
            ->join('roles as r', 'r.id', '=', 'ra.role_id')
            ->where('ra.user_id', $user->id);
        
        // Portal Owner can have roles in any organization
        if (!$user->isPortalOwner()) {
            $query->where('ra.organization_id', $user->organization_id);
        }
        
        return $query->where(function ($q) {
                $q->whereNull('ra.expires_at')
                  ->orWhere('ra.expires_at', '>', now());
            })
            ->where('r.slug', $roleSlug)
            ->exists();
    }

    public function userHasPermission(User $user, string $permissionSlug): bool
    {
        return $this->directPermission($user, $permissionSlug) || $this->viaRolePermission($user, $permissionSlug);
    }

    public function getEffectivePermissions(User $user): array
    {
        $directQuery = DB::table('user_permissions as up')
            ->join('permissions as p', 'p.id', '=', 'up.permission_id')
            ->where('up.user_id', $user->id);
        
        if (!$user->isPortalOwner()) {
            $directQuery->where('up.organization_id', $user->organization_id);
        }
        
        $direct = $directQuery->where(function ($q) {
                $q->whereNull('up.expires_at')->orWhere('up.expires_at', '>', now());
            })
            ->pluck('p.slug')
            ->toArray();

        $viaRolesQuery = DB::table('role_assignments as ra')
            ->join('role_permissions as rp', 'rp.role_id', '=', 'ra.role_id')
            ->join('permissions as p', 'p.id', '=', 'rp.permission_id')
            ->where('ra.user_id', $user->id);
        
        if (!$user->isPortalOwner()) {
            $viaRolesQuery->where('ra.organization_id', $user->organization_id);
        }
        
        $viaRoles = $viaRolesQuery->pluck('p.slug')
            ->toArray();

        return array_values(array_unique(array_merge($direct, $viaRoles)));
    }

    public function checkPermissionAtLevel(User $user, string $permissionSlug, string $scope = 'organization', ?string $scopeId = null): bool
    {
        // For now, scopes are not restricting; extend to check scope-specific overrides
        return $this->userHasPermission($user, $permissionSlug);
    }

    private function directPermission(User $user, string $permissionSlug): bool
    {
        $query = DB::table('user_permissions as up')
            ->join('permissions as p', 'p.id', '=', 'up.permission_id')
            ->where('up.user_id', $user->id);
        
        if (!$user->isPortalOwner()) {
            $query->where('up.organization_id', $user->organization_id);
        }
        
        return $query->where(function ($q) {
                $q->whereNull('up.expires_at')->orWhere('up.expires_at', '>', now());
            })
            ->where('p.slug', $permissionSlug)
            ->exists();
    }

    private function viaRolePermission(User $user, string $permissionSlug): bool
    {
        $query = DB::table('role_assignments as ra')
            ->join('role_permissions as rp', 'rp.role_id', '=', 'ra.role_id')
            ->join('permissions as p', 'p.id', '=', 'rp.permission_id')
            ->where('ra.user_id', $user->id);
        
        if (!$user->isPortalOwner()) {
            $query->where('ra.organization_id', $user->organization_id);
        }
        
        return $query->where('p.slug', $permissionSlug)
            ->exists();
    }
}
