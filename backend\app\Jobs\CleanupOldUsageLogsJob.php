<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Modules\Billing\Application\Services\UsageTrackingService;
use Illuminate\Support\Facades\Log;

class CleanupOldUsageLogsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected int $daysToKeep;

    /**
     * Create a new job instance.
     */
    public function __construct(int $daysToKeep = 90)
    {
        $this->daysToKeep = $daysToKeep;
    }

    /**
     * Execute the job.
     */
    public function handle(UsageTrackingService $usageTrackingService): void
    {
        Log::info('Starting old usage logs cleanup job', [
            'days_to_keep' => $this->daysToKeep,
        ]);

        try {
            $deletedCount = $usageTrackingService->cleanupOldLogs($this->daysToKeep);

            Log::info('Old usage logs cleanup completed', [
                'deleted_count' => $deletedCount,
                'days_kept' => $this->daysToKeep,
            ]);
        } catch (\Exception $e) {
            Log::error('Old usage logs cleanup job failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('CleanupOldUsageLogsJob failed permanently', [
            'error' => $exception->getMessage(),
        ]);
    }
}
