import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import {
  CCard, CCardBody, CCardHeader, CCol, CRow, CButton, CTable, CTableBody,
  CTableDataCell, CTableHead, CTableHeaderCell, CTableRow, CSpinner, CAlert
} from '@coreui/react'
import CIcon from '@coreui/icons-react'
import { cilPlus, cilPencil, cilTrash } from '@coreui/icons'
import { rolesAPI } from '../../api/roles'

const Roles = () => {
  const navigate = useNavigate()
  const [roles, setRoles] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    loadRoles()
  }, [])

  const loadRoles = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await rolesAPI.getRoles()
      setRoles(response.data.data || [])
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to load roles')
      console.error('Error:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async (id) => {
    if (window.confirm('Are you sure?')) {
      try {
        await rolesAPI.deleteRole(id)
        setRoles(roles.filter((r) => r.id !== id))
      } catch (err) {
        setError(err.response?.data?.message || 'Failed to delete')
      }
    }
  }

  return (
    <CRow>
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardHeader>
            <div className="d-flex justify-content-between align-items-center">
              <strong>Roles & Permissions</strong>
              <CButton color="primary" size="sm" onClick={() => navigate('/roles/new')}>
                <CIcon icon={cilPlus} className="me-2" />
                Add Role
              </CButton>
            </div>
          </CCardHeader>
          <CCardBody>
            {error && <CAlert color="danger">{error}</CAlert>}
            {loading ? (
              <div className="text-center"><CSpinner color="primary" /></div>
            ) : (
              <CTable hover responsive>
                <CTableHead>
                  <CTableRow>
                    <CTableHeaderCell scope="col">#</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Role Name</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Description</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Permissions</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Actions</CTableHeaderCell>
                  </CTableRow>
                </CTableHead>
                <CTableBody>
                  {roles.length === 0 ? (
                    <CTableRow>
                      <CTableDataCell colSpan="5" className="text-center text-muted">
                        No roles found. Create a role to get started.
                      </CTableDataCell>
                    </CTableRow>
                  ) : (
                    roles.map((role, idx) => (
                      <CTableRow key={role.id}>
                        <CTableDataCell>{idx + 1}</CTableDataCell>
                        <CTableDataCell>{role.name}</CTableDataCell>
                        <CTableDataCell>{role.description || '-'}</CTableDataCell>
                        <CTableDataCell>{role.permissions_count || 0}</CTableDataCell>
                        <CTableDataCell>
                          <CButton color="info" size="sm" className="me-2"
                            onClick={() => navigate(`/roles/${role.id}`)}>
                            <CIcon icon={cilPencil} />
                          </CButton>
                          <CButton color="danger" size="sm" onClick={() => handleDelete(role.id)}>
                            <CIcon icon={cilTrash} />
                          </CButton>
                        </CTableDataCell>
                      </CTableRow>
                    ))
                  )}
                </CTableBody>
              </CTable>
            )}
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  )
}

export default Roles
