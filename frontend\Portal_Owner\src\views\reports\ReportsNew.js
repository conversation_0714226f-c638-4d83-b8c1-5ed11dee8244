import React, { useState, useEffect } from 'react'
import { CCard, CCardBody, CCardHeader, CCol, CRow, CNav, CNavItem, CNavLink, CTabContent, CTabPane, CSpinner, CAlert } from '@coreui/react'
import { reportsAPI } from '../../api/reports'

const Reports = () => {
  const [activeTab, setActiveTab] = useState('subscriptions')
  const [reports, setReports] = useState({})
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    loadReports()
  }, [])

  const loadReports = async () => {
    try {
      setLoading(true)
      setError(null)
      const [subs, payments, revenue, users, approvals] = await Promise.all([
        reportsAPI.getSubscriptionsReport(),
        reportsAPI.getPaymentsReport(),
        reportsAPI.getRevenueReport(),
        reportsAPI.getUsersReport(),
        reportsAPI.getApprovalsReport(),
      ])
      setReports({
        subscriptions: subs.data.data,
        payments: payments.data.data,
        revenue: revenue.data.data,
        users: users.data.data,
        approvals: approvals.data.data,
      })
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to load reports')
      console.error('Error:', err)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <CRow>
        <CCol xs={12} className="text-center">
          <CSpinner color="primary" />
        </CCol>
      </CRow>
    )
  }

  return (
    <CRow>
      {error && (
        <CCol xs={12}>
          <CAlert color="danger">{error}</CAlert>
        </CCol>
      )}
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardHeader>
            <strong>Reports</strong>
          </CCardHeader>
          <CCardBody>
            <CNav variant="tabs" role="tablist">
              <CNavItem role="presentation">
                <CNavLink
                  active={activeTab === 'subscriptions'}
                  href="#"
                  onClick={() => setActiveTab('subscriptions')}
                >
                  Subscriptions
                </CNavLink>
              </CNavItem>
              <CNavItem role="presentation">
                <CNavLink
                  active={activeTab === 'payments'}
                  href="#"
                  onClick={() => setActiveTab('payments')}
                >
                  Payments
                </CNavLink>
              </CNavItem>
              <CNavItem role="presentation">
                <CNavLink
                  active={activeTab === 'revenue'}
                  href="#"
                  onClick={() => setActiveTab('revenue')}
                >
                  Revenue
                </CNavLink>
              </CNavItem>
              <CNavItem role="presentation">
                <CNavLink
                  active={activeTab === 'users'}
                  href="#"
                  onClick={() => setActiveTab('users')}
                >
                  Users
                </CNavLink>
              </CNavItem>
              <CNavItem role="presentation">
                <CNavLink
                  active={activeTab === 'approvals'}
                  href="#"
                  onClick={() => setActiveTab('approvals')}
                >
                  Approvals
                </CNavLink>
              </CNavItem>
            </CNav>
            <CTabContent>
              <CTabPane role="tabpanel" aria-labelledby="subscriptions-tab" visible={activeTab === 'subscriptions'}>
                <div className="p-3">
                  {reports.subscriptions ? (
                    <pre>{JSON.stringify(reports.subscriptions, null, 2)}</pre>
                  ) : (
                    <div className="text-muted text-center">No data available</div>
                  )}
                </div>
              </CTabPane>
              <CTabPane role="tabpanel" aria-labelledby="payments-tab" visible={activeTab === 'payments'}>
                <div className="p-3">
                  {reports.payments ? (
                    <pre>{JSON.stringify(reports.payments, null, 2)}</pre>
                  ) : (
                    <div className="text-muted text-center">No data available</div>
                  )}
                </div>
              </CTabPane>
              <CTabPane role="tabpanel" aria-labelledby="revenue-tab" visible={activeTab === 'revenue'}>
                <div className="p-3">
                  {reports.revenue ? (
                    <pre>{JSON.stringify(reports.revenue, null, 2)}</pre>
                  ) : (
                    <div className="text-muted text-center">No data available</div>
                  )}
                </div>
              </CTabPane>
              <CTabPane role="tabpanel" aria-labelledby="users-tab" visible={activeTab === 'users'}>
                <div className="p-3">
                  {reports.users ? (
                    <pre>{JSON.stringify(reports.users, null, 2)}</pre>
                  ) : (
                    <div className="text-muted text-center">No data available</div>
                  )}
                </div>
              </CTabPane>
              <CTabPane role="tabpanel" aria-labelledby="approvals-tab" visible={activeTab === 'approvals'}>
                <div className="p-3">
                  {reports.approvals ? (
                    <pre>{JSON.stringify(reports.approvals, null, 2)}</pre>
                  ) : (
                    <div className="text-muted text-center">No data available</div>
                  )}
                </div>
              </CTabPane>
            </CTabContent>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  )
}

export default Reports
