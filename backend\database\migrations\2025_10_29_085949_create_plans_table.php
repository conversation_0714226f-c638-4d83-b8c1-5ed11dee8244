<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plans', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('organization_id')->comment('Owning organization');
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->decimal('price', 10, 2)->comment('Monthly price');
            $table->decimal('yearly_price', 10, 2)->nullable();
            $table->string('billing_period', 20)->default('monthly')->comment('monthly, yearly');
            $table->integer('user_limit')->default(0)->comment('0 = unlimited');
            $table->integer('storage_limit')->default(0)->comment('In GB, 0 = unlimited');
            $table->integer('sub_org_limit')->default(0)->comment('Number of sub-orgs, 0 = unlimited');
            $table->integer('hierarchy_depth_limit')->default(0)->comment('Max hierarchy depth, 0 = unlimited');
            $table->integer('api_calls_limit')->default(0)->comment('API calls per day, 0 = unlimited');
            $table->json('modules')->nullable()->comment('Array of enabled module names');
            $table->json('features')->nullable()->comment('Plan features list (deprecated, use modules)');
            $table->boolean('is_active')->default(true);
            $table->integer('trial_days')->default(0);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
            $table->softDeletes();
            
            $table->index('organization_id');
            $table->index('slug');
            $table->index('is_active');
            
            $table->foreign('organization_id')->references('id')->on('organizations')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plans');
    }
};
