<?php

namespace App\Modules\Billing\Domain\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Modules\Shared\Domain\Traits\HasUUID;

class SubscriptionAddOn extends Model
{
    use HasFactory, SoftDeletes, HasUUID;

    protected $table = 'subscription_add_ons';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'subscription_id',
        'add_on_type',
        'quantity',
        'unit_price',
        'total_price',
        'starts_at',
        'ends_at',
        'metadata',
    ];

    protected $casts = [
        'quantity' => 'integer',
        'unit_price' => 'decimal:2',
        'total_price' => 'decimal:2',
        'starts_at' => 'datetime',
        'ends_at' => 'datetime',
        'metadata' => 'array',
    ];

    // Relationships
    public function subscription()
    {
        return $this->belongsTo(Subscription::class, 'subscription_id');
    }

    // Helper methods
    public function isActive(): bool
    {
        return $this->starts_at <= now() && 
               (is_null($this->ends_at) || $this->ends_at > now());
    }

    public function calculateProrated(int $daysRemaining): float
    {
        $daysInMonth = now()->daysInMonth;
        return ($this->total_price / $daysInMonth) * $daysRemaining;
    }

    public function getAddOnName(): string
    {
        return match($this->add_on_type) {
            'user' => 'Additional Users',
            'sub_org' => 'Additional Sub-Organizations',
            'storage' => 'Additional Storage',
            'hierarchy_level' => 'Additional Hierarchy Levels',
            'module' => 'Additional Module',
            'api_calls' => 'Additional API Calls',
            default => 'Unknown Add-On',
        };
    }

    public function getAddOnDescription(): string
    {
        $name = $this->getAddOnName();
        $qty = $this->quantity;
        
        return match($this->add_on_type) {
            'user' => "{$qty} extra user" . ($qty > 1 ? 's' : ''),
            'sub_org' => "{$qty} extra sub-organization" . ($qty > 1 ? 's' : ''),
            'storage' => "{$qty} GB extra storage",
            'hierarchy_level' => "{$qty} extra hierarchy level" . ($qty > 1 ? 's' : ''),
            'module' => $this->metadata['module_name'] ?? 'Module',
            'api_calls' => "{$qty}x 50K extra API calls/day",
            default => $name,
        };
    }
}
