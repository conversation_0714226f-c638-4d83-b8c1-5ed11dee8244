import { createSlice } from '@reduxjs/toolkit'

const initialState = {
  users: { data: [], loading: false, error: null, pagination: {} },
  organizations: { data: [], loading: false, error: null, pagination: {} },
  roles: { data: [], loading: false, error: null, pagination: {} },
  permissions: { data: [], loading: false, error: null, pagination: {} },
  subscriptions: { data: [], loading: false, error: null, pagination: {} },
  payments: { data: [], loading: false, error: null, pagination: {} },
  invoices: { data: [], loading: false, error: null, pagination: {} },
  approvals: { data: [], loading: false, error: null, pagination: {} },
  notifications: { data: [], loading: false, error: null, pagination: {} },
  webhooks: { data: [], loading: false, error: null, pagination: {} },
  reports: { subscriptions: null, payments: null, revenue: null, users: null, approvals: null },
  settings: { data: {}, loading: false, error: null },
}

// Data actions
export const SET_DATA = 'SET_DATA'
export const SET_LOADING = 'SET_LOADING'
export const SET_ERROR = 'SET_ERROR'

// Data action creators
export const setData = (module, data) => ({
  type: SET_DATA,
  module,
  payload: data,
})

export const setLoading = (module) => ({
  type: SET_LOADING,
  module,
})

export const setError = (module, error) => ({
  type: SET_ERROR,
  module,
  payload: error,
})

const dataSlice = createSlice({
  name: 'data',
  initialState,
  reducers: {
    // Generic actions for list data
    setListLoading: (state, action) => {
      const { module } = action.payload
      if (state[module]) {
        state[module].loading = true
        state[module].error = null
      }
    },
    setListData: (state, action) => {
      const { module, data, pagination } = action.payload
      if (state[module]) {
        state[module].data = data
        state[module].pagination = pagination || {}
        state[module].loading = false
      }
    },
    setListError: (state, action) => {
      const { module, error } = action.payload
      if (state[module]) {
        state[module].error = error
        state[module].loading = false
      }
    },
    addItem: (state, action) => {
      const { module, item } = action.payload
      if (state[module]) {
        state[module].data.unshift(item)
      }
    },
    updateItem: (state, action) => {
      const { module, id, item } = action.payload
      if (state[module]) {
        const index = state[module].data.findIndex((i) => i.id === id)
        if (index !== -1) {
          state[module].data[index] = item
        }
      }
    },
    deleteItem: (state, action) => {
      const { module, id } = action.payload
      if (state[module]) {
        state[module].data = state[module].data.filter((i) => i.id !== id)
      }
    },
    // Reports
    setReportData: (state, action) => {
      const { reportType, data } = action.payload
      state.reports[reportType] = data
    },
    // Settings
    setSettingsLoading: (state) => {
      state.settings.loading = true
      state.settings.error = null
    },
    setSettingsData: (state, action) => {
      state.settings.data = action.payload
      state.settings.loading = false
    },
    setSettingsError: (state, action) => {
      state.settings.error = action.payload
      state.settings.loading = false
    },
  },
})

export const {
  setListLoading,
  setListData,
  setListError,
  addItem,
  updateItem,
  deleteItem,
  setReportData,
  setSettingsLoading,
  setSettingsData,
  setSettingsError,
} = dataSlice.actions

export default dataSlice.reducer
