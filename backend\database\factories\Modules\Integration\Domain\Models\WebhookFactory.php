<?php

namespace Database\Factories\Modules\Integration\Domain\Models;

use App\Modules\Integration\Domain\Models\Webhook;
use App\Modules\Organizations\Domain\Models\Organization;
use Illuminate\Database\Eloquent\Factories\Factory;

class WebhookFactory extends Factory
{
    protected $model = Webhook::class;

    public function definition(): array
    {
        return [
            'id' => $this->faker->uuid(),
            'organization_id' => Organization::factory(),
            'name' => 'Webhook-' . $this->faker->word(),
            'url' => $this->faker->url(),
            'events' => ['payment.completed', 'subscription.created'],
            'secret' => bin2hex(random_bytes(32)),
            'is_active' => true,
            'retry_count' => 3,
            'metadata' => [],
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}
