<?php

namespace App\Modules\Billing\Application\Services;

use App\Modules\Organizations\Domain\Models\Organization;
use App\Modules\Billing\Domain\Models\Subscription;
use App\Modules\Billing\Domain\Models\ResourceUsageLog;
use App\Modules\Notifications\Domain\Services\NotificationService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class UsageTrackingService
{
    public function __construct(
        protected NotificationService $notificationService
    ) {}

    /**
     * Log current resource usage for a subscription
     */
    public function logResourceUsage(Subscription $subscription): array
    {
        try {
            DB::beginTransaction();

            $plan = $subscription->plan;
            $logs = [];

            // Log user usage
            if ($plan->user_limit > 0) {
                $logs[] = $this->createUsageLog(
                    $subscription,
                    'users',
                    $subscription->user_count,
                    $plan->user_limit
                );
            }

            // Log sub-org usage
            if ($plan->sub_org_limit > 0) {
                $logs[] = $this->createUsageLog(
                    $subscription,
                    'sub_orgs',
                    $subscription->sub_org_count,
                    $plan->sub_org_limit
                );
            }

            // Log storage usage
            if ($plan->storage_limit > 0) {
                $storageLimitBytes = $plan->storage_limit * 1024 * 1024 * 1024;
                $logs[] = $this->createUsageLog(
                    $subscription,
                    'storage',
                    $subscription->storage_used,
                    $storageLimitBytes
                );
            }

            // Log hierarchy depth
            if ($plan->hierarchy_depth_limit > 0) {
                $logs[] = $this->createUsageLog(
                    $subscription,
                    'hierarchy_depth',
                    $subscription->hierarchy_depth,
                    $plan->hierarchy_depth_limit
                );
            }

            // Log API calls (would need tracking implementation)
            if ($plan->api_calls_limit > 0) {
                // This would require actual API call counting
                // For now, we'll skip or use a placeholder
            }

            Log::info('Resource usage logged', [
                'subscription_id' => $subscription->id,
                'organization_id' => $subscription->organization_id,
                'logs_count' => count($logs),
            ]);

            DB::commit();

            // Check for alerts after logging
            $this->checkAndSendAlerts($subscription, $logs);

            return [
                'success' => true,
                'logs' => $logs,
                'message' => 'Resource usage logged successfully.',
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Failed to log resource usage', [
                'subscription_id' => $subscription->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Failed to log resource usage: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Create a usage log entry
     */
    protected function createUsageLog(
        Subscription $subscription,
        string $resourceType,
        int $usageValue,
        int $limitValue
    ): ResourceUsageLog {
        $usagePercentage = $limitValue > 0 ? ($usageValue / $limitValue) * 100 : 0;
        $alertLevel = $this->calculateAlertLevel($usagePercentage);

        return ResourceUsageLog::create([
            'subscription_id' => $subscription->id,
            'tenant_organization_id' => $subscription->organization_id,
            'resource_type' => $resourceType,
            'usage_value' => $usageValue,
            'limit_value' => $limitValue,
            'usage_percentage' => round($usagePercentage, 2),
            'recorded_at' => now(),
            'alert_sent' => false,
            'alert_level' => $alertLevel,
        ]);
    }

    /**
     * Calculate alert level based on usage percentage
     */
    protected function calculateAlertLevel(float $percentage): string
    {
        if ($percentage >= 100) return 'exceeded';
        if ($percentage >= 90) return 'critical';
        if ($percentage >= 75) return 'warning';
        return 'normal';
    }

    /**
     * Check usage and send alerts if needed
     */
    public function checkAndSendAlerts(Subscription $subscription, array $logs): void
    {
        foreach ($logs as $log) {
            if ($log->shouldSendAlert()) {
                $this->sendUsageAlert($subscription, $log);
            }
        }
    }

    /**
     * Send usage alert notification
     */
    protected function sendUsageAlert(Subscription $subscription, ResourceUsageLog $log): void
    {
        try {
            $organization = $subscription->organization;
            
            // Get organization admins (would need proper role checking)
            $admins = $organization->users()
                ->where('status', 'active')
                ->limit(5) // Limit to avoid spam
                ->get();

            foreach ($admins as $admin) {
                $this->notificationService->create(
                    $admin->id,
                    'resource_usage_alert',
                    $log->getAlertMessage(),
                    [
                        'resource_type' => $log->resource_type,
                        'usage_percentage' => $log->usage_percentage,
                        'alert_level' => $log->alert_level,
                        'usage_value' => $log->usage_value,
                        'limit_value' => $log->limit_value,
                    ],
                    'high'
                );
            }

            $log->markAlertAsSent();

            Log::info('Usage alert sent', [
                'subscription_id' => $subscription->id,
                'resource_type' => $log->resource_type,
                'alert_level' => $log->alert_level,
                'recipients_count' => $admins->count(),
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send usage alert', [
                'log_id' => $log->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Get usage history for a subscription
     */
    public function getUsageHistory(
        Subscription $subscription,
        ?string $resourceType = null,
        ?Carbon $startDate = null,
        ?Carbon $endDate = null,
        int $limit = 100
    ): array {
        $query = ResourceUsageLog::where('subscription_id', $subscription->id);

        if ($resourceType) {
            $query->where('resource_type', $resourceType);
        }

        if ($startDate) {
            $query->where('recorded_at', '>=', $startDate);
        }

        if ($endDate) {
            $query->where('recorded_at', '<=', $endDate);
        }

        $logs = $query->orderBy('recorded_at', 'desc')
                     ->limit($limit)
                     ->get();

        return [
            'total' => $logs->count(),
            'resource_type' => $resourceType,
            'period' => [
                'start' => $startDate?->toDateString(),
                'end' => $endDate?->toDateString(),
            ],
            'logs' => $logs->map(function($log) {
                return [
                    'id' => $log->id,
                    'resource_type' => $log->resource_type,
                    'usage_value' => $log->usage_value,
                    'limit_value' => $log->limit_value,
                    'usage_percentage' => $log->usage_percentage,
                    'alert_level' => $log->alert_level,
                    'recorded_at' => $log->recorded_at->toDateTimeString(),
                ];
            })->toArray(),
        ];
    }

    /**
     * Get usage trends for a resource type
     */
    public function getUsageTrends(
        Subscription $subscription,
        string $resourceType,
        int $days = 30
    ): array {
        $startDate = now()->subDays($days);
        
        $logs = ResourceUsageLog::where('subscription_id', $subscription->id)
            ->where('resource_type', $resourceType)
            ->where('recorded_at', '>=', $startDate)
            ->orderBy('recorded_at', 'asc')
            ->get();

        $trend = $logs->map(function($log) {
            return [
                'date' => $log->recorded_at->toDateString(),
                'usage_value' => $log->usage_value,
                'usage_percentage' => $log->usage_percentage,
                'alert_level' => $log->alert_level,
            ];
        })->toArray();

        // Calculate trend direction
        $trendDirection = 'stable';
        if ($logs->count() >= 2) {
            $firstUsage = $logs->first()->usage_percentage;
            $lastUsage = $logs->last()->usage_percentage;
            $change = $lastUsage - $firstUsage;

            if ($change > 10) {
                $trendDirection = 'increasing';
            } elseif ($change < -10) {
                $trendDirection = 'decreasing';
            }
        }

        return [
            'resource_type' => $resourceType,
            'period_days' => $days,
            'data_points' => $logs->count(),
            'trend_direction' => $trendDirection,
            'current_usage' => $logs->last()?->usage_percentage ?? 0,
            'average_usage' => $logs->avg('usage_percentage'),
            'peak_usage' => $logs->max('usage_percentage'),
            'trend_data' => $trend,
        ];
    }

    /**
     * Get latest usage snapshot for subscription
     */
    public function getLatestUsageSnapshot(Subscription $subscription): array
    {
        $resourceTypes = ['users', 'sub_orgs', 'storage', 'hierarchy_depth', 'api_calls'];
        $snapshot = [];

        foreach ($resourceTypes as $type) {
            $latestLog = ResourceUsageLog::where('subscription_id', $subscription->id)
                ->where('resource_type', $type)
                ->orderBy('recorded_at', 'desc')
                ->first();

            if ($latestLog) {
                $snapshot[$type] = [
                    'usage_value' => $latestLog->usage_value,
                    'limit_value' => $latestLog->limit_value,
                    'usage_percentage' => $latestLog->usage_percentage,
                    'alert_level' => $latestLog->alert_level,
                    'last_recorded' => $latestLog->recorded_at->toDateTimeString(),
                ];
            }
        }

        return [
            'subscription_id' => $subscription->id,
            'organization_id' => $subscription->organization_id,
            'snapshot_at' => now()->toDateTimeString(),
            'resources' => $snapshot,
        ];
    }

    /**
     * Delete old usage logs
     */
    public function cleanupOldLogs(int $daysToKeep = 90): int
    {
        $cutoffDate = now()->subDays($daysToKeep);
        
        $deleted = ResourceUsageLog::where('recorded_at', '<', $cutoffDate)
            ->delete();

        Log::info('Old usage logs cleaned up', [
            'days_kept' => $daysToKeep,
            'deleted_count' => $deleted,
        ]);

        return $deleted;
    }

    /**
     * Get alert summary for organization
     */
    public function getAlertSummary(Organization $organization): array
    {
        $subscription = $organization->subscription;
        
        if (!$subscription) {
            return [
                'has_subscription' => false,
                'message' => 'No active subscription found.',
            ];
        }

        $recentLogs = ResourceUsageLog::where('subscription_id', $subscription->id)
            ->where('recorded_at', '>=', now()->subDays(7))
            ->get();

        $alertsByLevel = $recentLogs->groupBy('alert_level')
            ->map->count()
            ->toArray();

        $resourcesAtRisk = $recentLogs->filter(function($log) {
            return in_array($log->alert_level, ['warning', 'critical', 'exceeded']);
        })->map(function($log) {
            return [
                'resource_type' => $log->resource_type,
                'alert_level' => $log->alert_level,
                'usage_percentage' => $log->usage_percentage,
            ];
        })->unique('resource_type')->values()->toArray();

        return [
            'has_subscription' => true,
            'period' => 'last_7_days',
            'total_logs' => $recentLogs->count(),
            'alerts_by_level' => $alertsByLevel,
            'resources_at_risk' => $resourcesAtRisk,
            'risk_count' => count($resourcesAtRisk),
        ];
    }

    /**
     * Log all subscriptions usage (for scheduled task)
     */
    public function logAllActiveSubscriptions(): array
    {
        $subscriptions = Subscription::where('status', 'active')->get();
        $results = [
            'total' => $subscriptions->count(),
            'success' => 0,
            'failed' => 0,
            'errors' => [],
        ];

        foreach ($subscriptions as $subscription) {
            $result = $this->logResourceUsage($subscription);
            
            if ($result['success']) {
                $results['success']++;
            } else {
                $results['failed']++;
                $results['errors'][] = [
                    'subscription_id' => $subscription->id,
                    'error' => $result['message'],
                ];
            }
        }

        Log::info('All active subscriptions usage logged', $results);

        return $results;
    }

    // ==================================================================
    // API Controller Helper Methods
    // ==================================================================

    /**
     * Get current usage for organization (API-friendly method)
     */
    public function getCurrentUsage(string $organizationId): array
    {
        $organization = Organization::findOrFail($organizationId);
        $subscription = Subscription::where('organization_id', $organizationId)
            ->where('status', 'active')
            ->first();

        if (!$subscription) {
            return [
                'has_subscription' => false,
                'message' => 'No active subscription found',
            ];
        }

        $plan = $subscription->plan;

        return [
            'has_subscription' => true,
            'users' => [
                'current' => $subscription->user_count,
                'limit' => $plan->user_limit,
                'percentage' => $plan->user_limit > 0 ? ($subscription->user_count / $plan->user_limit) * 100 : 0,
            ],
            'sub_organizations' => [
                'current' => $subscription->sub_org_count,
                'limit' => $plan->sub_org_limit,
                'percentage' => $plan->sub_org_limit > 0 ? ($subscription->sub_org_count / $plan->sub_org_limit) * 100 : 0,
            ],
            'storage' => [
                'current' => $subscription->storage_used,
                'current_gb' => round($subscription->storage_used / (1024 * 1024 * 1024), 2),
                'limit' => $plan->storage_limit * 1024 * 1024 * 1024,
                'limit_gb' => $plan->storage_limit,
                'percentage' => $plan->storage_limit > 0 ? ($subscription->storage_used / ($plan->storage_limit * 1024 * 1024 * 1024)) * 100 : 0,
            ],
        ];
    }

    /**
     * Get usage alerts for organization (API-friendly method)
     */
    public function getUsageAlerts(string $organizationId): array
    {
        $organization = Organization::findOrFail($organizationId);
        $subscription = Subscription::where('organization_id', $organizationId)
            ->where('status', 'active')
            ->first();

        if (!$subscription) {
            return [
                'has_subscription' => false,
                'alerts' => [],
            ];
        }

        // Get recent logs with alerts
        $alertLogs = ResourceUsageLog::where('subscription_id', $subscription->id)
            ->whereIn('alert_level', ['warning', 'critical', 'exceeded'])
            ->where('recorded_at', '>=', now()->subDays(7))
            ->orderBy('recorded_at', 'desc')
            ->get();

        return [
            'has_subscription' => true,
            'total_alerts' => $alertLogs->count(),
            'alerts' => $alertLogs->map(function($log) {
                return [
                    'resource_type' => $log->resource_type,
                    'alert_level' => $log->alert_level,
                    'usage_percentage' => $log->usage_percentage,
                    'usage_value' => $log->usage_value,
                    'limit_value' => $log->limit_value,
                    'recorded_at' => $log->recorded_at->toDateTimeString(),
                    'message' => $log->getAlertMessage(),
                ];
            })->toArray(),
        ];
    }
}
