import client from './client'

export const billingAPI = {
  // Subscriptions CRUD
  getSubscriptions: (params) => client.get('/subscriptions', { params }),
  createSubscription: (data) => client.post('/subscriptions', data),
  getSubscription: (id) => client.get(`/subscriptions/${id}`),
  updateSubscription: (id, data) => client.patch(`/subscriptions/${id}`, data),
  deleteSubscription: (id) => client.delete(`/subscriptions/${id}`),
  
  // Subscription actions
  upgradeSubscription: (id, data) => client.post(`/subscriptions/${id}/upgrade`, data),
  downgradeSubscription: (id, data) => client.post(`/subscriptions/${id}/downgrade`, data),
  cancelSubscription: (id, data) => client.post(`/subscriptions/${id}/cancel`, data),
  
  // Enhanced subscription management
  addAddon: (subscriptionId, data) =>
    client.post(`/subscriptions/${subscriptionId}/addons`, data),
  removeAddon: (subscriptionId, addonId) =>
    client.delete(`/subscriptions/${subscriptionId}/addons/${addonId}`),
  upgradePlan: (subscriptionId, data) =>
    client.post(`/subscriptions/${subscriptionId}/upgrade-plan`, data),
  downgradePlan: (subscriptionId, data) =>
    client.post(`/subscriptions/${subscriptionId}/downgrade-plan`, data),
  calculateUpgradePrice: (subscriptionId, data) =>
    client.post(`/subscriptions/${subscriptionId}/calculate-upgrade-price`, data),
  getUsageHistory: (subscriptionId, params) =>
    client.get(`/subscriptions/${subscriptionId}/usage/history`, { params }),
  
  // Payments CRUD
  getPayments: (params) => client.get('/payments', { params }),
  createPayment: (data) => client.post('/payments', data),
  getPayment: (id) => client.get(`/payments/${id}`),
  retryPayment: (id, data) => client.post(`/payments/${id}/retry`, data),
  refundPayment: (id, data) => client.post(`/payments/${id}/refund`, data),
  paymentWebhook: (data) => client.post('/payments/webhook', data),
  
  // Invoices CRUD
  getInvoices: (params) => client.get('/invoices', { params }),
  createInvoice: (data) => client.post('/invoices', data),
  getInvoice: (id) => client.get(`/invoices/${id}`),
  getInvoicePdf: (id) => client.get(`/invoices/${id}/pdf`, { responseType: 'blob' }),
  sendInvoice: (id, data) => client.post(`/invoices/${id}/send`, data),
}
