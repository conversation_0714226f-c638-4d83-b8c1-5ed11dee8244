import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { CCard, CCardBody, CCardHeader, CCol, CRow, CButton, CTable, CTableBody, CTableDataCell, CTableHead, CTableHeaderCell, CTableRow, CSpinner, CAlert, CBadge } from '@coreui/react'
import CIcon from '@coreui/icons-react'
import { cilPlus, cilPencil, cilTrash } from '@coreui/icons'
import { billingAPI } from '../../api/billing'

const Subscriptions = () => {
  const navigate = useNavigate()
  const [subscriptions, setSubscriptions] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    loadSubscriptions()
  }, [])

  const loadSubscriptions = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await billingAPI.getSubscriptions()
      setSubscriptions(response.data.data || [])
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to load subscriptions')
      console.error('Error:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async (id) => {
    if (window.confirm('Are you sure?')) {
      try {
        await billingAPI.deleteSubscription(id)
        setSubscriptions(subscriptions.filter((s) => s.id !== id))
      } catch (err) {
        setError(err.response?.data?.message || 'Failed to delete')
      }
    }
  }

  return (
    <CRow>
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardHeader>
            <div className="d-flex justify-content-between align-items-center">
              <strong>Subscriptions</strong>
              <CButton color="primary" size="sm" onClick={() => navigate('/subscriptions/new')}>
                <CIcon icon={cilPlus} className="me-2" />
                New Subscription
              </CButton>
            </div>
          </CCardHeader>
          <CCardBody>
            {error && <CAlert color="danger">{error}</CAlert>}
            {loading ? (
              <div className="text-center"><CSpinner color="primary" /></div>
            ) : (
              <CTable hover responsive>
                <CTableHead>
                  <CTableRow>
                    <CTableHeaderCell scope="col">#</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Organization</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Plan</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Status</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Start Date</CTableHeaderCell>
                    <CTableHeaderCell scope="col">End Date</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Actions</CTableHeaderCell>
                  </CTableRow>
                </CTableHead>
                <CTableBody>
                  {subscriptions.length === 0 ? (
                    <CTableRow>
                      <CTableDataCell colSpan="7" className="text-center text-muted">
                        No subscriptions found.
                      </CTableDataCell>
                    </CTableRow>
                  ) : (
                    subscriptions.map((sub, idx) => (
                      <CTableRow key={sub.id}>
                        <CTableDataCell>{idx + 1}</CTableDataCell>
                        <CTableDataCell>{sub.organization_name || '-'}</CTableDataCell>
                        <CTableDataCell>{sub.plan_name || '-'}</CTableDataCell>
                        <CTableDataCell><CBadge color={sub.status === 'active' ? 'success' : 'secondary'}>{sub.status}</CBadge></CTableDataCell>
                        <CTableDataCell>{sub.starts_at || '-'}</CTableDataCell>
                        <CTableDataCell>{sub.ends_at || '-'}</CTableDataCell>
                        <CTableDataCell>
                          <CButton color="info" size="sm" className="me-2" onClick={() => navigate(`/subscriptions/${sub.id}`)}>
                            <CIcon icon={cilPencil} />
                          </CButton>
                          <CButton color="danger" size="sm" onClick={() => handleDelete(sub.id)}>
                            <CIcon icon={cilTrash} />
                          </CButton>
                        </CTableDataCell>
                      </CTableRow>
                    ))
                  )}
                </CTableBody>
              </CTable>
            )}
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  )
}

export default Subscriptions
