<?php

namespace Database\Factories\Modules\Billing\Domain\Models;

use App\Modules\Billing\Domain\Models\Subscription;
use App\Modules\Billing\Domain\Models\Plan;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class SubscriptionFactory extends Factory
{
    protected $model = Subscription::class;

    public function definition(): array
    {
        return [
            'organization_id' => Str::uuid()->toString(),
            'plan_id' => Plan::factory(),
            'status' => 'active',
            'starts_at' => now(),
            'ends_at' => now()->addMonth(),
            'trial_ends_at' => null,
            'cancelled_at' => null,
            'auto_renew' => true,
            'price' => $this->faker->randomFloat(2, 10, 200),
            'billing_period' => 'monthly',
            'user_count' => 1,
            'metadata' => [],
        ];
    }
}
