<?php

namespace App\Modules\RolesPermissions\Domain\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Modules\Shared\Domain\Traits\HasUUID;
use App\Modules\Shared\Domain\Traits\HasOrganizationId;
use App\Modules\Organizations\Domain\Models\Organization;

class Permission extends Model
{
    use HasFactory, SoftDeletes, HasUUID, HasOrganizationId;

    protected $table = 'permissions';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'organization_id',
        'name',
        'slug',
        'module',
        'description',
    ];

    // Relationships
    public function organization()
    {
        return $this->belongsTo(Organization::class, 'organization_id');
    }

    public function roles()
    {
        return $this->belongsToMany(
            Role::class,
            'role_permissions',
            'permission_id',
            'role_id'
        )->withTimestamps();
    }
}
