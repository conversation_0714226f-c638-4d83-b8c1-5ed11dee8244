<?php

namespace Database\Factories\Modules\Billing\Domain\Models;

use App\Modules\Billing\Domain\Models\Plan;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class PlanFactory extends Factory
{
    protected $model = Plan::class;

    public function definition(): array
    {
        $name = 'Plan ' . Str::upper(Str::random(4));
        return [
            'organization_id' => \App\Modules\Organizations\Domain\Models\Organization::factory(),
            'name' => $name,
            'slug' => Str::slug($name) . '-' . Str::random(5),
            'description' => $this->faker->sentence(),
            'price' => $this->faker->randomFloat(2, 10, 200),
            'yearly_price' => $this->faker->randomFloat(2, 100, 2000),
            'billing_period' => 'monthly',
            'user_limit' => 0,
            'storage_limit' => 0,
            'features' => [],
            'is_active' => true,
            'trial_days' => 14,
            'sort_order' => 0,
        ];
    }
}
