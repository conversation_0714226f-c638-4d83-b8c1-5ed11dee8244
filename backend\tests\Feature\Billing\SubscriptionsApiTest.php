<?php

namespace Tests\Feature\Billing;

use Tests\TestCase;
use Tests\Support\CreatesOrgUser;
use Illuminate\Foundation\Testing\RefreshDatabase;

class SubscriptionsApiTest extends TestCase
{
    use RefreshDatabase, CreatesOrgUser;

    public function test_subscription_lifecycle(): void
    {
        $org = $this->createOrganization();
        $user = $this->createUserInOrg($org);
        $this->actingAs($user);
        $this->grantPermission($user, 'subscriptions.create');
        $this->grantPermission($user, 'subscriptions.update');
        $this->grantPermission($user, 'subscriptions.delete');

        // Create plan
        $plan = $this->createPlan($org, ['name' => 'Basic', 'slug' => 'basic', 'price' => 10.00]);

        // Create subscription
        $subId = $this->postJson('/api/v1/subscriptions', [
            'organization_id' => $org->id,
            'plan_id' => $plan->id,
            'billing_period' => 'monthly',
        ])->assertStatus(201)->json('id');

        // Upgrade
        $newPlan = $this->createPlan($org, ['name' => 'Pro', 'slug' => 'pro', 'price' => 20.00]);
        $this->postJson("/api/v1/subscriptions/{$subId}/upgrade", ['plan_id' => $newPlan->id])->assertStatus(200);

        // Downgrade
        $this->postJson("/api/v1/subscriptions/{$subId}/downgrade", ['plan_id' => $plan->id])->assertStatus(200);

        // Cancel
        $this->postJson("/api/v1/subscriptions/{$subId}/cancel")->assertStatus(200);
    }
}
