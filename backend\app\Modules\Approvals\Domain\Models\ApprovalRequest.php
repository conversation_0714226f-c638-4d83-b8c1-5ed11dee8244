<?php

namespace App\Modules\Approvals\Domain\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Modules\Shared\Domain\Traits\HasUUID;
use App\Modules\Shared\Domain\Traits\HasOrganizationId;
use App\Modules\Organizations\Domain\Models\Organization;
use App\Modules\Users\Domain\Models\User;

class ApprovalRequest extends Model
{
    use HasFactory, SoftDeletes, HasUUID, HasOrganizationId;

    protected $table = 'approval_requests';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'organization_id',
        'requester_id',
        'type',
        'reference_type',
        'reference_id',
        'status',
        'description',
        'amount',
        'data',
        'current_step',
        'submitted_at',
        'completed_at',
        'resource_type',
        'urgency',
        'current_limit',
        'requested_limit',
        'billing_impact',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'data' => 'array',
        'current_step' => 'integer',
        'submitted_at' => 'datetime',
        'completed_at' => 'datetime',
    ];

    // Relationships
    public function organization()
    {
        return $this->belongsTo(Organization::class, 'organization_id');
    }

    public function requester()
    {
        return $this->belongsTo(User::class, 'requester_id');
    }

    public function steps()
    {
        return $this->hasMany(ApprovalStep::class, 'approval_request_id');
    }

    public function comments()
    {
        return $this->hasMany(ApprovalComment::class, 'approval_request_id');
    }

    public function reference()
    {
        return $this->morphTo('reference', 'reference_type', 'reference_id');
    }

    // Helper methods
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    public function isApproved(): bool
    {
        return $this->status === 'approved';
    }

    public function isRejected(): bool
    {
        return $this->status === 'rejected';
    }

    public function isEscalated(): bool
    {
        return $this->status === 'escalated';
    }
}
