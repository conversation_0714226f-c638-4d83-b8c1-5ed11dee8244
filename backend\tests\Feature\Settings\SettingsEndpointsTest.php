<?php

namespace Tests\Feature\Settings;

use Tests\TestCase;
use App\Modules\Users\Domain\Models\User;
use App\Modules\Organizations\Domain\Models\Organization;
use Illuminate\Foundation\Testing\RefreshDatabase;

class SettingsEndpointsTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Organization $org;

    protected function setUp(): void
    {
        parent::setUp();
        $this->org = Organization::factory()->create();
        $this->user = User::factory()->create(['organization_id' => $this->org->id]);
    }

    public function test_can_get_all_settings()
    {
        $response = $this->actingAs($this->user)->getJson('/api/v1/settings');
        $response->assertStatus(200);
    }

    public function test_can_update_multiple_settings()
    {
        $response = $this->actingAs($this->user)->patchJson('/api/v1/settings', [
            'theme' => 'dark',
            'language' => 'en',
        ]);
        $response->assertStatus(200);
    }

    public function test_can_get_setting_by_key()
    {
        $response = $this->actingAs($this->user)->getJson('/api/v1/settings/theme');
        // Accept 200 if setting exists or 404 if not
        $this->assertTrue(in_array($response->status(), [200, 404]));
    }

    public function test_can_update_setting_by_key()
    {
        $response = $this->actingAs($this->user)->patchJson('/api/v1/settings/theme', [
            'value' => 'dark',
        ]);
        $response->assertStatus(200);
        $response->assertJsonPath('key', 'theme');
        $response->assertJsonPath('value', 'dark');
    }

    public function test_update_setting_requires_value()
    {
        $response = $this->actingAs($this->user)->patchJson('/api/v1/settings/theme', []);
        $response->assertStatus(422);
    }

    public function test_can_get_nonexistent_setting()
    {
        $response = $this->actingAs($this->user)->getJson('/api/v1/settings/nonexistent');
        $response->assertStatus(404);
    }
}
