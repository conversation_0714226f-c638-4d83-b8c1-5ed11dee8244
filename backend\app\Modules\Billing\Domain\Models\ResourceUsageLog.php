<?php

namespace App\Modules\Billing\Domain\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Modules\Shared\Domain\Traits\HasUUID;
use App\Modules\Organizations\Domain\Models\Organization;

class ResourceUsageLog extends Model
{
    use HasFactory, HasUUID;

    protected $table = 'resource_usage_logs';
    public $incrementing = false;
    protected $keyType = 'string';
    public $timestamps = false; // Only recorded_at

    protected $fillable = [
        'subscription_id',
        'tenant_organization_id',
        'resource_type',
        'usage_value',
        'usage_amount',
        'limit_value',
        'limit_amount',
        'usage_percentage',
        'recorded_at',
        'timestamp',
        'alert_sent',
        'alert_level',
        'metadata',
    ];

    protected $casts = [
        'usage_value' => 'integer',
        'usage_amount' => 'integer',
        'limit_value' => 'integer',
        'limit_amount' => 'integer',
        'usage_percentage' => 'decimal:2',
        'recorded_at' => 'datetime',
        'timestamp' => 'datetime',
        'alert_sent' => 'boolean',
        'metadata' => 'array',
    ];

    // Relationships
    public function tenant()
    {
        return $this->belongsTo(Organization::class, 'tenant_organization_id');
    }

    public function subscription()
    {
        return $this->belongsTo(Subscription::class, 'subscription_id');
    }

    // Helper methods
    public function getUsagePercentage(): ?float
    {
        $limit = $this->limit_amount ?? $this->limit_value;
        if (!$limit || $limit == 0) {
            return null; // Unlimited
        }
        $usage = $this->usage_amount ?? $this->usage_value;
        return round(($usage / $limit) * 100, 2);
    }

    public function isLimitExceeded(): bool
    {
        $limit = $this->limit_amount ?? $this->limit_value;
        if (!$limit || $limit == 0) {
            return false; // Unlimited
        }
        $usage = $this->usage_amount ?? $this->usage_value;
        return $usage > $limit;
    }

    public function shouldSendAlert(): bool
    {
        return !$this->alert_sent && $this->usage_percentage >= 75;
    }

    public function getAlertLevelAttribute($value): string
    {
        // If already set, return it
        if ($value) return $value;
        
        // Calculate based on percentage
        return $this->calculateAlertLevel();
    }

    public function calculateAlertLevel(): string
    {
        $percentage = $this->usage_percentage;
        
        if ($percentage >= 100) return 'exceeded';
        if ($percentage >= 90) return 'critical';
        if ($percentage >= 75) return 'warning';
        return 'normal';
    }

    public function getAlertMessage(): string
    {
        $resourceName = $this->getResourceDisplayName();
        $percentage = round($this->usage_percentage, 1);
        
        return match($this->alert_level) {
            'exceeded' => "{$resourceName} limit exceeded ({$percentage}% used)",
            'critical' => "{$resourceName} usage critical ({$percentage}% used)",
            'warning' => "{$resourceName} usage warning ({$percentage}% used)",
            default => "{$resourceName} usage normal ({$percentage}% used)",
        };
    }

    public function getResourceDisplayName(): string
    {
        return match($this->resource_type) {
            'users' => 'User',
            'sub_orgs' => 'Sub-Organization',
            'storage' => 'Storage',
            'hierarchy_depth' => 'Hierarchy Depth',
            'api_calls' => 'API Calls',
            default => 'Resource',
        };
    }

    public function markAlertAsSent(): void
    {
        $this->update(['alert_sent' => true]);
    }
}
