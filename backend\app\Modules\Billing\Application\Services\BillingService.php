<?php

namespace App\Modules\Billing\Application\Services;

use App\Modules\Organizations\Domain\Models\Organization;
use App\Modules\Billing\Domain\Models\Subscription;
use App\Modules\Billing\Domain\Models\SubscriptionAddOn;
use App\Modules\Billing\Domain\Models\Plan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class BillingService
{
    /**
     * Purchase an add-on for a subscription
     */
    public function purchaseAddOn(
        Subscription $subscription,
        string $addOnType,
        int $quantity = 1,
        ?Carbon $startsAt = null
    ): array {
        try {
            DB::beginTransaction();

            // Validate add-on type
            if (!$this->isValidAddOnType($addOnType)) {
                return [
                    'success' => false,
                    'message' => "Invalid add-on type: {$addOnType}",
                ];
            }

            // Get pricing
            $unitPrice = $this->getAddOnUnitPrice($subscription->plan, $addOnType);
            $totalPrice = $unitPrice * $quantity;

            // Calculate start date
            $startsAt = $startsAt ?? now();

            // Create add-on
            $addOn = SubscriptionAddOn::create([
                'subscription_id' => $subscription->id,
                'add_on_type' => $addOnType,
                'quantity' => $quantity,
                'unit_price' => $unitPrice,
                'total_price' => $totalPrice,
                'starts_at' => $startsAt,
                'ends_at' => null,
                'metadata' => [
                    'purchased_at' => now()->toDateTimeString(),
                ],
            ]);

            // Calculate prorated amount if starting mid-month
            $proratedAmount = $this->calculateProratedAmount($addOn, $startsAt);

            Log::info('Add-on purchased', [
                'subscription_id' => $subscription->id,
                'add_on_id' => $addOn->id,
                'add_on_type' => $addOnType,
                'quantity' => $quantity,
                'total_price' => $totalPrice,
                'prorated_amount' => $proratedAmount,
            ]);

            DB::commit();

            return [
                'success' => true,
                'add_on' => $addOn,
                'total_price' => $totalPrice,
                'prorated_amount' => $proratedAmount,
                'message' => "Add-on purchased successfully.",
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Failed to purchase add-on', [
                'subscription_id' => $subscription->id,
                'add_on_type' => $addOnType,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Failed to purchase add-on: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Cancel an add-on
     */
    public function cancelAddOn(SubscriptionAddOn $addOn, ?Carbon $endsAt = null): array
    {
        try {
            DB::beginTransaction();

            $endsAt = $endsAt ?? now();

            $addOn->update([
                'ends_at' => $endsAt,
            ]);

            Log::info('Add-on cancelled', [
                'add_on_id' => $addOn->id,
                'subscription_id' => $addOn->subscription_id,
                'ends_at' => $endsAt->toDateTimeString(),
            ]);

            DB::commit();

            return [
                'success' => true,
                'add_on' => $addOn,
                'message' => 'Add-on cancelled successfully.',
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Failed to cancel add-on', [
                'add_on_id' => $addOn->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Failed to cancel add-on: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Get add-on unit price based on plan
     */
    public function getAddOnUnitPrice(Plan $plan, string $addOnType): int
    {
        return match($addOnType) {
            'user' => $plan->getUserAddOnPrice(),
            'sub_org' => $plan->getSubOrgAddOnPrice(),
            'storage' => $plan->getStorageAddOnPrice(),
            'hierarchy_level' => $plan->getHierarchyLevelAddOnPrice(),
            'module' => $plan->getModuleAddOnPrice(),
            'api_calls' => $plan->getApiCallsAddOnPrice(),
            default => 0,
        };
    }

    /**
     * Calculate prorated amount for add-on
     */
    public function calculateProratedAmount(SubscriptionAddOn $addOn, Carbon $startDate): float
    {
        $daysInMonth = $startDate->daysInMonth;
        $daysRemaining = $startDate->daysInMonth - $startDate->day + 1;

        // If starting on first of month, no proration needed
        if ($startDate->day === 1) {
            return $addOn->total_price;
        }

        return ($addOn->total_price / $daysInMonth) * $daysRemaining;
    }

    /**
     * Calculate total monthly cost for subscription including active add-ons
     */
    public function calculateMonthlyTotal(Subscription $subscription): array
    {
        $basePrice = (float) $subscription->price;
        
        $activeAddOns = $subscription->addOns()
            ->where('starts_at', '<=', now())
            ->where(function($q) {
                $q->whereNull('ends_at')
                  ->orWhere('ends_at', '>', now());
            })
            ->get();

        $addOnsTotal = $activeAddOns->sum('total_price');
        $grandTotal = $basePrice + $addOnsTotal;

        return [
            'base_price' => $basePrice,
            'add_ons_total' => $addOnsTotal,
            'grand_total' => $grandTotal,
            'active_add_ons_count' => $activeAddOns->count(),
            'add_ons' => $activeAddOns->map(function($addOn) {
                return [
                    'id' => $addOn->id,
                    'type' => $addOn->add_on_type,
                    'name' => $addOn->getAddOnName(),
                    'quantity' => $addOn->quantity,
                    'unit_price' => $addOn->unit_price,
                    'total_price' => $addOn->total_price,
                    'starts_at' => $addOn->starts_at->toDateTimeString(),
                ];
            })->toArray(),
        ];
    }

    /**
     * Get add-on pricing for a plan
     */
    public function getAddOnPricing(Plan $plan): array
    {
        return [
            'plan' => [
                'name' => $plan->name,
                'slug' => $plan->slug,
            ],
            'add_on_prices' => [
                'user' => [
                    'type' => 'user',
                    'name' => 'Additional Users',
                    'unit_price' => $plan->getUserAddOnPrice(),
                    'description' => 'Add extra users to your organization',
                ],
                'sub_org' => [
                    'type' => 'sub_org',
                    'name' => 'Additional Sub-Organizations',
                    'unit_price' => $plan->getSubOrgAddOnPrice(),
                    'description' => 'Create more sub-organizations',
                ],
                'storage' => [
                    'type' => 'storage',
                    'name' => 'Additional Storage (per GB)',
                    'unit_price' => $plan->getStorageAddOnPrice(),
                    'description' => 'Increase your storage capacity',
                ],
                'hierarchy_level' => [
                    'type' => 'hierarchy_level',
                    'name' => 'Additional Hierarchy Level',
                    'unit_price' => $plan->getHierarchyLevelAddOnPrice(),
                    'description' => 'Add more levels to your organization hierarchy',
                ],
                'module' => [
                    'type' => 'module',
                    'name' => 'Additional Module',
                    'unit_price' => $plan->getModuleAddOnPrice(),
                    'description' => 'Add extra modules to your plan',
                ],
                'api_calls' => [
                    'type' => 'api_calls',
                    'name' => 'Additional API Calls (per 1000)',
                    'unit_price' => $plan->getApiCallsAddOnPrice(),
                    'description' => 'Increase your API calls limit',
                ],
            ],
        ];
    }

    /**
     * Calculate billing amount for current period
     */
    public function calculateCurrentPeriodBilling(Subscription $subscription): array
    {
        $now = now();
        $periodStart = $subscription->starts_at ?? $now->copy()->startOfMonth();
        $periodEnd = $periodStart->copy()->endOfMonth();

        $basePrice = (float) $subscription->price;
        
        // Get add-ons that were active during this period
        $addOns = $subscription->addOns()
            ->where('starts_at', '<=', $periodEnd)
            ->where(function($q) use ($periodStart) {
                $q->whereNull('ends_at')
                  ->orWhere('ends_at', '>=', $periodStart);
            })
            ->get();

        $addOnsCharges = [];
        $addOnsTotal = 0;

        foreach ($addOns as $addOn) {
            // Calculate actual charge based on when it was active
            $addOnStart = $addOn->starts_at->max($periodStart);
            $addOnEnd = $addOn->ends_at ? $addOn->ends_at->min($periodEnd) : $periodEnd;
            
            $daysActive = $addOnStart->diffInDays($addOnEnd) + 1;
            $daysInPeriod = $periodStart->daysInMonth;
            
            $proratedCharge = ($addOn->total_price / $daysInPeriod) * $daysActive;
            $addOnsTotal += $proratedCharge;

            $addOnsCharges[] = [
                'id' => $addOn->id,
                'type' => $addOn->add_on_type,
                'name' => $addOn->getAddOnName(),
                'quantity' => $addOn->quantity,
                'full_price' => $addOn->total_price,
                'prorated_charge' => round($proratedCharge, 2),
                'days_active' => $daysActive,
            ];
        }

        return [
            'period_start' => $periodStart->toDateString(),
            'period_end' => $periodEnd->toDateString(),
            'base_price' => $basePrice,
            'add_ons_total' => round($addOnsTotal, 2),
            'grand_total' => round($basePrice + $addOnsTotal, 2),
            'add_ons' => $addOnsCharges,
        ];
    }

    /**
     * Get active add-ons for subscription
     */
    public function getActiveAddOns(Subscription $subscription): array
    {
        $activeAddOns = $subscription->addOns()
            ->where('starts_at', '<=', now())
            ->where(function($q) {
                $q->whereNull('ends_at')
                  ->orWhere('ends_at', '>', now());
            })
            ->get();

        return $activeAddOns->map(function($addOn) {
            return [
                'id' => $addOn->id,
                'type' => $addOn->add_on_type,
                'name' => $addOn->getAddOnName(),
                'description' => $addOn->getAddOnDescription(),
                'quantity' => $addOn->quantity,
                'unit_price' => $addOn->unit_price,
                'total_price' => $addOn->total_price,
                'starts_at' => $addOn->starts_at->toDateTimeString(),
                'is_active' => $addOn->isActive(),
            ];
        })->toArray();
    }

    /**
     * Upgrade add-on quantity
     */
    public function upgradeAddOn(SubscriptionAddOn $addOn, int $additionalQuantity): array
    {
        try {
            DB::beginTransaction();

            $oldQuantity = $addOn->quantity;
            $newQuantity = $oldQuantity + $additionalQuantity;
            
            $newTotalPrice = $addOn->unit_price * $newQuantity;

            $addOn->update([
                'quantity' => $newQuantity,
                'total_price' => $newTotalPrice,
            ]);

            Log::info('Add-on upgraded', [
                'add_on_id' => $addOn->id,
                'old_quantity' => $oldQuantity,
                'new_quantity' => $newQuantity,
                'additional_quantity' => $additionalQuantity,
            ]);

            DB::commit();

            return [
                'success' => true,
                'add_on' => $addOn,
                'old_quantity' => $oldQuantity,
                'new_quantity' => $newQuantity,
                'additional_charge' => $addOn->unit_price * $additionalQuantity,
                'message' => 'Add-on upgraded successfully.',
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Failed to upgrade add-on', [
                'add_on_id' => $addOn->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Failed to upgrade add-on: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Check if add-on type is valid
     */
    protected function isValidAddOnType(string $type): bool
    {
        return in_array($type, [
            'user',
            'sub_org',
            'storage',
            'hierarchy_level',
            'module',
            'api_calls',
        ]);
    }

    /**
     * Get add-on usage summary
     */
    public function getAddOnUsageSummary(Subscription $subscription): array
    {
        $activeAddOns = $this->getActiveAddOns($subscription);
        
        $summary = [
            'total_active_add_ons' => count($activeAddOns),
            'monthly_add_on_cost' => array_sum(array_column($activeAddOns, 'total_price')),
            'by_type' => [],
        ];

        foreach ($activeAddOns as $addOn) {
            $type = $addOn['type'];
            if (!isset($summary['by_type'][$type])) {
                $summary['by_type'][$type] = [
                    'type' => $type,
                    'name' => $addOn['name'],
                    'count' => 0,
                    'total_quantity' => 0,
                    'total_cost' => 0,
                ];
            }
            $summary['by_type'][$type]['count']++;
            $summary['by_type'][$type]['total_quantity'] += $addOn['quantity'];
            $summary['by_type'][$type]['total_cost'] += $addOn['total_price'];
        }

        $summary['by_type'] = array_values($summary['by_type']);

        return $summary;
    }

    // ==================================================================
    // API Controller Helper Methods (wrappers for backwards compatibility)
    // ==================================================================

    /**
     * Add an add-on to subscription (API-friendly method)
     */
    public function addAddon(string $subscriptionId, string $addonId, int $quantity = 1): Subscription
    {
        $subscription = Subscription::findOrFail($subscriptionId);
        $addon = \App\Modules\Billing\Domain\Models\Addon::findOrFail($addonId);

        // Attach add-on to subscription using many-to-many
        $subscription->addonCatalog()->attach($addonId, [
            'quantity' => $quantity,
            'price' => $addon->price * $quantity,
            'attached_at' => now(),
        ]);

        return $subscription->fresh();
    }

    /**
     * Remove an add-on from subscription (API-friendly method)
     */
    public function removeAddon(string $subscriptionId, string $addonId): Subscription
    {
        $subscription = Subscription::findOrFail($subscriptionId);
        
        $subscription->addonCatalog()->detach($addonId);

        return $subscription->fresh();
    }

    /**
     * Upgrade subscription to a new plan (API-friendly method)
     */
    public function upgradeSubscription(string $subscriptionId, string $newPlanId): Subscription
    {
        $subscription = Subscription::findOrFail($subscriptionId);
        $newPlan = Plan::findOrFail($newPlanId);

        // Calculate prorated amount
        $proratedAmount = $this->calculateUpgradePrice($subscriptionId, $newPlanId);

        DB::transaction(function () use ($subscription, $newPlan) {
            $subscription->update([
                'plan_id' => $newPlan->id,
                'price' => $newPlan->price,
            ]);

            Log::info('Subscription upgraded', [
                'subscription_id' => $subscription->id,
                'new_plan_id' => $newPlan->id,
            ]);
        });

        return $subscription->fresh();
    }

    /**
     * Downgrade subscription to a new plan (API-friendly method)
     */
    public function downgradeSubscription(string $subscriptionId, string $newPlanId): Subscription
    {
        $subscription = Subscription::findOrFail($subscriptionId);
        $newPlan = Plan::findOrFail($newPlanId);

        DB::transaction(function () use ($subscription, $newPlan) {
            $subscription->update([
                'plan_id' => $newPlan->id,
                'price' => $newPlan->price,
            ]);

            Log::info('Subscription downgraded', [
                'subscription_id' => $subscription->id,
                'new_plan_id' => $newPlan->id,
            ]);
        });

        return $subscription->fresh();
    }

    /**
     * Cancel subscription (API-friendly method)
     */
    public function cancelSubscription(string $subscriptionId, bool $immediate = false): Subscription
    {
        $subscription = Subscription::findOrFail($subscriptionId);

        DB::transaction(function () use ($subscription, $immediate) {
            if ($immediate) {
                $subscription->update([
                    'status' => 'cancelled',
                    'cancelled_at' => now(),
                ]);
            } else {
                // Schedule cancellation at end of billing period
                $subscription->update([
                    'cancelled_at' => $subscription->ends_at ?? now()->endOfMonth(),
                ]);
            }

            Log::info('Subscription cancelled', [
                'subscription_id' => $subscription->id,
                'immediate' => $immediate,
            ]);
        });

        return $subscription->fresh();
    }

    /**
     * Calculate upgrade price with proration
     */
    public function calculateUpgradePrice(string $subscriptionId, string $newPlanId): array
    {
        $subscription = Subscription::findOrFail($subscriptionId);
        $newPlan = Plan::findOrFail($newPlanId);

        $currentPlan = $subscription->plan;
        
        // Calculate days remaining in current billing period
        $periodEnd = $subscription->ends_at ?? now()->endOfMonth();
        $daysRemaining = now()->diffInDays($periodEnd, false);
        $daysRemaining = max(0, $daysRemaining);
        
        // Calculate daily rates
        $currentPlanDailyRate = $currentPlan->price / 30;
        $newPlanDailyRate = $newPlan->price / 30;
        
        // Calculate prorated amounts
        $currentPlanCredit = $currentPlanDailyRate * $daysRemaining;
        $newPlanCharge = $newPlanDailyRate * $daysRemaining;
        $proratedAmount = $newPlanCharge - $currentPlanCredit;

        return [
            'prorated_amount' => round($proratedAmount, 2),
            'days_remaining' => $daysRemaining,
            'current_plan_daily_rate' => round($currentPlanDailyRate, 2),
            'new_plan_daily_rate' => round($newPlanDailyRate, 2),
            'current_plan_credit' => round($currentPlanCredit, 2),
            'new_plan_charge' => round($newPlanCharge, 2),
        ];
    }
}
