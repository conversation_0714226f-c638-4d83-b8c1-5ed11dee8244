import client from './client'

export const authAPI = {
  register: (data) => client.post('/auth/register', data),
  login: (email, password) => client.post('/auth/login', { email, password }),
  logout: () => client.post('/auth/logout'),
  refreshToken: (refreshToken) => client.post('/auth/refresh-token', { refresh_token: refreshToken }),
  requestOtp: (email) => client.post('/auth/request-otp', { email }),
  verifyOtp: (email, otp) => client.post('/auth/verify-otp', { email, otp }),
  resetPassword: (email, token, password) =>
    client.post('/auth/reset-password', { email, token, password }),
  setupMFA: () => client.post('/auth/setup-mfa'),
  verifyMFA: (code) => client.post('/auth/verify-mfa', { code }),
  getMe: () => client.get('/me'),
  updateMe: (data) => client.patch('/me', data),
}
