<?php

use Illuminate\Support\Facades\Route;

Route::middleware('auth')->group(function () {
    // Reports
    Route::get('reports/subscriptions', [\App\Modules\Reports\Http\Controllers\ReportController::class, 'subscriptions']);
    Route::get('reports/payments', [\App\Modules\Reports\Http\Controllers\ReportController::class, 'payments']);
    Route::get('reports/revenue', [\App\Modules\Reports\Http\Controllers\ReportController::class, 'revenue']);
    Route::get('reports/users', [\App\Modules\Reports\Http\Controllers\ReportController::class, 'users']);
    Route::get('reports/approvals', [\App\Modules\Reports\Http\Controllers\ReportController::class, 'approvals']);
});
