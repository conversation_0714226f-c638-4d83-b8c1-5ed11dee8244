# APPROVAL WORKFLOW AND RESOURCE-BASED PRICING

**Date**: January 29, 2025  
**Status**: CRITICAL ADDITIONS TO ARCHITECTURE

**Reference**: This extends ARCHITECTURE_UNDERSTANDING.md with two critical features:
1. Approval workflow for limit overages
2. Resource-based pricing model

---

## 🔄 APPROVAL WORKFLOW FOR LIMIT OVERAGES

### ✅ CRITICAL RULE: ALL limit overages require Portal Owner approval

**What Changed from Original Understanding**:
- ❌ WRONG: Tenant exceeds limit → System blocks or auto-charges
- ✅ CORRECT: Tenant exceeds limit → Request goes to Portal Owner for approval

---

### What Requires Approval:

1. ✅ **Creating sub-organizations beyond plan limit**
2. ✅ **Creating users beyond plan limit**
3. ✅ **Storage usage beyond plan limit**
4. ✅ **Enabling additional modules/features**
5. ✅ **Plan changes (upgrades/downgrades)**
6. ✅ **Increasing hierarchy depth beyond plan limit**

---

### Approval Process Flow:

```
┌─────────────────────────────────────┐
│  Tenant Action (Exceeds Limit)     │
└──────────────┬──────────────────────┘
               ↓
┌──────────────────────────────────────┐
│  System Creates Pending Request      │
│  Resource Status: PENDING_APPROVAL   │
└──────────────┬───────────────────────┘
               ↓
┌──────────────────────────────────────┐
│  Notification to Portal Owner        │
│  (Email + Dashboard Alert)           │
└──────────────┬───────────────────────┘
               ↓
┌──────────────────────────────────────┐
│  Portal Owner Reviews Request        │
│  (Approval Dashboard)                │
└──────────────┬───────────────────────┘
               ↓
       ┌───────┴───────┐
       ↓               ↓
  ┌─────────┐    ┌─────────┐
  │ APPROVE │    │ REJECT  │
  └────┬────┘    └────┬────┘
       ↓              ↓
  ┌────────────┐  ┌──────────────┐
  │ Activate   │  │ Keep Pending │
  │ Resource   │  │ or Delete    │
  └────┬───────┘  └──────┬───────┘
       ↓                 ↓
  ┌────────────┐  ┌──────────────┐
  │ Apply      │  │ Notify       │
  │ Billing    │  │ Tenant       │
  └────┬───────┘  └──────────────┘
       ↓
  ┌────────────┐
  │ Notify     │
  │ Tenant     │
  └────────────┘
```

---

## 📋 APPROVAL STATES BY RESOURCE TYPE

### 1. Sub-Organizations (Branches)

**When Limit Exceeded**:
```
Tenant: ABC Retailers
Plan: Pro Plan (5 sub-orgs included)
Current: 5 sub-orgs active
Action: Create 6th sub-org "Factory Unit"

Result:
├── Sub-org created with status: PENDING_APPROVAL
├── Visible to tenant (grayed out/locked)
├── Badge shown: "⏳ Pending Portal Owner Approval"
├── Cannot add users to it
├── Cannot enter data in it
└── Notification sent to Portal Owner
```

**Tenant View**:
```
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
SUB-ORGANIZATIONS
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

✅ Delhi Branch (Active)
✅ Mumbai Branch (Active)
✅ Warehouse Division (Active)
✅ Chennai Office (Active)
✅ Kolkata Store (Active)
⏳ Factory Unit (Pending Approval)
   └── Additional fee: ₹2,000/month
       Submitted: Jan 15, 2025
       Status: Awaiting Portal Owner approval
       [CANCEL REQUEST]
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
```

**Portal Owner Approval Interface**:
```
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
APPROVAL REQUEST #AR-2025-0042
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

Tenant: ABC Retailers Pvt Ltd
Plan: Pro Plan (₹10,000/month)

Request Type: Additional Sub-Organization
Resource Name: "Factory Unit"
Created By: Rajesh Kumar (CEO)
Submitted: Jan 15, 2025 10:35 AM

CURRENT USAGE:
├── Sub-Organizations: 5/5 (100%)
├── Users: 23/25 (92%)
├── Storage: 42 GB / 50 GB (84%)
└── Hierarchy Depth: 4 levels

BILLING IMPACT:
├── Additional Sub-Org Fee: ₹2,000/month
├── Prorated (15 days): ₹1,000
└── Next Invoice: ₹12,000 + ₹2,160 GST

RECOMMENDATION:
⚠️ Tenant approaching multiple limits
💡 Consider suggesting Enterprise Plan upgrade

[APPROVE] [REJECT] [SUGGEST UPGRADE] [CONTACT TENANT]
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
```

**Upon Approval**:
```
✅ Sub-organization activated
✅ Status changed: PENDING_APPROVAL → ACTIVE
✅ Add-on applied to subscription
✅ Prorated charge added to next invoice
📧 Email sent to tenant:
   "Your request for 'Factory Unit' has been approved.
   Additional charge: ₹2,000/month will appear on your next invoice."
```

---

### 2. Users

**When Limit Exceeded**:
```
Tenant: ABC Retailers
Plan: Pro Plan (25 users included)
Current: 25 users active
Action: Tenant admin creates 26th user

Result:
├── User account created
├── Status: PENDING_APPROVAL
├── User CANNOT login
├── Welcome email NOT sent yet
├── Visible in tenant's user list (with pending badge)
└── Notification sent to Portal Owner
```

**Tenant View**:
```
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
USERS (26/25)
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

⚠️ User limit exceeded

Name               Email                    Status
──────────────────────────────────────────────────
Rajesh Kumar       <EMAIL>           ✅ Active
Priya Sharma       <EMAIL>            ✅ Active
...(23 more active users)...
Suresh Patel       <EMAIL>           ⏳ Pending
                   └── Additional fee: ₹500/month
                       Awaiting approval
                       [CANCEL REQUEST]
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

💡 Tip: You've reached your user limit.
   Upgrade to Enterprise Plan for 100 users.
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
```

**User's Perspective** (Suresh Patel):
```
📧 Email received:

Subject: Account Creation Pending

Hi Suresh,

Your account has been created but is pending activation.

Organization: ABC Retailers Pvt Ltd
Email: <EMAIL>

Your account will be activated once approved by the
portal administrator. You'll receive another email
with login credentials once approved.

Thank you for your patience.
```

**Portal Owner Approval**:
```
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
APPROVAL REQUEST #AR-2025-0043
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

Tenant: ABC Retailers Pvt Ltd

Request Type: Additional User
User: Suresh Patel (<EMAIL>)
Role: Inventory Staff
Organization: Warehouse Division
Submitted: Jan 25, 2025 3:15 PM

CURRENT USAGE:
├── Users: 25/25 (100%) ⚠️
├── Active users exceed limit

OPTIONS:

1️⃣ APPROVE USER ADD-ON
   ├── Cost: ₹500/month per user
   ├── Prorated: ₹250 (15 days)
   └── New limit: 26 users

2️⃣ SUGGEST ENTERPRISE UPGRADE
   ├── From: Pro (25 users) - ₹10,000/month
   ├── To: Enterprise (100 users) - ₹25,000/month
   ├── Better value if they need >30 users
   └── Send upgrade offer email

3️⃣ REJECT REQUEST
   └── Tenant must remove existing user first

[APPROVE ADD-ON] [SUGGEST UPGRADE] [REJECT] [CONTACT]
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
```

**Upon Approval**:
```
✅ User activated
✅ Status: PENDING_APPROVAL → ACTIVE
✅ User add-on applied (₹500/month)
✅ Login credentials sent to user
📧 Welcome email sent:
   "Your account is now active! Click here to set password..."
```

---

### 3. Storage Overages

**Multi-Stage Warning System**:

**Stage 1: 75% Usage**
```
📧 Email to Tenant:

Subject: Storage Usage Notice

You're using 37.5 GB of your 50 GB storage limit (75%).

Consider:
- Deleting old files
- Archiving unused data
- Upgrading your plan
```

**Stage 2: 90% Usage**
```
⚠️ Dashboard Alert + Email:

Storage: 45 GB / 50 GB (90%)

You're approaching your storage limit.
Upload restrictions will apply at 100% usage.

[UPGRADE PLAN] [PURCHASE STORAGE] [MANAGE FILES]
```

**Stage 3: 100% Usage**
```
🚨 System Action:
├── Auto-create approval request for +10 GB
├── Allow 5% grace period (up to 52.5 GB)
├── Show urgent banner on dashboard
└── Send notification to Portal Owner

Tenant sees:
"Storage limit reached. Request for additional 10 GB
sent to administrator. Grace period: 2.5 GB remaining."
```

**Stage 4: 105% Usage (Grace Limit)**
```
❌ UPLOADS BLOCKED

Error on file upload:
"Storage limit exceeded. Your request for additional
storage is pending approval. Please contact support."

Tenant CANNOT:
├── Upload new files
├── Import data
└── Backup operations

Tenant CAN:
├── Download/export files
├── Delete files
└── View existing data
```

**Portal Owner Auto-Request**:
```
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
AUTO-GENERATED REQUEST #AR-2025-0044
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

Tenant: ABC Retailers Pvt Ltd

Request Type: Storage Increase (Auto-generated)
Current: 50 GB → Requested: 60 GB (+10 GB)
Actual Usage: 52.3 GB (104.6%)
Generated: Jan 28, 2025 (after hitting limit)

⚠️ URGENT: Tenant has exceeded storage limit

BILLING IMPACT:
├── Additional Storage: 10 GB × ₹100 = ₹1,000/month
├── Prorated: ₹500 (15 days)
└── Next Invoice: ₹11,000 + GST

RECOMMENDATION:
💡 If tenant needs >75 GB, suggest Enterprise Plan
   (includes 200 GB storage)

[APPROVE +10GB] [APPROVE +25GB] [SUGGEST UPGRADE] [CONTACT]
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
```

---

### 4. Hierarchy Depth Limit

**When Limit Exceeded**:
```
Tenant: ABC Retailers
Plan: Pro Plan (max 5 levels)
Current Hierarchy:
└── ABC Retailers (L1)
    └── Warehouse Division (L2)
        └── Warehouse A (L3)
            └── Storage Section (L4)
                └── Rack Zone (L5) ← Current limit

Action: Create "Shelf Unit" under Rack Zone (would be L6)

Result:
├── Creation blocked with message
├── Auto-creates approval request
└── Notification to Portal Owner
```

**Tenant Sees**:
```
⚠️ Hierarchy Depth Limit Reached

Your plan allows up to 5 organizational levels.
You're attempting to create a 6th level.

Request sent for approval.
Additional cost: ₹2,000/month per level

[CANCEL] [WAIT FOR APPROVAL]
```

---

## 📊 PORTAL OWNER APPROVAL DASHBOARD

### Main Dashboard View:

```
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
               APPROVAL MANAGEMENT DASHBOARD
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

📊 OVERVIEW:

┌────────────────────┬──────────────────────────────────────┐
│ Pending Approvals  │ 12 requests                          │
│ Urgent (>3 days)   │ 3 requests (Storage blocks active)   │
│ Revenue Impact     │ +₹15,500/month if all approved       │
│ Today              │ 5 new requests                       │
└────────────────────┴──────────────────────────────────────┘

🔔 PENDING REQUESTS (Sorted by urgency):

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🚨 URGENT - BLOCKING OPERATIONS
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

1. XYZ Manufacturing Co
   Type: Storage Increase (Auto-generated)
   Current: 50 GB → Requested: 60 GB
   Status: 🔴 BLOCKING UPLOADS (107% used)
   Impact: +₹1,000/month
   Age: 4 days
   [⚡ QUICK APPROVE] [REJECT] [CONTACT]

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
⚠️ HIGH PRIORITY
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

2. ABC Retailers Pvt Ltd
   Type: 3 Additional Users
   Details: Users pending activation
   Impact: +₹1,500/month
   Age: 2 days
   Recommendation: Suggest Enterprise upgrade
   [APPROVE] [SUGGEST UPGRADE] [REJECT]

3. PQR Hospital Group
   Type: Additional Sub-Organization
   Details: "Pharmacy Branch"
   Impact: +₹2,000/month
   Age: 1 day
   [APPROVE] [REJECT] [CONTACT]

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📋 NORMAL PRIORITY
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

4. ABC Retailers Pvt Ltd
   Type: Additional Sub-Organization
   Details: "Factory Unit"
   Impact: +₹2,000/month
   Age: 6 hours
   [APPROVE] [REJECT]

5. DEF Logistics Pvt Ltd
   Type: Module Add-on
   Details: Enable "HR Management Module"
   Impact: +₹5,000/month
   Age: 3 hours
   [APPROVE] [REJECT]

... 7 more requests

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

[VIEW ALL] [FILTER BY TENANT] [FILTER BY TYPE] [SETTINGS]

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
```

### Bulk Actions:

```
✅ BULK APPROVE
├── Select multiple requests
├── One-click approval for low-risk items
└── Auto-applies billing adjustments

📧 BATCH COMMUNICATION
├── Send upgrade offers to multiple tenants
└── Notify multiple tenants of rejections

📊 ANALYTICS
├── Most requested add-ons
├── Average approval time
└── Revenue from approved requests
```

---

## 💰 RESOURCE-BASED PRICING MODEL

### Critical Business Reality:

**Your Infrastructure**: Portal deployed on **VPS** (Virtual Private Server)

**Fixed Costs Per VPS**:
- CPU cores
- RAM
- Storage (SSD/HDD)
- Bandwidth
- Backups
- Maintenance

**Key Insight**: As tenant resource usage grows, your VPS costs increase.

---

### Resource Impact Analysis:

#### **1. Users Impact**:

```
RESOURCE CONSUMPTION PER USER:

├── Database Connections: 2-5 concurrent per active user
├── Session Storage: ~5 MB per user session
├── Query Load: ~100-500 queries/day per user
├── CPU Usage: Background tasks, reports, exports
└── Bandwidth: API calls, file uploads/downloads

COST CALCULATION:
10 users  → Minimal impact (~₹500/month in resources)
25 users  → Moderate impact (~₹1,500/month)
50 users  → Significant impact (~₹3,500/month)
100 users → High impact (~₹8,000/month)
500 users → Very high (~₹40,000/month, need dedicated VPS)

PRICING STRATEGY:
├── Include 10-25 users in base plans
├── Charge ₹500/user/month for add-ons (covers cost + margin)
└── Suggest plan upgrades for >30 users (better economics)
```

#### **2. Sub-Organizations Impact**:

```
RESOURCE CONSUMPTION PER SUB-ORG:

├── Query Complexity: More JOINs, WHERE clauses
├── Permission Checks: Calculated per request
├── Data Segregation: Indexes, partitioning overhead
└── UI Rendering: Org selector dropdowns, filters

QUERY PERFORMANCE:

Simple (2 orgs):
SELECT * FROM invoices WHERE org_id IN (1, 2);
→ Query time: ~50ms

Complex (20 orgs across 5 levels):
WITH RECURSIVE org_tree AS (
  SELECT id FROM organizations WHERE id = 1
  UNION ALL
  SELECT o.id FROM organizations o
  JOIN org_tree ON o.parent_id = org_tree.id
)
SELECT * FROM invoices WHERE org_id IN (
  SELECT id FROM org_tree
);
→ Query time: ~500ms (10x slower!)

COST CALCULATION:
2-5 orgs   → Minimal impact (~₹500/month)
10 orgs    → Moderate (~₹2,000/month in query overhead)
20+ orgs   → High (~₹5,000/month, may need caching layer)

PRICING STRATEGY:
├── Include 2-5 orgs in base plans
├── Charge ₹1,000-₹3,000/org based on hierarchy depth
└── Higher price for deeper nesting (more expensive queries)
```

#### **3. Hierarchy Depth Impact**:

```
QUERY COMPLEXITY BY DEPTH:

Level 2-3: Simple queries, minimal overhead
→ Cost: Included in base plan

Level 4-5: Recursive CTEs, more complex
→ Cost: Pro plan and above
→ Additional overhead: ~₹1,000/month

Level 6+: Very complex, requires optimization
→ Cost: ₹2,000/month per level above 5
→ May require:
   - Materialized views
   - Path enumeration optimization
   - Caching layers
   - Query optimization

EXAMPLE:
Level 7 hierarchy = ₹2,000/month extra
Level 10 hierarchy = ₹10,000/month extra
```

#### **4. Storage Impact**:

```
STORAGE COSTS:

├── Primary Storage: SSD space on VPS
├── Backup Storage: 3x primary (daily, weekly, monthly)
├── CDN/Object Storage: For files, images
└── Database Size: Indexes grow with data

COST BREAKDOWN:
10 GB  → ₹300/month (storage + backup)
50 GB  → ₹1,500/month
100 GB → ₹3,000/month
500 GB → ₹15,000/month
1 TB   → ₹30,000/month (may need separate storage server)

PRICING STRATEGY:
├── Include 10-50 GB in base plans
├── Charge ₹100/GB/month for overages
└── Bulk discounts for large storage (₹75/GB for >100GB)
```

#### **5. API Call Rate Impact**:

```
API LOAD IMPACT:

├── CPU Usage: Processing requests
├── Bandwidth: Data transfer
├── Rate Limiting: Infrastructure to manage
└── Caching: Redis/Memcached needed for high volume

COST BY VOLUME:
10K calls/day   → Minimal (~₹200/month)
50K calls/day   → Moderate (~₹1,000/month)
500K calls/day  → High (~₹5,000/month, need Redis)
5M calls/day    → Very high (~₹25,000/month, load balancer)

PRICING:
├── Include 10K-50K/day in base plans
├── Charge ₹5,000 per 50K additional calls/day
└── Enterprise gets unlimited (custom VPS anyway)
```

---

### VPS Tier Requirements:

```
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
                    VPS SIZING GUIDE
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

┌─────────────────────────────────────────────────────────────┐
│ SMALL VPS - For Basic Plan Tenants (1-3 tenants)            │
├─────────────────────────────────────────────────────────────┤
│ CPU:        2 cores                                          │
│ RAM:        4 GB                                             │
│ Storage:    50 GB SSD                                        │
│ Bandwidth:  1 TB/month                                       │
│ Users:      Up to 30 total across tenants                    │
│ Sub-Orgs:   Up to 10 total                                   │
│ Cost:       ~₹3,000/month                                    │
│ Revenue:    ₹15,000/month (3 × ₹5,000)                      │
│ Margin:     ₹12,000/month (80%)                              │
└─────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────┐
│ MEDIUM VPS - For Pro Plan Tenants (3-8 tenants)             │
├─────────────────────────────────────────────────────────────┤
│ CPU:        4 cores                                          │
│ RAM:        8 GB                                             │
│ Storage:    200 GB SSD                                       │
│ Bandwidth:  3 TB/month                                       │
│ Users:      Up to 150 total                                  │
│ Sub-Orgs:   Up to 40 total                                   │
│ Cost:       ~₹7,000/month                                    │
│ Revenue:    ₹60,000/month (6 × ₹10,000)                     │
│ Margin:     ₹53,000/month (88%)                              │
└─────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────┐
│ LARGE VPS - For Enterprise Tenants (2-5 tenants)            │
├─────────────────────────────────────────────────────────────┤
│ CPU:        8 cores                                          │
│ RAM:        16 GB                                            │
│ Storage:    500 GB SSD                                       │
│ Bandwidth:  5 TB/month                                       │
│ Database:   Separate instance (2 cores, 4 GB)               │
│ Users:      Up to 400 total                                  │
│ Sub-Orgs:   Up to 100 total                                  │
│ Cost:       ~₹18,000/month (app + db)                        │
│ Revenue:    ₹100,000/month (4 × ₹25,000)                    │
│ Margin:     ₹82,000/month (82%)                              │
└─────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────┐
│ DEDICATED - For Single Large Tenant (Custom)                 │
├─────────────────────────────────────────────────────────────┤
│ CPU:        16+ cores                                        │
│ RAM:        32+ GB                                           │
│ Storage:    1+ TB NVMe SSD                                   │
│ Bandwidth:  10+ TB/month                                     │
│ Database:   Dedicated server (8 cores, 16 GB)               │
│ Cache:      Redis server (2 cores, 4 GB)                     │
│ Load Bal:   nginx/HAProxy                                    │
│ Users:      500+                                             │
│ Sub-Orgs:   100+                                             │
│ Cost:       ~₹50,000/month                                   │
│ Revenue:    ₹100,000+/month (custom pricing)                │
│ Margin:     ₹50,000+/month (50%+)                            │
└─────────────────────────────────────────────────────────────┘

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
```

---

### Complete Plan Matrix (Resource-Driven):

```
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
                        PLAN COMPARISON
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

                 BASIC      PRO        ENTERPRISE    CUSTOM
────────────────────────────────────────────────────────────────────
Price/Month      ₹5,000     ₹10,000    ₹25,000       Quoted

USERS
├─ Included      10         25         100           500+
├─ Add-on Cost   ₹500/user  ₹500/user  ₹400/user     ₹300/user
└─ Your Cost     ~₹50       ~₹50       ~₹40          ~₹30

SUB-ORGANIZATIONS
├─ Included      2          5          20            50+
├─ Add-on Cost   ₹1,500     ₹2,000     ₹2,500        ₹3,000
└─ Your Cost     ~₹500      ~₹800      ~₹1,000       ~₹1,200

HIERARCHY DEPTH
├─ Included      3 levels   5 levels   Unlimited     Unlimited
├─ Extra Level   ₹2,000     ₹2,000     Included      Included
└─ Your Cost     ~₹800      ~₹800      ~₹1,500       ~₹2,000

STORAGE
├─ Included      10 GB      50 GB      200 GB        1 TB+
├─ Add-on Cost   ₹100/GB    ₹100/GB    ₹75/GB        ₹50/GB
└─ Your Cost     ~₹30/GB    ~₹30/GB    ~₹25/GB       ~₹20/GB

MODULES
├─ Included      1          2          All           All
├─ Add-on Cost   ₹3,000/mod ₹4,000/mod Included      Included
└─ Your Cost     ~₹1,000    ~₹1,500    ~₹3,000       ~₹5,000

API CALLS/DAY
├─ Included      10,000     50,000     500,000       Unlimited
├─ Add-on Cost   ₹5,000     ₹5,000     ₹10,000       Included
└─ Your Cost     ~₹2,000    ~₹2,000    ~₹4,000       ~₹8,000

SUPPORT
├─ Included      Email      Priority   Dedicated     24/7
├─ Response SLA  48h        24h        4h            1h
└─ Your Cost     Minimal    ~₹500      ~₹2,000       ~₹5,000

────────────────────────────────────────────────────────────────────
VPS TIER         Small      Medium     Large         Dedicated
VPS Cost         ~₹3,000    ~₹7,000    ~₹18,000      ~₹50,000
Margin %         40%        30%        28%           50%+
Break-even       1 tenant   1 tenant   1 tenant      1 tenant
────────────────────────────────────────────────────────────────────

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
```

---

## 📊 RESOURCE MONITORING DASHBOARD

**Portal Owner Needs Real-Time Visibility**:

```
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
          INFRASTRUCTURE & RESOURCE MONITORING
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

🖥️  VPS: vps-prod-01 (Medium Tier)

┌─────────────────────────────────────────────────────────┐
│ CURRENT UTILIZATION                                     │
├─────────────────────────────────────────────────────────┤
│ CPU:     ████████████░░░░░░░░ 68% (2.7 / 4 cores)      │
│ RAM:     ████████████████░░░░ 82% (6.5 / 8 GB)     ⚠️  │
│ Storage: ███████████████░░░░░ 76% (152 / 200 GB)       │
│ Network: ████████░░░░░░░░░░░░ 45% (1.4 / 3 TB)         │
└─────────────────────────────────────────────────────────┘

⚠️ WARNING: RAM usage above 80% - Consider upgrade

📊 TENANT RESOURCE BREAKDOWN:

┌──────────────────────┬────────┬────────┬─────────┬─────────┐
│ TENANT               │ USERS  │ ORGS   │ STORAGE │ IMPACT  │
├──────────────────────┼────────┼────────┼─────────┼─────────┤
│ ABC Retailers        │ 25/25  │ 5/5    │ 48GB    │ ███ 35% │
│ XYZ Manufacturing    │ 18/25  │ 8/5(+3)│ 62GB    │ ████40% │
│ PQR Hospital         │ 8/10   │ 2/2    │ 12GB    │ █ 10%   │
│ DEF Logistics        │ 12/25  │ 3/5    │ 30GB    │ ██ 15%  │
└──────────────────────┴────────┴────────┴─────────┴─────────┘

🎯 RECOMMENDATIONS:

1. ⚠️  Upgrade VPS to Large tier (₹18K/month)
   └── Current load: 75% sustained
       Growth trajectory: +15%/month
       ROI: Revenue ₹60K vs Cost ₹18K = 233% margin

2. 💡 Contact XYZ Manufacturing
   └── Using 3 extra sub-orgs (generating ₹6K/month)
       Suggest Enterprise plan upgrade
       Better value: ₹25K vs current ₹16K + add-ons

3. 📈 ABC Retailers at limits
   └── 25/25 users, 5/5 orgs, 48/50GB
       Likely to request more resources soon
       Proactively offer Enterprise upgrade

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
```

---

## 💡 STRATEGIC PRICING DECISIONS

### When to Suggest Plan Upgrades:

```
AUTO-SUGGESTION TRIGGERS:

1. User Add-ons > 50% of next plan's included users
   Example: Basic (10) + 15 add-ons = 25 users
   → Suggest Pro (includes 25 users)
   Savings: ₹5,000 + (15 × ₹500) = ₹12,500 → ₹10,000 (save ₹2,500)

2. Multiple resource types exceeded
   Example: Pro plan + 10 users + 3 orgs + 20GB storage
   → Suggest Enterprise
   Current: ₹10,000 + ₹5,000 + ₹6,000 + ₹2,000 = ₹23,000
   Enterprise: ₹25,000 (only ₹2K more, huge limits)

3. Consistent growth pattern
   Example: Tenant requested add-ons 3 months in a row
   → Proactive upgrade offer

4. Resource utilization > 90%
   Example: 23/25 users, 4.5/5 orgs, 47/50GB
   → Suggest upgrade before hitting limits
```

---

## ✅ IMPLEMENTATION REQUIREMENTS

### Database Schema Additions:

```sql
-- Approval requests table
CREATE TABLE approval_requests (
    id BIGINT PRIMARY KEY,
    tenant_organization_id BIGINT NOT NULL,
    request_type ENUM('user', 'sub_org', 'storage', 'module', 'hierarchy', 'plan_change'),
    resource_id BIGINT, -- ID of pending resource (user_id, org_id, etc.)
    requested_value JSON, -- Details of request
    current_limit INT,
    requested_limit INT,
    billing_impact DECIMAL(10,2), -- Monthly cost
    status ENUM('pending', 'approved', 'rejected', 'cancelled'),
    urgency ENUM('low', 'normal', 'high', 'urgent'),
    submitted_at TIMESTAMP,
    submitted_by BIGINT,
    reviewed_at TIMESTAMP,
    reviewed_by BIGINT,
    notes TEXT,
    FOREIGN KEY (tenant_organization_id) REFERENCES organizations(id),
    INDEX idx_status_urgency (status, urgency),
    INDEX idx_tenant_status (tenant_organization_id, status)
);

-- Resource usage tracking
CREATE TABLE resource_usage_logs (
    id BIGINT PRIMARY KEY,
    tenant_organization_id BIGINT NOT NULL,
    resource_type VARCHAR(50),
    usage_value INT,
    limit_value INT,
    usage_percentage DECIMAL(5,2),
    recorded_at TIMESTAMP,
    alert_sent BOOLEAN DEFAULT FALSE,
    INDEX idx_tenant_resource (tenant_organization_id, resource_type)
);

-- User status field
ALTER TABLE users
ADD COLUMN status ENUM('active', 'pending_approval', 'suspended', 'deleted') DEFAULT 'active',
ADD COLUMN approval_request_id BIGINT,
ADD FOREIGN KEY (approval_request_id) REFERENCES approval_requests(id);

-- Organization status field
ALTER TABLE organizations
ADD COLUMN status ENUM('active', 'pending_approval', 'suspended', 'deleted') DEFAULT 'active',
ADD COLUMN approval_request_id BIGINT,
ADD FOREIGN KEY (approval_request_id) REFERENCES approval_requests(id);
```

---

## 🎯 SUMMARY

### Key Takeaways:

1. **ALL limit overages require Portal Owner approval**
   - Users, sub-orgs, storage, hierarchy depth
   - Resources created but kept in PENDING status
   - Portal Owner approves/rejects via dashboard

2. **Pricing driven by actual infrastructure costs**
   - More users = more DB load, RAM, CPU
   - More sub-orgs = more complex queries
   - More hierarchy = recursive query overhead
   - More storage = disk space + backups

3. **VPS tiers determine tenant capacity**
   - Small VPS: 1-3 Basic tenants
   - Medium VPS: 3-8 Pro tenants
   - Large VPS: 2-5 Enterprise tenants
   - Dedicated: 1 large custom tenant

4. **Proactive resource monitoring essential**
   - Real-time VPS utilization tracking
   - Per-tenant resource consumption
   - Auto-alerts for approaching limits
   - Upgrade suggestions to maximize revenue

5. **Approval workflow prevents resource abuse**
   - Portal Owner controls growth
   - Prevents unexpected infrastructure costs
   - Opportunity to upsell plan upgrades
   - Better planning for VPS capacity

---

**THIS DOCUMENT**: Mandatory reference for billing module development.
**READ TOGETHER WITH**: ARCHITECTURE_UNDERSTANDING.md
