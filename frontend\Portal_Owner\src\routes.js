import React from 'react'

const Dashboard = React.lazy(() => import('./views/dashboard/Dashboard'))

// Portal Owner Views - Management
const Organizations = React.lazy(() => import('./views/organizations/Organizations'))
const OrganizationDetail = React.lazy(() => import('./views/organizations/OrganizationDetail'))
const Users = React.lazy(() => import('./views/users/Users'))
const UserDetail = React.lazy(() => import('./views/users/UserDetail'))
const Roles = React.lazy(() => import('./views/roles/Roles'))
const RoleDetail = React.lazy(() => import('./views/roles/RoleDetail'))

// Portal Owner Views - Billing
const Subscriptions = React.lazy(() => import('./views/billing/Subscriptions'))
const SubscriptionDetail = React.lazy(() => import('./views/billing/SubscriptionDetail'))
const Payments = React.lazy(() => import('./views/billing/Payments'))
const PaymentDetail = React.lazy(() => import('./views/billing/PaymentDetail'))
const Invoices = React.lazy(() => import('./views/billing/Invoices'))
const InvoiceDetail = React.lazy(() => import('./views/billing/InvoiceDetail'))

// Portal Owner Views - Operations
const Approvals = React.lazy(() => import('./views/approvals/Approvals'))
const ApprovalDetail = React.lazy(() => import('./views/approvals/ApprovalDetail'))
const Notifications = React.lazy(() => import('./views/notifications/Notifications'))
const NotificationDetail = React.lazy(() => import('./views/notifications/NotificationDetail'))
const Reports = React.lazy(() => import('./views/reports/Reports'))

// Portal Owner Views - Configuration
const Webhooks = React.lazy(() => import('./views/webhooks/Webhooks'))
const WebhookDetail = React.lazy(() => import('./views/webhooks/WebhookDetail'))
const Settings = React.lazy(() => import('./views/settings/Settings'))

const routes = [
  { path: '/', exact: true, name: 'Home' },
  { path: '/dashboard', name: 'Dashboard', element: Dashboard },
  
  // Management Routes
  { path: '/organizations', name: 'Organizations', element: Organizations },
  { path: '/organizations/:id', name: 'Organization Detail', element: OrganizationDetail },
  { path: '/users', name: 'Users', element: Users },
  { path: '/users/:id', name: 'User Detail', element: UserDetail },
  { path: '/roles', name: 'Roles & Permissions', element: Roles },
  { path: '/roles/:id', name: 'Role Detail', element: RoleDetail },
  
  // Billing Routes
  { path: '/subscriptions', name: 'Subscriptions', element: Subscriptions },
  { path: '/subscriptions/:id', name: 'Subscription Detail', element: SubscriptionDetail },
  { path: '/payments', name: 'Payments', element: Payments },
  { path: '/payments/:id', name: 'Payment Detail', element: PaymentDetail },
  { path: '/invoices', name: 'Invoices', element: Invoices },
  { path: '/invoices/:id', name: 'Invoice Detail', element: InvoiceDetail },
  
  // Operations Routes
  { path: '/approvals', name: 'Approvals', element: Approvals },
  { path: '/approvals/:id', name: 'Approval Detail', element: ApprovalDetail },
  { path: '/notifications', name: 'Notifications', element: Notifications },
  { path: '/notifications/:id', name: 'Notification Detail', element: NotificationDetail },
  { path: '/reports', name: 'Reports', element: Reports },
  
  // Configuration Routes
  { path: '/webhooks', name: 'Webhooks', element: Webhooks },
  { path: '/webhooks/:id', name: 'Webhook Detail', element: WebhookDetail },
  { path: '/settings', name: 'Settings', element: Settings },
]

export default routes
