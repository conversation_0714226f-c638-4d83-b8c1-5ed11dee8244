<?php

namespace Tests\Feature\RolesPermissions;

use Tests\TestCase;
use Tests\Support\CreatesOrgUser;
use Illuminate\Foundation\Testing\RefreshDatabase;

class RolesPermissionsApiTest extends TestCase
{
    use RefreshDatabase, CreatesOrgUser;

    public function test_roles_permissions_crud_and_attach(): void
    {
        $org = $this->createOrganization();
        $user = $this->createUserInOrg($org);
        $this->actingAs($user);
        $this->grantPermission($user, 'roles.create');
        $this->grantPermission($user, 'roles.update');
        $this->grantPermission($user, 'roles.delete');
        $this->grantPermission($user, 'roles.view');
        $this->grantPermission($user, 'permissions.create');
        $this->grantPermission($user, 'permissions.update');
        $this->grantPermission($user, 'permissions.delete');
        $this->grantPermission($user, 'permissions.view');

        // Create permission
        $perm = $this->postJson('/api/v1/permissions', [
            'organization_id' => $org->id,
            'name' => 'Manage Users',
            'slug' => 'users.manage',
        ])->assertStatus(201)->json('id');

        // Create role
        $role = $this->postJson('/api/v1/roles', [
            'organization_id' => $org->id,
            'name' => 'Admin',
            'slug' => 'admin',
        ])->assertStatus(201)->json('id');

        // Attach permission
        $this->postJson("/api/v1/roles/{$role}/permissions/{$perm}")->assertStatus(200);
        // Detach permission
        $this->deleteJson("/api/v1/roles/{$role}/permissions/{$perm}")->assertStatus(200);

        // Index / Show / Update
        $this->getJson('/api/v1/roles')->assertStatus(200);
        $this->getJson('/api/v1/roles/'.$role)->assertStatus(200);
        $this->patchJson('/api/v1/roles/'.$role, ['description' => 'Updated'])->assertStatus(200)->assertJsonFragment(['description' => 'Updated']);

        // Delete
        $this->deleteJson('/api/v1/roles/'.$role)->assertStatus(200)->assertJsonFragment(['deleted' => true]);
    }
}
