<?php

use Illuminate\Support\Facades\Route;
use App\Modules\Users\Http\Controllers\AuthController;
use App\Modules\Users\Http\Controllers\UserController;

// Auth
Route::post('auth/register', [AuthController::class, 'register']);
Route::post('auth/login', [AuthController::class, 'login'])->middleware('throttle:5,1');
Route::post('auth/logout', [AuthController::class, 'logout']);
Route::post('auth/refresh-token', [AuthController::class, 'refreshToken']);
Route::post('auth/request-otp', [AuthController::class, 'requestOtp'])->middleware('throttle:5,1');
Route::post('auth/verify-otp', [AuthController::class, 'verifyOtp']);
Route::post('auth/reset-password', [AuthController::class, 'resetPassword']);
Route::post('auth/setup-mfa', [AuthController::class, 'setupMFA']);
Route::post('auth/verify-mfa', [AuthController::class, 'verifyMFA']);

// Me and Users (protected)
Route::middleware('auth')->group(function () {
    Route::get('me', [UserController::class, 'me']);
    Route::patch('me', [UserController::class, 'updateMe']);

    // Users CRUD
    Route::get('users', [UserController::class, 'index']);
Route::post('users', [UserController::class, 'store'])->middleware('check.permission:users.create');
    Route::get('users/{id}', [UserController::class, 'show']);
Route::patch('users/{id}', [UserController::class, 'update'])->middleware('check.permission:users.update');
Route::delete('users/{id}', [UserController::class, 'destroy'])->middleware('check.permission:users.delete');

    // Extra endpoints
Route::post('users/bulk-import', [UserController::class, 'bulkImport'])->middleware('check.permission:users.create');
    Route::get('users/{id}/activity', [UserController::class, 'activity']);
    Route::patch('users/{id}/status', [UserController::class, 'changeStatus']);
});
