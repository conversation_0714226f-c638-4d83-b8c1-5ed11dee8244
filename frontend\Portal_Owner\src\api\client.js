import authService from '../services/authService'

// Use environment variable for API URL, with fallback for development
const API_URL = import.meta.env.VITE_API_URL || 'http://127.0.0.1:8000/api/v1'

const client = {
  async request(method, url, data = null, params = null) {
    const token = authService.getToken()
    
    // Fix #4: Validate token before making request (but allow login/register endpoints)
    if (!token && !url.includes('/auth/login') && !url.includes('/auth/register')) {
      window.location.href = '/login'
      return
    }
    
    const headers = {
      'Content-Type': 'application/json',
    }
    
    // Only add Authorization header if token exists
    if (token) {
      headers.Authorization = `Bearer ${token}`
    }

    const options = {
      method,
      headers,
    }

    if (data) {
      options.body = JSON.stringify(data)
    }

    // Build query string from params
    let fullUrl = `${API_URL}${url}`
    if (params) {
      const queryString = new URLSearchParams(params).toString()
      if (queryString) {
        fullUrl += `?${queryString}`
      }
    }

    try {
      const response = await fetch(fullUrl, options)
      
      if (response.status === 401) {
        // Session expired or unauthorized
        authService.handleSessionExpired()
        return
      }
      
      if (!response.ok) {
        const error = await response.json()
        throw error
      }
      
      return response
    } catch (error) {
      // Handle CORS and network errors
      if (error instanceof TypeError && error.message === 'Failed to fetch') {
        const corsError = new Error('Network error. Please check your connection and ensure the API server is running.')
        corsError.isCorsError = true
        throw corsError
      }
      throw error
    }
  },

  get(url, options = {}) {
    return this.request('GET', url, null, options.params)
  },

  post(url, data) {
    return this.request('POST', url, data)
  },

  patch(url, data) {
    return this.request('PATCH', url, data)
  },

  delete(url) {
    return this.request('DELETE', url)
  },
}

export default client
