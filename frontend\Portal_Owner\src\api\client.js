const API_URL = 'http://localhost:8000/api/v1'

const client = {
  async request(method, url, data = null) {
    const token = localStorage.getItem('authToken')
    const headers = {
      'Content-Type': 'application/json',
    }
    if (token) {
      headers.Authorization = `Bearer ${token}`
    }

    const options = {
      method,
      headers,
    }

    if (data) {
      options.body = JSON.stringify(data)
    }

    try {
      const response = await fetch(`${API_URL}${url}`, options)
      
      if (response.status === 401) {
        try {
          const refreshToken = localStorage.getItem('refreshToken')
          const refreshResponse = await fetch(`${API_URL}/auth/refresh-token`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ refresh_token: refreshToken }),
          })
          
          if (refreshResponse.ok) {
            const refreshData = await refreshResponse.json()
            const newToken = refreshData.data.token
            localStorage.setItem('authToken', newToken)
            
            headers.Authorization = `Bearer ${newToken}`
            options.headers = headers
            return fetch(`${API_URL}${url}`, options)
          }
        } catch (err) {
          localStorage.removeItem('authToken')
          localStorage.removeItem('refreshToken')
          window.location.href = '/login'
        }
      }
      
      if (!response.ok) {
        const error = await response.json()
        throw error
      }
      
      return response
    } catch (error) {
      throw error
    }
  },

  get(url) {
    return this.request('GET', url)
  },

  post(url, data) {
    return this.request('POST', url, data)
  },

  patch(url, data) {
    return this.request('PATCH', url, data)
  },

  delete(url) {
    return this.request('DELETE', url)
  },
}

export default client
