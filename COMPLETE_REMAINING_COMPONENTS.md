# Complete Remaining Components - Copy & Paste Ready

## Status: Creating all 15 remaining components

### PAYMENTS.JS - Copy this entire file

```javascript
import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { CCard, CCardBody, CCardHeader, CCol, CRow, CButton, CTable, CTableBody, CTableDataCell, CTableHead, CTableHeaderCell, CTableRow, CSpinner, CAlert, CBadge } from '@coreui/react'
import CIcon from '@coreui/icons-react'
import { cilPlus, cilPencil, cilTrash } from '@coreui/icons'
import { billingAPI } from '../../api/billing'

const Payments = () => {
  const navigate = useNavigate()
  const [payments, setPayments] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => { loadPayments() }, [])

  const loadPayments = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await billingAPI.getPayments()
      setPayments(response.data.data || [])
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to load payments')
      console.error('Error:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async (id) => {
    if (window.confirm('Are you sure?')) {
      try {
        await billingAPI.deletePayment(id)
        setPayments(payments.filter((p) => p.id !== id))
      } catch (err) {
        setError(err.response?.data?.message || 'Failed to delete')
      }
    }
  }

  return (
    <CRow>
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardHeader>
            <div className="d-flex justify-content-between align-items-center">
              <strong>Payments</strong>
              <CButton color="primary" size="sm" onClick={() => navigate('/payments/new')}>
                <CIcon icon={cilPlus} className="me-2" />
                Record Payment
              </CButton>
            </div>
          </CCardHeader>
          <CCardBody>
            {error && <CAlert color="danger">{error}</CAlert>}
            {loading ? (
              <div className="text-center"><CSpinner color="primary" /></div>
            ) : (
              <CTable hover responsive>
                <CTableHead>
                  <CTableRow>
                    <CTableHeaderCell scope="col">#</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Amount</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Method</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Status</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Date</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Actions</CTableHeaderCell>
                  </CTableRow>
                </CTableHead>
                <CTableBody>
                  {payments.length === 0 ? (
                    <CTableRow>
                      <CTableDataCell colSpan="6" className="text-center text-muted">
                        No payments found.
                      </CTableDataCell>
                    </CTableRow>
                  ) : (
                    payments.map((payment, idx) => (
                      <CTableRow key={payment.id}>
                        <CTableDataCell>{idx + 1}</CTableDataCell>
                        <CTableDataCell>${payment.amount || 0}</CTableDataCell>
                        <CTableDataCell>{payment.payment_method || '-'}</CTableDataCell>
                        <CTableDataCell><CBadge color={payment.status === 'completed' ? 'success' : 'warning'}>{payment.status}</CBadge></CTableDataCell>
                        <CTableDataCell>{payment.created_at || '-'}</CTableDataCell>
                        <CTableDataCell>
                          <CButton color="info" size="sm" className="me-2" onClick={() => navigate(`/payments/${payment.id}`)}><CIcon icon={cilPencil} /></CButton>
                          <CButton color="danger" size="sm" onClick={() => handleDelete(payment.id)}><CIcon icon={cilTrash} /></CButton>
                        </CTableDataCell>
                      </CTableRow>
                    ))
                  )}
                </CTableBody>
              </CTable>
            )}
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  )
}

export default Payments
```

### INVOICES.JS - Copy this entire file

```javascript
import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { CCard, CCardBody, CCardHeader, CCol, CRow, CButton, CTable, CTableBody, CTableDataCell, CTableHead, CTableHeaderCell, CTableRow, CSpinner, CAlert, CBadge } from '@coreui/react'
import CIcon from '@coreui/icons-react'
import { cilPlus, cilPencil, cilTrash } from '@coreui/icons'
import { billingAPI } from '../../api/billing'

const Invoices = () => {
  const navigate = useNavigate()
  const [invoices, setInvoices] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => { loadInvoices() }, [])

  const loadInvoices = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await billingAPI.getInvoices()
      setInvoices(response.data.data || [])
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to load invoices')
      console.error('Error:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async (id) => {
    if (window.confirm('Are you sure?')) {
      try {
        await billingAPI.deleteInvoice(id)
        setInvoices(invoices.filter((i) => i.id !== id))
      } catch (err) {
        setError(err.response?.data?.message || 'Failed to delete')
      }
    }
  }

  return (
    <CRow>
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardHeader>
            <div className="d-flex justify-content-between align-items-center">
              <strong>Invoices</strong>
              <CButton color="primary" size="sm" onClick={() => navigate('/invoices/new')}>
                <CIcon icon={cilPlus} className="me-2" />
                Create Invoice
              </CButton>
            </div>
          </CCardHeader>
          <CCardBody>
            {error && <CAlert color="danger">{error}</CAlert>}
            {loading ? (
              <div className="text-center"><CSpinner color="primary" /></div>
            ) : (
              <CTable hover responsive>
                <CTableHead>
                  <CTableRow>
                    <CTableHeaderCell scope="col">#</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Invoice #</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Amount</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Status</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Due Date</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Actions</CTableHeaderCell>
                  </CTableRow>
                </CTableHead>
                <CTableBody>
                  {invoices.length === 0 ? (
                    <CTableRow>
                      <CTableDataCell colSpan="6" className="text-center text-muted">
                        No invoices found.
                      </CTableDataCell>
                    </CTableRow>
                  ) : (
                    invoices.map((invoice, idx) => (
                      <CTableRow key={invoice.id}>
                        <CTableDataCell>{idx + 1}</CTableDataCell>
                        <CTableDataCell>{invoice.invoice_number || '-'}</CTableDataCell>
                        <CTableDataCell>${invoice.amount || 0}</CTableDataCell>
                        <CTableDataCell><CBadge color={invoice.status === 'paid' ? 'success' : 'warning'}>{invoice.status}</CBadge></CTableDataCell>
                        <CTableDataCell>{invoice.due_date || '-'}</CTableDataCell>
                        <CTableDataCell>
                          <CButton color="info" size="sm" className="me-2" onClick={() => navigate(`/invoices/${invoice.id}`)}><CIcon icon={cilPencil} /></CButton>
                          <CButton color="danger" size="sm" onClick={() => handleDelete(invoice.id)}><CIcon icon={cilTrash} /></CButton>
                        </CTableDataCell>
                      </CTableRow>
                    ))
                  )}
                </CTableBody>
              </CTable>
            )}
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  )
}

export default Invoices
```

### APPROVALS.JS - Copy this entire file

```javascript
import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { CCard, CCardBody, CCardHeader, CCol, CRow, CButton, CTable, CTableBody, CTableDataCell, CTableHead, CTableHeaderCell, CTableRow, CSpinner, CAlert, CBadge } from '@coreui/react'
import CIcon from '@coreui/icons-react'
import { cilPlus, cilPencil, cilTrash } from '@coreui/icons'
import { approvalsAPI } from '../../api/approvals'

const Approvals = () => {
  const navigate = useNavigate()
  const [approvals, setApprovals] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => { loadApprovals() }, [])

  const loadApprovals = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await approvalsAPI.getApprovals()
      setApprovals(response.data.data || [])
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to load approvals')
      console.error('Error:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async (id) => {
    if (window.confirm('Are you sure?')) {
      try {
        await approvalsAPI.deleteApproval(id)
        setApprovals(approvals.filter((a) => a.id !== id))
      } catch (err) {
        setError(err.response?.data?.message || 'Failed to delete')
      }
    }
  }

  return (
    <CRow>
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardHeader>
            <div className="d-flex justify-content-between align-items-center">
              <strong>Approvals</strong>
              <CButton color="primary" size="sm" onClick={() => navigate('/approvals/new')}>
                <CIcon icon={cilPlus} className="me-2" />
                New Approval
              </CButton>
            </div>
          </CCardHeader>
          <CCardBody>
            {error && <CAlert color="danger">{error}</CAlert>}
            {loading ? (
              <div className="text-center"><CSpinner color="primary" /></div>
            ) : (
              <CTable hover responsive>
                <CTableHead>
                  <CTableRow>
                    <CTableHeaderCell scope="col">#</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Entity</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Status</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Created</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Actions</CTableHeaderCell>
                  </CTableRow>
                </CTableHead>
                <CTableBody>
                  {approvals.length === 0 ? (
                    <CTableRow>
                      <CTableDataCell colSpan="5" className="text-center text-muted">
                        No approvals found.
                      </CTableDataCell>
                    </CTableRow>
                  ) : (
                    approvals.map((approval, idx) => (
                      <CTableRow key={approval.id}>
                        <CTableDataCell>{idx + 1}</CTableDataCell>
                        <CTableDataCell>{approval.entity_type || '-'}</CTableDataCell>
                        <CTableDataCell><CBadge color={approval.status === 'pending' ? 'warning' : 'success'}>{approval.status}</CBadge></CTableDataCell>
                        <CTableDataCell>{approval.created_at || '-'}</CTableDataCell>
                        <CTableDataCell>
                          <CButton color="info" size="sm" className="me-2" onClick={() => navigate(`/approvals/${approval.id}`)}><CIcon icon={cilPencil} /></CButton>
                          <CButton color="danger" size="sm" onClick={() => handleDelete(approval.id)}><CIcon icon={cilTrash} /></CButton>
                        </CTableDataCell>
                      </CTableRow>
                    ))
                  )}
                </CTableBody>
              </CTable>
            )}
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  )
}

export default Approvals
```

### NOTIFICATIONS.JS - Copy this entire file

```javascript
import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { CCard, CCardBody, CCardHeader, CCol, CRow, CButton, CTable, CTableBody, CTableDataCell, CTableHead, CTableHeaderCell, CTableRow, CSpinner, CAlert, CBadge } from '@coreui/react'
import CIcon from '@coreui/icons-react'
import { cilPencil, cilTrash } from '@coreui/icons'
import { notificationsAPI } from '../../api/notifications'

const Notifications = () => {
  const navigate = useNavigate()
  const [notifications, setNotifications] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => { loadNotifications() }, [])

  const loadNotifications = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await notificationsAPI.getNotifications()
      setNotifications(response.data.data || [])
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to load notifications')
      console.error('Error:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async (id) => {
    if (window.confirm('Are you sure?')) {
      try {
        await notificationsAPI.markAsRead(id)
        setNotifications(notifications.filter((n) => n.id !== id))
      } catch (err) {
        setError(err.response?.data?.message || 'Failed to delete')
      }
    }
  }

  return (
    <CRow>
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardHeader>
            <strong>Notifications</strong>
          </CCardHeader>
          <CCardBody>
            {error && <CAlert color="danger">{error}</CAlert>}
            {loading ? (
              <div className="text-center"><CSpinner color="primary" /></div>
            ) : (
              <CTable hover responsive>
                <CTableHead>
                  <CTableRow>
                    <CTableHeaderCell scope="col">#</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Message</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Type</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Status</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Date</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Actions</CTableHeaderCell>
                  </CTableRow>
                </CTableHead>
                <CTableBody>
                  {notifications.length === 0 ? (
                    <CTableRow>
                      <CTableDataCell colSpan="6" className="text-center text-muted">
                        No notifications found.
                      </CTableDataCell>
                    </CTableRow>
                  ) : (
                    notifications.map((notif, idx) => (
                      <CTableRow key={notif.id}>
                        <CTableDataCell>{idx + 1}</CTableDataCell>
                        <CTableDataCell>{notif.message || '-'}</CTableDataCell>
                        <CTableDataCell>{notif.type || '-'}</CTableDataCell>
                        <CTableDataCell><CBadge color={notif.read_at ? 'secondary' : 'primary'}>{ notif.read_at ? 'Read' : 'Unread'}</CBadge></CTableDataCell>
                        <CTableDataCell>{notif.created_at || '-'}</CTableDataCell>
                        <CTableDataCell>
                          <CButton color="info" size="sm" className="me-2" onClick={() => navigate(`/notifications/${notif.id}`)}><CIcon icon={cilPencil} /></CButton>
                          <CButton color="danger" size="sm" onClick={() => handleDelete(notif.id)}><CIcon icon={cilTrash} /></CButton>
                        </CTableDataCell>
                      </CTableRow>
                    ))
                  )}
                </CTableBody>
              </CTable>
            )}
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  )
}

export default Notifications
```

### WEBHOOKS.JS - Copy this entire file

```javascript
import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { CCard, CCardBody, CCardHeader, CCol, CRow, CButton, CTable, CTableBody, CTableDataCell, CTableHead, CTableHeaderCell, CTableRow, CSpinner, CAlert, CBadge } from '@coreui/react'
import CIcon from '@coreui/icons-react'
import { cilPlus, cilPencil, cilTrash } from '@coreui/icons'
import { webhooksAPI } from '../../api/webhooks'

const Webhooks = () => {
  const navigate = useNavigate()
  const [webhooks, setWebhooks] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => { loadWebhooks() }, [])

  const loadWebhooks = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await webhooksAPI.getWebhooks()
      setWebhooks(response.data.data || [])
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to load webhooks')
      console.error('Error:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async (id) => {
    if (window.confirm('Are you sure?')) {
      try {
        await webhooksAPI.deleteWebhook(id)
        setWebhooks(webhooks.filter((w) => w.id !== id))
      } catch (err) {
        setError(err.response?.data?.message || 'Failed to delete')
      }
    }
  }

  return (
    <CRow>
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardHeader>
            <div className="d-flex justify-content-between align-items-center">
              <strong>Webhooks</strong>
              <CButton color="primary" size="sm" onClick={() => navigate('/webhooks/new')}>
                <CIcon icon={cilPlus} className="me-2" />
                Add Webhook
              </CButton>
            </div>
          </CCardHeader>
          <CCardBody>
            {error && <CAlert color="danger">{error}</CAlert>}
            {loading ? (
              <div className="text-center"><CSpinner color="primary" /></div>
            ) : (
              <CTable hover responsive>
                <CTableHead>
                  <CTableRow>
                    <CTableHeaderCell scope="col">#</CTableHeaderCell>
                    <CTableHeaderCell scope="col">URL</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Events</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Status</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Actions</CTableHeaderCell>
                  </CTableRow>
                </CTableHead>
                <CTableBody>
                  {webhooks.length === 0 ? (
                    <CTableRow>
                      <CTableDataCell colSpan="5" className="text-center text-muted">
                        No webhooks found.
                      </CTableDataCell>
                    </CTableRow>
                  ) : (
                    webhooks.map((webhook, idx) => (
                      <CTableRow key={webhook.id}>
                        <CTableDataCell>{idx + 1}</CTableDataCell>
                        <CTableDataCell>{webhook.url || '-'}</CTableDataCell>
                        <CTableDataCell>{webhook.events_count || 0}</CTableDataCell>
                        <CTableDataCell><CBadge color={webhook.active ? 'success' : 'secondary'}>{webhook.active ? 'Active' : 'Inactive'}</CBadge></CTableDataCell>
                        <CTableDataCell>
                          <CButton color="info" size="sm" className="me-2" onClick={() => navigate(`/webhooks/${webhook.id}`)}><CIcon icon={cilPencil} /></CButton>
                          <CButton color="danger" size="sm" onClick={() => handleDelete(webhook.id)}><CIcon icon={cilTrash} /></CButton>
                        </CTableDataCell>
                      </CTableRow>
                    ))
                  )}
                </CTableBody>
              </CTable>
            )}
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  )
}

export default Webhooks
```

## NEXT: Create all 7 detail pages using same pattern

Each detail page follows this structure:
1. Import hooks, components, API
2. Get id from params
3. Load data on mount
4. Create form with fields
5. Handle submit
6. Show loading/error states

Use the templates from RAPID_COMPLETION_GUIDE.md for each detail page.

