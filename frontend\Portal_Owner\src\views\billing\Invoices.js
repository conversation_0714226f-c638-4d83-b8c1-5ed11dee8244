import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { CCard, CCardBody, CCardHeader, CCol, CRow, CButton, CTable, CTableBody, CTableDataCell, CTableHead, CTableHeaderCell, CTableRow, CSpinner, CAlert, CBadge } from '@coreui/react'
import CIcon from '@coreui/icons-react'
import { cilPlus, cilPencil, cilTrash } from '@coreui/icons'
import { billingAPI } from '../../api/billing'

const Invoices = () => {
  const navigate = useNavigate()
  const [invoices, setInvoices] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => { loadInvoices() }, [])

  const loadInvoices = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await billingAPI.getInvoices()
      setInvoices(response.data.data || [])
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to load invoices')
      console.error('Error:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async (id) => {
    if (window.confirm('Are you sure?')) {
      try {
        await billingAPI.deleteInvoice(id)
        setInvoices(invoices.filter((i) => i.id !== id))
      } catch (err) {
        setError(err.response?.data?.message || 'Failed to delete')
      }
    }
  }

  return (
    <CRow>
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardHeader>
            <div className="d-flex justify-content-between align-items-center">
              <strong>Invoices</strong>
              <CButton color="primary" size="sm" onClick={() => navigate('/invoices/new')}>
                <CIcon icon={cilPlus} className="me-2" />
                Create Invoice
              </CButton>
            </div>
          </CCardHeader>
          <CCardBody>
            {error && <CAlert color="danger">{error}</CAlert>}
            {loading ? (
              <div className="text-center"><CSpinner color="primary" /></div>
            ) : (
              <CTable hover responsive>
                <CTableHead>
                  <CTableRow>
                    <CTableHeaderCell scope="col">#</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Invoice #</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Amount</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Status</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Due Date</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Actions</CTableHeaderCell>
                  </CTableRow>
                </CTableHead>
                <CTableBody>
                  {invoices.length === 0 ? (
                    <CTableRow>
                      <CTableDataCell colSpan="6" className="text-center text-muted">
                        No invoices found.
                      </CTableDataCell>
                    </CTableRow>
                  ) : (
                    invoices.map((invoice, idx) => (
                      <CTableRow key={invoice.id}>
                        <CTableDataCell>{idx + 1}</CTableDataCell>
                        <CTableDataCell>{invoice.invoice_number || '-'}</CTableDataCell>
                        <CTableDataCell>${invoice.amount || 0}</CTableDataCell>
                        <CTableDataCell><CBadge color={invoice.status === 'paid' ? 'success' : 'warning'}>{invoice.status}</CBadge></CTableDataCell>
                        <CTableDataCell>{invoice.due_date || '-'}</CTableDataCell>
                        <CTableDataCell>
                          <CButton color="info" size="sm" className="me-2" onClick={() => navigate(`/invoices/${invoice.id}`)}><CIcon icon={cilPencil} /></CButton>
                          <CButton color="danger" size="sm" onClick={() => handleDelete(invoice.id)}><CIcon icon={cilTrash} /></CButton>
                        </CTableDataCell>
                      </CTableRow>
                    ))
                  )}
                </CTableBody>
              </CTable>
            )}
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  )
}

export default Invoices
