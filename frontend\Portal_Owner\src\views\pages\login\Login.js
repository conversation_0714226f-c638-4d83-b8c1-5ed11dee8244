import React, { useState } from 'react'
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom'
import {
  CButton,
  CCard,
  CCardBody,
  CCardGroup,
  CCol,
  CContainer,
  CForm,
  CFormInput,
  CInputGroup,
  CInputGroupText,
  CRow,
  CAlert,
  CSpinner,
} from '@coreui/react'
import CIcon from '@coreui/icons-react'
import { cilLockLocked, cilUser } from '@coreui/icons'
import { authAPI } from '../../../api/auth'
import authService from '../../../services/authService'

const Login = () => {
  const navigate = useNavigate()
  const [email, setEmail] = useState('<EMAIL>')
  const [password, setPassword] = useState('Admin@123456')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [success, setSuccess] = useState(false)

  const handleLogin = async (e) => {
    e.preventDefault()
    try {
      setLoading(true)
      setError(null)
      setSuccess(false)

      const response = await authAPI.login(email, password)
      const jsonData = await response.json()
      
      // Handle both response structures
      let token, refresh_token
      if (jsonData.data && jsonData.data.token) {
        token = jsonData.data.token
        refresh_token = jsonData.data.refresh_token || jsonData.data.token  // Use token as fallback
      } else if (jsonData.token) {
        token = jsonData.token
        refresh_token = jsonData.refresh_token || jsonData.token  // Use token as fallback
      } else {
        throw new Error('Invalid response structure: missing token')
      }

      // Store tokens using auth service
      authService.setTokens(token, refresh_token)

      setSuccess(true)
      
      // Redirect to dashboard after 1 second
      setTimeout(() => {
        navigate('/dashboard')
      }, 1000)
    } catch (err) {
      const errorMessage = err.message || 'Login failed. Please check your credentials.'
      setError(errorMessage)
      console.error('Login error:', err)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="bg-body-tertiary min-vh-100 d-flex flex-row align-items-center">
      <CContainer>
        <CRow className="justify-content-center">
          <CCol md={8}>
            <CCardGroup>
              <CCard className="p-4">
                <CCardBody>
                  {error && <CAlert color="danger">{error}</CAlert>}
                  {success && <CAlert color="success">Login successful! Redirecting...</CAlert>}
                  
                  <CForm onSubmit={handleLogin}>
                    <h1>Portal Owner Login</h1>
                    <p className="text-body-secondary">Sign In to your account</p>
                    
                    <CInputGroup className="mb-3">
                      <CInputGroupText>
                        <CIcon icon={cilUser} />
                      </CInputGroupText>
                      <CFormInput
                        placeholder="Email"
                        autoComplete="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        disabled={loading}
                        required
                      />
                    </CInputGroup>
                    
                    <CInputGroup className="mb-4">
                      <CInputGroupText>
                        <CIcon icon={cilLockLocked} />
                      </CInputGroupText>
                      <CFormInput
                        type="password"
                        placeholder="Password"
                        autoComplete="current-password"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        disabled={loading}
                        required
                      />
                    </CInputGroup>
                    
                    <CRow>
                      <CCol xs={6}>
                        <CButton 
                          type="submit"
                          color="primary" 
                          className="px-4"
                          disabled={loading}
                        >
                          {loading ? (
                            <>
                              <CSpinner size="sm" className="me-2" />
                              Logging in...
                            </>
                          ) : (
                            'Login'
                          )}
                        </CButton>
                      </CCol>
                      <CCol xs={6} className="text-right">
                        <CButton color="link" className="px-0">
                          Forgot password?
                        </CButton>
                      </CCol>
                    </CRow>
                  </CForm>
                </CCardBody>
              </CCard>
              
              <CCard className="text-white bg-primary py-5" style={{ width: '44%' }}>
                <CCardBody className="text-center">
                  <div>
                    <h2>Demo Credentials</h2>
                    <p className="mt-4">
                      <strong>Email:</strong><br />
                      <EMAIL>
                    </p>
                    <p>
                      <strong>Password:</strong><br />
                      Admin@123456
                    </p>
                    <p className="text-muted small mt-4">
                      Use these credentials to test the portal owner functionality
                    </p>
                  </div>
                </CCardBody>
              </CCard>
            </CCardGroup>
          </CCol>
        </CRow>
      </CContainer>
    </div>
  )
}

export default Login
