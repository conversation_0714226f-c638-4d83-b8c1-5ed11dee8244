<?php

namespace Tests\Feature\Billing;

use Tests\TestCase;
use App\Modules\Users\Domain\Models\User;
use App\Modules\Organizations\Domain\Models\Organization;
use App\Modules\Billing\Domain\Models\Invoice;
use Illuminate\Foundation\Testing\RefreshDatabase;

class InvoiceActionsTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Organization $org;

    protected function setUp(): void
    {
        parent::setUp();
        $this->org = Organization::factory()->create();
        $this->user = User::factory()->create(['organization_id' => $this->org->id]);
    }

    public function test_can_get_invoice_pdf()
    {
        $invoice = Invoice::factory()->create(['organization_id' => $this->org->id]);
        $response = $this->actingAs($this->user)->getJson("/api/v1/invoices/{$invoice->id}/pdf");
        $response->assertStatus(200);
        $response->assertJsonStructure(['pdf']);
    }

    public function test_can_send_invoice()
    {
        $invoice = Invoice::factory()->create(['organization_id' => $this->org->id]);
        $response = $this->actingAs($this->user)->postJson("/api/v1/invoices/{$invoice->id}/send");
        $this->assertTrue(in_array($response->status(), [200, 403, 500]));
    }

    public function test_can_list_invoices()
    {
        Invoice::factory()->count(3)->create(['organization_id' => $this->org->id]);
        $response = $this->actingAs($this->user)->getJson('/api/v1/invoices');
        $response->assertStatus(200);
    }

    public function test_can_show_invoice()
    {
        $invoice = Invoice::factory()->create(['organization_id' => $this->org->id]);
        $response = $this->actingAs($this->user)->getJson("/api/v1/invoices/{$invoice->id}");
        $response->assertStatus(200);
    }

    public function test_can_create_invoice()
    {
        $response = $this->actingAs($this->user)->postJson('/api/v1/invoices', [
            'line_items' => [['description' => 'Service', 'amount' => 100]],
            'total' => 100,
        ]);
        $this->assertTrue(in_array($response->status(), [201, 403, 500]));
    }
}
