<?php

namespace App\Modules\Shared\Domain\Services;

/**
 * ConfigurationService Class
 * 
 * Centralized configuration access (Rule 44)
 * All configuration MUST be accessed through this service
 */
class ConfigurationService
{
    /**
     * Check if a module is enabled
     *
     * @param string $module Module name
     * @return bool
     */
    public static function isModuleEnabled(string $module): bool
    {
        $enabled = config('modules.enabled', []);
        
        if (!in_array($module, $enabled)) {
            return false;
        }

        return config("modules.{$module}.enabled", true);
    }

    /**
     * Check if a feature is enabled for a module
     *
     * @param string $module Module name
     * @param string $feature Feature name
     * @return bool
     */
    public static function isFeatureEnabled(string $module, string $feature): bool
    {
        if (!self::isModuleEnabled($module)) {
            return false;
        }

        return config("modules.{$module}.features.{$feature}", false);
    }

    /**
     * Get a setting value for a module
     *
     * @param string $module Module name
     * @param string $setting Setting name
     * @param mixed $default Default value
     * @return mixed
     */
    public static function getSetting(string $module, string $setting, $default = null)
    {
        return config("modules.{$module}.settings.{$setting}", $default);
    }

    /**
     * Get all configuration for a module
     *
     * @param string $module Module name
     * @return array
     */
    public static function getModuleConfig(string $module): array
    {
        return config("modules.{$module}", []);
    }

    /**
     * Get API configuration
     *
     * @return array
     */
    public static function getApiConfig(): array
    {
        return config('modules.api', []);
    }

    /**
     * Get pagination configuration
     *
     * @return array
     */
    public static function getPaginationConfig(): array
    {
        return config('modules.api.pagination', [
            'default_per_page' => 15,
            'max_per_page' => 100,
        ]);
    }

    /**
     * Check if a global feature is enabled
     *
     * @param string $feature Feature name
     * @return bool
     */
    public static function isGlobalFeatureEnabled(string $feature): bool
    {
        return config("modules.features.{$feature}", false);
    }
}
