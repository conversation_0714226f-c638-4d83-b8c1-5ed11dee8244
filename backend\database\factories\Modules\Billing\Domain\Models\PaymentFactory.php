<?php

namespace Database\Factories\Modules\Billing\Domain\Models;

use App\Modules\Billing\Domain\Models\Payment;
use App\Modules\Organizations\Domain\Models\Organization;
use Illuminate\Database\Eloquent\Factories\Factory;

class PaymentFactory extends Factory
{
    protected $model = Payment::class;

    public function definition(): array
    {
        return [
            'id' => $this->faker->uuid(),
            'organization_id' => Organization::factory(),
            'subscription_id' => null,
            'invoice_id' => null,
            'payment_method_id' => null,
            'amount' => $this->faker->randomFloat(2, 10, 1000),
            'currency' => 'USD',
            'status' => 'completed',
            'provider' => 'manual',
            'provider_transaction_id' => null,
            'paid_at' => now(),
            'metadata' => [],
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}
