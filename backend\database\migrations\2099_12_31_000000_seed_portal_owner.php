<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::transaction(function () {
            // Check if portal owner already exists
            $existingPortalOwner = DB::table('organizations')
                ->where('type', 'portal_owner')
                ->where('parent_id', null)
                ->first();

            if ($existingPortalOwner) {
                return; // Portal owner already exists
            }

            // Generate UUIDs
            $portalOwnerId = Str::uuid()->toString();
            $adminUserId = Str::uuid()->toString();
            $adminRoleId = Str::uuid()->toString();

            // 1. Create Portal Owner Organization
            DB::table('organizations')->insert([
                'id' => $portalOwnerId,
                'parent_id' => null,
                'type' => 'portal_owner',
                'name' => 'Portal Owner',
                'code' => 'PORTAL-' . strtoupper(Str::random(6)),
                'description' => 'Main Portal Owner Organization - System Administrator',
                'email' => '<EMAIL>',
                'phone' => '******-0000000',
                'address' => '123 Admin Street',
                'city' => 'Admin City',
                'state' => 'Admin State',
                'country' => 'United States',
                'postal_code' => '00000',
                'timezone' => 'UTC',
                'currency' => 'USD',
                'language' => 'en',
                'gstin' => 'PORTAL000001',
                'legal_name' => 'Portal Owner Administration',
                'billing_address' => '123 Admin Street, Admin City, Admin State 00000',
                'billing_city' => 'Admin City',
                'billing_state' => 'Admin State',
                'billing_postal_code' => '00000',
                'billing_country' => 'United States',
                'status' => 'active',
                'approval_request_id' => null,
                'settings' => json_encode([
                    'theme' => 'light',
                    'notifications_enabled' => true,
                    'auto_logout_minutes' => 30,
                ]),
                'created_at' => now(),
                'updated_at' => now(),
                'deleted_at' => null,
            ]);

            // 2. Create Org Unit Closure (self-reference)
            DB::table('org_unit_closure')->insert([
                'ancestor_id' => $portalOwnerId,
                'descendant_id' => $portalOwnerId,
                'depth' => 0,
            ]);

            // 3. Create Admin User with all fields
            DB::table('users')->insert([
                'id' => $adminUserId,
                'organization_id' => $portalOwnerId,
                'name' => 'Portal Administrator',
                'email' => '<EMAIL>',
                'mobile' => '******-0000001',
                'password' => Hash::make('Admin@123456'), // Strong default password
                'status' => 'active',
                'approval_request_id' => null,
                'email_verified_at' => now(),
                'mobile_verified_at' => now(),
                'mfa_enabled' => false,
                'mfa_secret' => null,
                'failed_login_attempts' => 0,
                'locked_until' => null,
                'last_login_at' => null,
                'last_login_ip' => null,
                'avatar' => null,
                'timezone' => 'UTC',
                'language' => 'en',
                'preferences' => json_encode([
                    'items_per_page' => 25,
                    'date_format' => 'Y-m-d',
                    'time_format' => 'H:i:s',
                    'notifications' => true,
                    'email_notifications' => true,
                ]),
                'remember_token' => null,
                'created_at' => now(),
                'updated_at' => now(),
                'deleted_at' => null,
            ]);

            // 4. Create Admin Role
            DB::table('roles')->insert([
                'id' => $adminRoleId,
                'organization_id' => $portalOwnerId,
                'name' => 'Administrator',
                'slug' => 'administrator',
                'description' => 'Portal Owner Administrator with full system access and permissions',
                'is_system' => true,
                'created_at' => now(),
                'updated_at' => now(),
                'deleted_at' => null,
            ]);

            // 5. Create Permissions
            $permissions = [
                // Organizations
                ['name' => 'Create Organizations', 'slug' => 'create-organizations', 'module' => 'organizations', 'description' => 'Create new organizations'],
                ['name' => 'View Organizations', 'slug' => 'view-organizations', 'module' => 'organizations', 'description' => 'View organization details'],
                ['name' => 'Edit Organizations', 'slug' => 'edit-organizations', 'module' => 'organizations', 'description' => 'Edit organization information'],
                ['name' => 'Delete Organizations', 'slug' => 'delete-organizations', 'module' => 'organizations', 'description' => 'Delete organizations'],
                
                // Users
                ['name' => 'Create Users', 'slug' => 'create-users', 'module' => 'users', 'description' => 'Create new users'],
                ['name' => 'View Users', 'slug' => 'view-users', 'module' => 'users', 'description' => 'View user details'],
                ['name' => 'Edit Users', 'slug' => 'edit-users', 'module' => 'users', 'description' => 'Edit user information'],
                ['name' => 'Delete Users', 'slug' => 'delete-users', 'module' => 'users', 'description' => 'Delete users'],
                ['name' => 'Bulk Import Users', 'slug' => 'bulk-import-users', 'module' => 'users', 'description' => 'Import users in bulk'],
                ['name' => 'View User Activity', 'slug' => 'view-user-activity', 'module' => 'users', 'description' => 'View user activity logs'],
                ['name' => 'Change User Status', 'slug' => 'change-user-status', 'module' => 'users', 'description' => 'Change user status'],
                
                // Roles
                ['name' => 'Create Roles', 'slug' => 'create-roles', 'module' => 'roles', 'description' => 'Create new roles'],
                ['name' => 'View Roles', 'slug' => 'view-roles', 'module' => 'roles', 'description' => 'View role details'],
                ['name' => 'Edit Roles', 'slug' => 'edit-roles', 'module' => 'roles', 'description' => 'Edit role information'],
                ['name' => 'Delete Roles', 'slug' => 'delete-roles', 'module' => 'roles', 'description' => 'Delete roles'],
                
                // Permissions
                ['name' => 'Create Permissions', 'slug' => 'create-permissions', 'module' => 'permissions', 'description' => 'Create new permissions'],
                ['name' => 'View Permissions', 'slug' => 'view-permissions', 'module' => 'permissions', 'description' => 'View permission details'],
                ['name' => 'Edit Permissions', 'slug' => 'edit-permissions', 'module' => 'permissions', 'description' => 'Edit permission information'],
                ['name' => 'Delete Permissions', 'slug' => 'delete-permissions', 'module' => 'permissions', 'description' => 'Delete permissions'],
                ['name' => 'Manage Role Permissions', 'slug' => 'manage-role-permissions', 'module' => 'permissions', 'description' => 'Assign permissions to roles'],
                
                // Subscriptions
                ['name' => 'Create Subscriptions', 'slug' => 'create-subscriptions', 'module' => 'billing', 'description' => 'Create new subscriptions'],
                ['name' => 'View Subscriptions', 'slug' => 'view-subscriptions', 'module' => 'billing', 'description' => 'View subscription details'],
                ['name' => 'Edit Subscriptions', 'slug' => 'edit-subscriptions', 'module' => 'billing', 'description' => 'Edit subscription information'],
                ['name' => 'Delete Subscriptions', 'slug' => 'delete-subscriptions', 'module' => 'billing', 'description' => 'Delete subscriptions'],
                ['name' => 'Upgrade Subscriptions', 'slug' => 'upgrade-subscriptions', 'module' => 'billing', 'description' => 'Upgrade subscription plans'],
                ['name' => 'Downgrade Subscriptions', 'slug' => 'downgrade-subscriptions', 'module' => 'billing', 'description' => 'Downgrade subscription plans'],
                ['name' => 'Cancel Subscriptions', 'slug' => 'cancel-subscriptions', 'module' => 'billing', 'description' => 'Cancel subscriptions'],
                ['name' => 'Manage Add-ons', 'slug' => 'manage-addons', 'module' => 'billing', 'description' => 'Manage subscription add-ons'],
                
                // Payments
                ['name' => 'Create Payments', 'slug' => 'create-payments', 'module' => 'billing', 'description' => 'Create new payments'],
                ['name' => 'View Payments', 'slug' => 'view-payments', 'module' => 'billing', 'description' => 'View payment details'],
                ['name' => 'Edit Payments', 'slug' => 'edit-payments', 'module' => 'billing', 'description' => 'Edit payment information'],
                ['name' => 'Delete Payments', 'slug' => 'delete-payments', 'module' => 'billing', 'description' => 'Delete payments'],
                ['name' => 'Retry Payments', 'slug' => 'retry-payments', 'module' => 'billing', 'description' => 'Retry failed payments'],
                ['name' => 'Refund Payments', 'slug' => 'refund-payments', 'module' => 'billing', 'description' => 'Process payment refunds'],
                
                // Invoices
                ['name' => 'Create Invoices', 'slug' => 'create-invoices', 'module' => 'billing', 'description' => 'Create new invoices'],
                ['name' => 'View Invoices', 'slug' => 'view-invoices', 'module' => 'billing', 'description' => 'View invoice details'],
                ['name' => 'Edit Invoices', 'slug' => 'edit-invoices', 'module' => 'billing', 'description' => 'Edit invoice information'],
                ['name' => 'Delete Invoices', 'slug' => 'delete-invoices', 'module' => 'billing', 'description' => 'Delete invoices'],
                ['name' => 'Send Invoices', 'slug' => 'send-invoices', 'module' => 'billing', 'description' => 'Send invoices to customers'],
                ['name' => 'View Invoice PDF', 'slug' => 'view-invoice-pdf', 'module' => 'billing', 'description' => 'View invoice as PDF'],
                
                // Approvals
                ['name' => 'Create Approvals', 'slug' => 'create-approvals', 'module' => 'approvals', 'description' => 'Create approval requests'],
                ['name' => 'View Approvals', 'slug' => 'view-approvals', 'module' => 'approvals', 'description' => 'View approval details'],
                ['name' => 'Approve Requests', 'slug' => 'approve-requests', 'module' => 'approvals', 'description' => 'Approve pending requests'],
                ['name' => 'Reject Requests', 'slug' => 'reject-requests', 'module' => 'approvals', 'description' => 'Reject pending requests'],
                ['name' => 'Comment on Approvals', 'slug' => 'comment-approvals', 'module' => 'approvals', 'description' => 'Add comments to approval requests'],
                
                // Notifications
                ['name' => 'View Notifications', 'slug' => 'view-notifications', 'module' => 'notifications', 'description' => 'View notifications'],
                ['name' => 'Mark Notifications as Read', 'slug' => 'mark-notifications-read', 'module' => 'notifications', 'description' => 'Mark notifications as read'],
                ['name' => 'Delete Notifications', 'slug' => 'delete-notifications', 'module' => 'notifications', 'description' => 'Delete notifications'],
                
                // Reports
                ['name' => 'View Reports', 'slug' => 'view-reports', 'module' => 'reports', 'description' => 'View system reports'],
                ['name' => 'Export Reports', 'slug' => 'export-reports', 'module' => 'reports', 'description' => 'Export reports to CSV/PDF'],
                ['name' => 'View Audit Logs', 'slug' => 'view-audit-logs', 'module' => 'reports', 'description' => 'View audit trail logs'],
                
                // Webhooks
                ['name' => 'Create Webhooks', 'slug' => 'create-webhooks', 'module' => 'webhooks', 'description' => 'Create new webhooks'],
                ['name' => 'View Webhooks', 'slug' => 'view-webhooks', 'module' => 'webhooks', 'description' => 'View webhook details'],
                ['name' => 'Edit Webhooks', 'slug' => 'edit-webhooks', 'module' => 'webhooks', 'description' => 'Edit webhook configuration'],
                ['name' => 'Delete Webhooks', 'slug' => 'delete-webhooks', 'module' => 'webhooks', 'description' => 'Delete webhooks'],
                ['name' => 'View Webhook Events', 'slug' => 'view-webhook-events', 'module' => 'webhooks', 'description' => 'View webhook event history'],
                ['name' => 'Test Webhooks', 'slug' => 'test-webhooks', 'module' => 'webhooks', 'description' => 'Test webhook delivery'],
                
                // Settings
                ['name' => 'View Settings', 'slug' => 'view-settings', 'module' => 'settings', 'description' => 'View system settings'],
                ['name' => 'Edit Settings', 'slug' => 'edit-settings', 'module' => 'settings', 'description' => 'Edit system settings'],
                ['name' => 'Manage API Keys', 'slug' => 'manage-api-keys', 'module' => 'settings', 'description' => 'Manage API keys'],
                
                // Plans
                ['name' => 'Create Plans', 'slug' => 'create-plans', 'module' => 'billing', 'description' => 'Create new billing plans'],
                ['name' => 'View Plans', 'slug' => 'view-plans', 'module' => 'billing', 'description' => 'View plan details'],
                ['name' => 'Edit Plans', 'slug' => 'edit-plans', 'module' => 'billing', 'description' => 'Edit plan information'],
                ['name' => 'Delete Plans', 'slug' => 'delete-plans', 'module' => 'billing', 'description' => 'Delete plans'],
            ];

            $permissionIds = [];
            foreach ($permissions as $permission) {
                $permissionId = Str::uuid()->toString();
                $permissionIds[] = $permissionId;
                
                DB::table('permissions')->insert([
                    'id' => $permissionId,
                    'organization_id' => $portalOwnerId,
                    'name' => $permission['name'],
                    'slug' => $permission['slug'],
                    'description' => $permission['description'],
                    'module' => $permission['module'],
                    'created_at' => now(),
                    'updated_at' => now(),
                    'deleted_at' => null,
                ]);
            }

            // 6. Attach all permissions to admin role
            foreach ($permissionIds as $permissionId) {
                DB::table('role_permissions')->insert([
                    'role_id' => $adminRoleId,
                    'permission_id' => $permissionId,
                    'organization_id' => $portalOwnerId,
                    'created_at' => now(),
                ]);
            }

            // 7. Assign admin role to admin user
            DB::table('role_assignments')->insert([
                'id' => Str::uuid()->toString(),
                'organization_id' => $portalOwnerId,
                'user_id' => $adminUserId,
                'role_id' => $adminRoleId,
                'assigned_by' => null,
                'assigned_at' => now(),
                'expires_at' => null,
                'scope' => 'organization',
                'scope_id' => null,
                'created_at' => now(),
                'updated_at' => now(),
                'deleted_at' => null,
            ]);

            // 8. Create Portal Owner Plan
            $planId = Str::uuid()->toString();
            DB::table('plans')->insert([
                'id' => $planId,
                'organization_id' => $portalOwnerId,
                'name' => 'Portal Owner Plan',
                'slug' => 'portal-owner-plan',
                'description' => 'Unlimited plan for Portal Owner with full access to all modules and features',
                'price' => 0.00,
                'yearly_price' => 0.00,
                'billing_period' => 'monthly',
                'user_limit' => 0, // unlimited
                'storage_limit' => 0, // unlimited
                'sub_org_limit' => 0, // unlimited
                'hierarchy_depth_limit' => 0, // unlimited
                'api_calls_limit' => 0, // unlimited
                'modules' => json_encode([
                    'organizations',
                    'users',
                    'roles',
                    'permissions',
                    'billing',
                    'approvals',
                    'notifications',
                    'reports',
                    'webhooks',
                    'settings',
                    'audit',
                ]),
                'features' => json_encode([
                    'unlimited_users' => true,
                    'unlimited_storage' => true,
                    'unlimited_organizations' => true,
                    'api_access' => true,
                    'webhook_support' => true,
                    'advanced_reporting' => true,
                    'audit_logs' => true,
                    'mfa_support' => true,
                    'sso_support' => true,
                ]),
                'is_active' => true,
                'trial_days' => 0,
                'sort_order' => 0,
                'created_at' => now(),
                'updated_at' => now(),
                'deleted_at' => null,
            ]);

            // 9. Create Portal Owner Subscription
            DB::table('subscriptions')->insert([
                'id' => Str::uuid()->toString(),
                'organization_id' => $portalOwnerId,
                'plan_id' => $planId,
                'status' => 'active',
                'starts_at' => now(),
                'ends_at' => null,
                'trial_ends_at' => null,
                'cancelled_at' => null,
                'auto_renew' => true,
                'price' => 0.00,
                'billing_period' => 'monthly',
                'user_count' => 1,
                'sub_org_count' => 0,
                'storage_used' => 0,
                'hierarchy_depth' => 0,
                'metadata' => json_encode([
                    'subscription_type' => 'portal_owner',
                    'created_by' => 'system',
                    'auto_created' => true,
                ]),
                'add_ons' => json_encode([]),
                'created_at' => now(),
                'updated_at' => now(),
                'deleted_at' => null,
            ]);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::transaction(function () {
            // Find portal owner
            $portalOwner = DB::table('organizations')
                ->where('type', 'portal_owner')
                ->where('parent_id', null)
                ->first();

            if (!$portalOwner) {
                return;
            }

            // Delete in reverse order of dependencies
            DB::table('subscriptions')->where('organization_id', $portalOwner->id)->delete();
            DB::table('plans')->where('organization_id', $portalOwner->id)->delete();
            DB::table('role_assignments')->where('organization_id', $portalOwner->id)->delete();
            DB::table('role_permissions')->whereIn('role_id', 
                DB::table('roles')->where('organization_id', $portalOwner->id)->pluck('id')
            )->delete();
            DB::table('permissions')->where('organization_id', $portalOwner->id)->delete();
            DB::table('roles')->where('organization_id', $portalOwner->id)->delete();
            DB::table('users')->where('organization_id', $portalOwner->id)->delete();
            DB::table('org_unit_closure')->where('ancestor_id', $portalOwner->id)->delete();
            DB::table('organizations')->where('id', $portalOwner->id)->delete();
        });
    }
};
