import React, { useMemo, useState } from 'react'
import Calendar from 'react-calendar'
import 'react-calendar/dist/Calendar.css'
import { CCard, CCardBody, CCardHeader, CCol, CFormCheck, CFormSelect, CRow } from '@coreui/react'
import './calendar.scss'

const RTL_LOCALES = ['ar', 'he', 'fa']

const startOfWeek = (date, weekStartsOn = 0) => {
  const d = new Date(date)
  const day = d.getDay()
  const diff = (day < weekStartsOn ? 7 : 0) + day - weekStartsOn
  d.setDate(d.getDate() - diff)
  d.setHours(0, 0, 0, 0)
  return d
}

const endOfWeek = (date, weekStartsOn = 0) => {
  const s = startOfWeek(date, weekStartsOn)
  const e = new Date(s)
  e.setDate(s.getDate() + 6)
  e.setHours(23, 59, 59, 999)
  return e
}

const CalendarPage = () => {
  const [value, setValue] = useState(new Date())
  const [range, setRange] = useState(false)
  const [doubleView, setDoubleView] = useState(false)
  const [disableWeekends, setDisableWeekends] = useState(false)
  const [showWeekNumbers, setShowWeekNumbers] = useState(false)
  const [locale, setLocale] = useState('en-US')

  const tileDisabled = useMemo(() => {
    if (!disableWeekends) return undefined
    return ({ date, view }) => {
      if (view !== 'month') return false
      const day = date.getDay()
      return day === 0 || day === 6
    }
  }, [disableWeekends])

  const weekStartsOn = useMemo(() => {
    // ISO locales usually Monday start; US uses Sunday
    if (locale.startsWith('en-US') || locale === 'ar' || locale === 'he' || locale === 'fa') return 0
    return 1
  }, [locale])

  return (
    <CRow>
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardHeader>Calendar</CCardHeader>
          <CCardBody>
            <div className="d-flex flex-wrap align-items-center gap-4 mb-3">
              <CFormCheck
                id="cal-range"
                label="Range selection"
                checked={range}
                onChange={(e) => setRange(e.target.checked)}
              />
              <CFormCheck
                id="cal-double"
                label="Show 2 months"
                checked={doubleView}
                onChange={(e) => setDoubleView(e.target.checked)}
              />
              <CFormCheck
                id="cal-disable-weekends"
                label="Disable weekends"
                checked={disableWeekends}
                onChange={(e) => setDisableWeekends(e.target.checked)}
              />
              <CFormCheck
                id="cal-weeknumbers"
                label="Week numbers"
                checked={showWeekNumbers}
                onChange={(e) => setShowWeekNumbers(e.target.checked)}
              />
              <div className="d-flex align-items-center gap-2">
                <span className="text-body-secondary small">Locale</span>
                <CFormSelect
                  size="sm"
                  value={locale}
                  onChange={(e) => setLocale(e.target.value)}
                  style={{ width: 160 }}
                >
                  <option value="en-US">English (US)</option>
                  <option value="en-GB">English (GB)</option>
                  <option value="fr-FR">Français (FR)</option>
                  <option value="de-DE">Deutsch (DE)</option>
                  <option value="es-ES">Español (ES)</option>
                  <option value="ja-JP">日本語 (JP)</option>
                  <option value="ko-KR">한국어 (KR)</option>
                  <option value="ar">العربية (AR)</option>
                </CFormSelect>
              </div>
            </div>
            <div className={`d-flex justify-content-center ${RTL_LOCALES.includes(locale) ? 'rtl' : ''}`}>
              <Calendar
                className="cui-calendar border rounded bg-body"
                selectRange={range}
                onChange={setValue}
                value={value}
                tileDisabled={tileDisabled}
                showWeekNumbers={showWeekNumbers}
                locale={locale}
                prev2Label={null}
                next2Label={null}
                view="month"
                minDetail="decade"
                maxDetail="month"
                onClickWeekNumber={(_num, date) => {
                  if (!range) return
                  const start = startOfWeek(date, weekStartsOn)
                  const end = endOfWeek(date, weekStartsOn)
                  setValue([start, end])
                }}
                // doubleView shows two months side by side
                {...(doubleView ? { doubleView: true } : {})}
              />
            </div>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  )
}

export default CalendarPage
