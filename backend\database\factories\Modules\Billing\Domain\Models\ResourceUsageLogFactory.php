<?php

namespace Database\Factories\Modules\Billing\Domain\Models;

use App\Modules\Billing\Domain\Models\ResourceUsageLog;
use App\Modules\Billing\Domain\Models\Subscription;
use App\Modules\Organizations\Domain\Models\Organization;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class ResourceUsageLogFactory extends Factory
{
    protected $model = ResourceUsageLog::class;

    public function definition(): array
    {
        $limitValue = $this->faker->numberBetween(50, 200);
        $usageValue = $this->faker->numberBetween(10, $limitValue);
        $usagePercentage = ($usageValue / $limitValue) * 100;
        
        $alertLevel = 'normal';
        if ($usagePercentage >= 100) {
            $alertLevel = 'exceeded';
        } elseif ($usagePercentage >= 90) {
            $alertLevel = 'critical';
        } elseif ($usagePercentage >= 75) {
            $alertLevel = 'warning';
        }

        return [
            'subscription_id' => Subscription::factory(),
            'tenant_organization_id' => Str::uuid()->toString(),
            'resource_type' => $this->faker->randomElement(['users', 'sub_orgs', 'storage', 'hierarchy_depth', 'api_calls']),
            'usage_value' => $usageValue,
            'limit_value' => $limitValue,
            'usage_percentage' => round($usagePercentage, 2),
            'recorded_at' => now(),
            'alert_sent' => false,
            'alert_level' => $alertLevel,
        ];
    }

    public function users(): self
    {
        return $this->state([
            'resource_type' => 'users',
        ]);
    }

    public function storage(): self
    {
        return $this->state([
            'resource_type' => 'storage',
        ]);
    }

    public function warning(): self
    {
        return $this->state(function () {
            $limitValue = 100;
            $usageValue = 80;
            
            return [
                'limit_value' => $limitValue,
                'usage_value' => $usageValue,
                'usage_percentage' => 80.00,
                'alert_level' => 'warning',
            ];
        });
    }

    public function critical(): self
    {
        return $this->state(function () {
            $limitValue = 100;
            $usageValue = 95;
            
            return [
                'limit_value' => $limitValue,
                'usage_value' => $usageValue,
                'usage_percentage' => 95.00,
                'alert_level' => 'critical',
            ];
        });
    }

    public function exceeded(): self
    {
        return $this->state(function () {
            $limitValue = 100;
            $usageValue = 110;
            
            return [
                'limit_value' => $limitValue,
                'usage_value' => $usageValue,
                'usage_percentage' => 110.00,
                'alert_level' => 'exceeded',
            ];
        });
    }

    public function alertSent(): self
    {
        return $this->state([
            'alert_sent' => true,
        ]);
    }
}
