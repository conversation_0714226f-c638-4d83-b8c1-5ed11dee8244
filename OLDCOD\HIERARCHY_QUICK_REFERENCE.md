# ERP System Hierarchy - Quick Reference Guide

## 🎯 Quick Overview

Your ERP system has **6 hierarchical levels** with a sophisticated multi-level authorization system:

| Level | Name | Scope | Typical Roles | Key Feature |
|-------|------|-------|---------------|-------------|
| 1 | **Portal** | Global | System Admin | System-wide control |
| 2 | **Organization** | org_id | Org Admin | Root organization |
| 3 | **Sub-Organization** | org_id | Sub-Org Admin | Nested orgs |
| 4 | **Department** | dept_id | Dept Head | Functional groups |
| 5 | **Team** | team_id | Team Lead | Project teams |
| 6 | **User** | user_id | User | Individual |

---

## 📊 Hierarchy Visualization

```
PORTAL (System Admin)
  └─ ORGANIZATION (Org Admin)
      ├─ SUB-ORG (Sub-Org Admin)
      │   ├─ DEPARTMENT (Dept Head)
      │   │   ├─ TEAM (Team Lead)
      │   │   │   └─ USERS (Members)
      │   │   └─ TEAM (Team Lead)
      │   │       └─ USERS (Members)
      │   └─ DEPARTMENT (Dept Head)
      │       └─ TEAM (Team Lead)
      │           └─ USERS (Members)
      └─ DEPARTMENT (Dept Head)
          └─ TEAM (Team Lead)
              └─ USERS (Members)
```

---

## 🔐 Authorization Scopes

### Scope Types in RoleAssignment

```php
// Global scope (system-wide)
RoleAssignment::create([
    'scope' => 'global',
    'scope_id' => null,
]);

// Organization scope
RoleAssignment::create([
    'scope' => 'organization',
    'scope_id' => null,  // Entire org
]);

// Department scope
RoleAssignment::create([
    'scope' => 'department',
    'scope_id' => $dept_id,  // Specific dept
]);

// Team scope
RoleAssignment::create([
    'scope' => 'team',
    'scope_id' => $team_id,  // Specific team
]);

// User scope
RoleAssignment::create([
    'scope' => 'user',
    'scope_id' => $user_id,  // Individual user
]);
```

---

## 🗂️ Key Tables

### Organizations (Hierarchy)
- `id`: Organization ID
- `parent_id`: Parent organization (NULL = root)
- `type`: Organization type
- `name`: Organization name
- `status`: active/inactive

### OrgUnitClosure (Adjacency List)
- `ancestor_id`: Parent/ancestor
- `descendant_id`: Child/descendant
- `depth`: Distance (0 = self)

### Users (Organization Members)
- `id`: User ID
- `organization_id`: Organization context
- `email`: User email
- `status`: active/inactive/locked

### Roles (Permission Groups)
- `id`: Role ID
- `organization_id`: Organization context
- `name`: Role name
- `slug`: Role identifier

### Permissions (Access Rights)
- `id`: Permission ID
- `organization_id`: Organization context
- `name`: Permission name
- `module`: Feature module
- `slug`: Permission identifier

### RoleAssignments (User-Role Mapping)
- `user_id`: User receiving role
- `role_id`: Role being assigned
- `scope`: Scope level (global/org/dept/team/user)
- `scope_id`: Specific scope identifier
- `expires_at`: Optional expiration date

---

## 👥 Common Role Examples

### Portal Level
- **System Administrator**: Full system access
- **System Auditor**: View-only access to all data
- **System Support**: Support operations

### Organization Level
- **Organization Admin**: Manage entire organization
- **Organization Manager**: Manage teams and projects
- **Organization Member**: Basic operations

### Department Level
- **Department Head**: Manage department
- **Department Manager**: Manage teams
- **Department Lead**: Lead projects
- **Department Member**: Execute tasks

### Team Level
- **Team Lead**: Lead team, assign work
- **Team Member**: Execute team tasks
- **Team Contributor**: Contribute to team

### User Level
- **Power User**: Extended permissions
- **User**: Basic permissions

---

## 🔄 Authorization Flow

```
1. User sends request with Bearer token
   ↓
2. Validate token in user_sessions
   ↓
3. Load user and organization_id
   ↓
4. Load user's roles from role_assignments
   ↓
5. Load permissions from:
   - role_permissions (via roles)
   - user_permissions (direct)
   ↓
6. Check if required permission exists
   ↓
7. Check scope match (if scope-specific)
   ↓
8. ✅ Allow or ❌ Deny request
```

---

## 📋 Permission Naming Convention

```
module.action

Examples:
- users.view      (View users)
- users.create    (Create users)
- users.update    (Update users)
- users.delete    (Delete users)
- roles.view      (View roles)
- roles.create    (Create roles)
- organizations.view
- organizations.create
- etc.
```

---

## 🔑 Key Features

✅ **Multi-level hierarchy** - Unlimited organizational depth  
✅ **Flexible scoping** - Roles at any level  
✅ **Time-based expiration** - Roles can expire  
✅ **Organization isolation** - Complete data separation  
✅ **Closure table** - Efficient hierarchical queries  
✅ **Permission inheritance** - Permissions flow through roles  
✅ **Direct permissions** - Bypass roles if needed  
✅ **Audit trail** - Track role assignments  

---

## 💻 Code Examples

### Check if User Has Permission
```php
$authService->userHasPermission($user, 'users.create');
// Returns: true/false
```

### Get User's Effective Permissions
```php
$permissions = $authService->getEffectivePermissions($user);
// Returns: ['users.view', 'users.create', 'roles.view', ...]
```

### Assign Role to User
```php
RoleAssignment::create([
    'user_id' => $user->id,
    'role_id' => $role->id,
    'organization_id' => $org->id,
    'scope' => 'department',
    'scope_id' => $dept_id,
    'assigned_by' => auth()->id(),
    'assigned_at' => now(),
    'expires_at' => now()->addMonths(3),
]);
```

### Get Organization Hierarchy
```php
$hierarchy = $orgService->getHierarchy($org_id);
// Returns: Collection of child organizations with depth
```

### Get Organization Ancestors
```php
$ancestors = $orgService->getAncestors($org_id);
// Returns: Collection of parent organizations
```

---

## 🎯 Common Use Cases

### Use Case 1: Create Organization Structure
```
Company (Root)
├── Sales Division
│   ├── North Region
│   │   ├── Sales Team 1
│   │   └── Sales Team 2
│   └── South Region
│       ├── Sales Team 3
│       └── Sales Team 4
└── Engineering Division
    ├── Backend Team
    └── Frontend Team
```

### Use Case 2: Assign Roles
```
1. User 1 → Admin role (scope: organization)
   → Can manage entire organization

2. User 2 → Manager role (scope: department, dept_id: sales)
   → Can manage only sales department

3. User 3 → Lead role (scope: team, team_id: backend)
   → Can manage only backend team

4. User 4 → Member role (scope: user, user_id: user4)
   → Can only access own resources
```

### Use Case 3: Temporary Role Assignment
```php
// Project lead for 3 months
RoleAssignment::create([
    'user_id' => $user->id,
    'role_id' => $project_lead->id,
    'scope' => 'team',
    'scope_id' => $project_team->id,
    'expires_at' => now()->addMonths(3),
]);
// Role automatically expires after 3 months
```

---

## 🔍 Database Queries

### Get User's Permissions
```sql
SELECT DISTINCT p.slug
FROM user_permissions up
JOIN permissions p ON p.id = up.permission_id
WHERE up.user_id = ? AND up.organization_id = ?
UNION
SELECT DISTINCT p.slug
FROM role_assignments ra
JOIN role_permissions rp ON rp.role_id = ra.role_id
JOIN permissions p ON p.id = rp.permission_id
WHERE ra.user_id = ? AND ra.organization_id = ?;
```

### Get Organization Tree
```sql
SELECT o.*, c.depth
FROM organizations o
JOIN org_unit_closure c ON c.descendant_id = o.id
WHERE c.ancestor_id = ?
ORDER BY c.depth;
```

### Get Active Role Assignments
```sql
SELECT ra.*, r.name, u.email
FROM role_assignments ra
JOIN roles r ON r.id = ra.role_id
JOIN users u ON u.id = ra.user_id
WHERE ra.organization_id = ?
  AND (ra.expires_at IS NULL OR ra.expires_at > NOW())
ORDER BY ra.assigned_at DESC;
```

---

## 📚 Related Documents

- **HIERARCHY_LEVELS_DIAGRAM.md** - Detailed hierarchy explanation
- **HIERARCHY_VISUAL_DIAGRAM.txt** - ASCII diagrams and flows
- **HIERARCHY_DATA_MODEL.md** - Complete database schema
- **COMPREHENSIVE_TEST_AUDIT.md** - Test coverage report

---

## ⚡ Performance Tips

1. **Cache permissions** in session (refresh on role change)
2. **Use closure table** for hierarchy (O(1) queries)
3. **Index frequently queried fields**:
   - `users.organization_id`
   - `role_assignments.user_id`
   - `role_assignments.expires_at`
   - `organizations.parent_id`

4. **Batch load** related data
5. **Use soft deletes** for audit trail

---

## 🚀 Getting Started

1. **Create root organization**
   ```php
   $org = Organization::create(['name' => 'Company', 'type' => 'organization']);
   ```

2. **Create hierarchy levels**
   ```php
   HierarchyLevel::create(['organization_id' => $org->id, 'name' => 'Division', 'level' => 1]);
   HierarchyLevel::create(['organization_id' => $org->id, 'name' => 'Department', 'level' => 2]);
   ```

3. **Create roles**
   ```php
   $admin = Role::create(['organization_id' => $org->id, 'name' => 'Admin', 'slug' => 'admin']);
   ```

4. **Create permissions**
   ```php
   $perm = Permission::create(['organization_id' => $org->id, 'name' => 'View Users', 'slug' => 'users.view', 'module' => 'users']);
   ```

5. **Attach permissions to role**
   ```php
   $admin->permissions()->attach($perm->id);
   ```

6. **Assign role to user**
   ```php
   RoleAssignment::create(['user_id' => $user->id, 'role_id' => $admin->id, 'organization_id' => $org->id]);
   ```

---

**Quick Reference Generated**: October 29, 2025  
**System**: ERP Backend API  
**Architecture**: DDD Modular Monolith
