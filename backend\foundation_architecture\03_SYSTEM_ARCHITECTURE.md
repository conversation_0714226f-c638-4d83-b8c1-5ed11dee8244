# System Architecture

## High-Level Architecture

### Architectural Pattern: Modular Monolith

```
┌─────────────────────────────────────────────────────────────────┐
│                        CLIENT LAYER                              │
│  Web Browser / Mobile App / Third-party API Consumers           │
└────────────────────────┬────────────────────────────────────────┘
                         │
                         ▼
┌─────────────────────────────────────────────────────────────────┐
│                     API GATEWAY LAYER                            │
│  • Route Handling     • Authentication    • Rate Limiting       │
│  • Validation         • Error Handling    • Response Formatting │
└────────────────────────┬────────────────────────────────────────┘
                         │
                         ▼
┌─────────────────────────────────────────────────────────────────┐
│                    PRESENTATION LAYER                            │
│  Controllers (HTTP/API) - Handle requests, validate input       │
└────────────────────────┬────────────────────────────────────────┘
                         │
                         ▼
┌─────────────────────────────────────────────────────────────────┐
│                    APPLICATION LAYER                             │
│  Services - Business use cases, orchestration                   │
│  • BillingService    • UsageTrackingService                     │
│  • ApprovalService   • ResourceLimitService                     │
└────────────────────────┬────────────────────────────────────────┘
                         │
                         ▼
┌─────────────────────────────────────────────────────────────────┐
│                      DOMAIN LAYER                                │
│  Models - Business entities and rules                           │
│  • Organization   • Subscription   • Plan   • User              │
│  Repositories - Data access interfaces                          │
└────────────────────────┬────────────────────────────────────────┘
                         │
                         ▼
┌─────────────────────────────────────────────────────────────────┐
│                  INFRASTRUCTURE LAYER                            │
│  • Database (MySQL/SQLite)  • Cache (Redis)                     │
│  • Queue System (Redis)     • File Storage                      │
│  • Email Service            • Logging                           │
└─────────────────────────────────────────────────────────────────┘
```

---

## Modular Organization

### Module Structure

```
app/Modules/
├── Organizations/          # Tenant & hierarchy management
│   ├── Domain/
│   │   ├── Models/        # Organization, hierarchy
│   │   └── Repositories/  # Data access interfaces
│   ├── Application/
│   │   └── Services/      # Business logic
│   ├── Infrastructure/
│   │   └── Repositories/  # Eloquent implementations
│   └── Presentation/
│       └── API/           # Controllers
│
├── Billing/               # Subscription & resource management
│   ├── Domain/
│   │   ├── Models/        # Plan, Subscription, Addon, ResourceUsageLog
│   │   └── Repositories/
│   ├── Application/
│   │   └── Services/      # BillingService, UsageTrackingService
│   └── Presentation/
│       └── API/           # BillingController
│
├── Approvals/             # Workflow management
│   ├── Domain/
│   │   ├── Models/        # ApprovalRequest, ApprovalStep
│   │   └── Services/      # ApprovalService
│   └── Presentation/
│       └── API/
│
├── Users/                 # User management
│   ├── Domain/
│   ├── Application/
│   └── Presentation/
│
├── Notifications/         # Alert system
│   ├── Domain/
│   ├── Application/
│   └── Presentation/
│
├── Audit/                 # Activity logging
│   ├── Domain/
│   ├── Application/
│   └── Presentation/
│
├── Reports/               # Analytics & reporting
├── Settings/              # System configuration
├── Integration/           # External APIs
├── RolesPermissions/      # RBAC (future)
├── Accounting/            # Finance (future)
├── Inventory/             # Stock management (future)
└── Shared/                # Common utilities
    ├── Domain/
    │   ├── Traits/        # HasUUID, HasOrganizationId
    │   └── Services/      # BaseService
    └── Infrastructure/
```

---

## Domain-Driven Design Layers

### 1. Domain Layer (Core Business Logic)

**Purpose:** Contains pure business logic and rules

**Components:**
- **Models:** Business entities (Organization, Subscription, User)
- **Value Objects:** Immutable objects (Money, Email, DateRange)
- **Domain Services:** Business operations that don't belong to a single entity
- **Repositories (Interfaces):** Data access contracts
- **Domain Events:** Business events that occur

**Rules:**
- NO dependencies on infrastructure
- NO framework dependencies
- Only business logic
- Testable without database

**Example Structure:**
```php
Billing/Domain/
├── Models/
│   ├── Plan.php              # Business entity
│   ├── Subscription.php
│   └── ResourceUsageLog.php
├── Repositories/
│   └── PlanRepositoryInterface.php  # Contract
└── Events/
    └── SubscriptionUpgraded.php     # Domain event
```

---

### 2. Application Layer (Use Cases)

**Purpose:** Orchestrates domain logic for specific use cases

**Components:**
- **Services:** Application services (BillingService, UsageTrackingService)
- **DTOs:** Data transfer objects for inter-layer communication
- **Commands:** CQRS command objects (future)
- **Queries:** CQRS query objects (future)

**Responsibilities:**
- Coordinate domain objects
- Handle transactions
- Trigger domain events
- Validate business rules
- Call infrastructure services

**Example:**
```php
BillingService {
    - purchaseAddOn()       # Orchestrates domain logic
    - upgradeSubscription() # Coordinates multiple entities
    - calculateProration()  # Business calculation
}
```

---

### 3. Infrastructure Layer (Technical Implementation)

**Purpose:** Provides technical implementations

**Components:**
- **Repositories (Concrete):** Eloquent implementations
- **External Services:** Email, SMS, payment APIs
- **Database Migrations:** Schema definitions
- **Cache Implementations:** Redis, Memcached
- **Queue Handlers:** Job processors

**Example:**
```php
Billing/Infrastructure/
├── Repositories/
│   └── EloquentPlanRepository.php  # Eloquent implementation
├── Services/
│   └── EmailNotificationService.php
└── Jobs/
    └── LogResourceUsageJob.php
```

---

### 4. Presentation Layer (User Interface)

**Purpose:** Handles HTTP requests and responses

**Components:**
- **Controllers:** API endpoints
- **Requests:** Validation logic
- **Resources:** Response transformers
- **Middleware:** Request processing

**Example:**
```php
Billing/Presentation/API/
├── BillingController.php
├── Requests/
│   └── AddAddonRequest.php
├── Resources/
│   └── SubscriptionResource.php
└── Middleware/
    └── CheckSubscriptionStatus.php
```

---

## Data Flow Architecture

### Request Flow

```
1. HTTP Request
   ↓
2. Router (routes/api.php)
   ↓
3. Middleware Chain
   - Authentication
   - Organization Context
   - Rate Limiting
   - Permission Check
   ↓
4. Controller (Presentation Layer)
   - Validate input
   - Extract parameters
   ↓
5. Application Service
   - Business logic
   - Coordinate domain objects
   ↓
6. Domain Models
   - Apply business rules
   - Validate state
   ↓
7. Repository
   - Database operations
   - Query optimization
   ↓
8. Response
   - Transform data
   - Format JSON
   - Return to client
```

### Example: Add-on Purchase Flow

```
Client Request: POST /api/v1/subscriptions/{id}/addons
    ↓
1. AuthenticationMiddleware
   - Verify JWT token
   - Load user session
    ↓
2. OrganizationContextMiddleware
   - Validate organization access
   - Set tenant scope
    ↓
3. BillingController::addAddon()
   - Validate addon_id exists
   - Validate quantity
    ↓
4. BillingService::addAddon()
   - Load Subscription model
   - Load Addon model
   - Check resource limits
   - Calculate proration
   - Attach addon to subscription
   - Log transaction
    ↓
5. Database Transaction
   - Insert into subscription_addon
   - Update subscription record
   - Create audit log
   - Commit transaction
    ↓
6. Response
   - Transform subscription data
   - Include addon details
   - Return 200 OK
```

---

## Module Communication Patterns

### 1. Direct Service Injection

**When to use:** Same bounded context or closely related modules

```php
class BillingService {
    public function __construct(
        private ApprovalService $approvalService,
        private NotificationService $notificationService
    ) {}
    
    public function upgradeSubscription() {
        // Direct service call
        $this->approvalService->createApprovalRequest(...);
        $this->notificationService->sendAlert(...);
    }
}
```

---

### 2. Domain Events (Recommended)

**When to use:** Loose coupling between modules

```php
// In BillingService
Event::dispatch(new SubscriptionUpgraded($subscription));

// In NotificationModule (Event Listener)
class SendUpgradeNotification {
    public function handle(SubscriptionUpgraded $event) {
        // Send notification
    }
}
```

**Benefits:**
- Loose coupling
- Easy to add new listeners
- Async processing possible
- Better testability

---

### 3. Repository Pattern

**Purpose:** Abstract data access

```php
// Interface (Domain Layer)
interface PlanRepositoryInterface {
    public function findById(string $id): ?Plan;
    public function all(): Collection;
}

// Implementation (Infrastructure Layer)
class EloquentPlanRepository implements PlanRepositoryInterface {
    public function findById(string $id): ?Plan {
        return Plan::find($id);
    }
}

// Usage (Application Layer)
class BillingService {
    public function __construct(
        private PlanRepositoryInterface $planRepository
    ) {}
}
```

---

## Database Architecture

### Multi-Tenancy Strategy: Shared Database, Tenant Filtering

```
Single Database
    ├── organizations (tenant data)
    ├── users (organization_id foreign key)
    ├── subscriptions (organization_id)
    ├── plans (shared across tenants)
    └── resource_usage_logs (tenant-specific)
```

**Enforcement Mechanisms:**

1. **Global Query Scopes:**
```php
// Automatically filter by organization_id
User::all();  // Only returns current tenant's users
```

2. **Middleware Validation:**
```php
// Verify user belongs to requested organization
OrganizationContextMiddleware::handle()
```

3. **Model Traits:**
```php
trait HasOrganizationId {
    protected static function boot() {
        static::addGlobalScope(new OrganizationScope);
    }
}
```

---

## Caching Strategy

### Cache Layers

```
┌─────────────────────────────────────┐
│   Application Cache (Redis)         │
│   - User sessions                   │
│   - API rate limits                 │
│   - Frequently accessed data        │
└─────────────────────────────────────┘
         ↓ (Cache Miss)
┌─────────────────────────────────────┐
│   Query Result Cache                │
│   - Plan listings                   │
│   - Organization hierarchy          │
│   - Permission checks               │
└─────────────────────────────────────┘
         ↓ (Cache Miss)
┌─────────────────────────────────────┐
│   Database (MySQL)                  │
└─────────────────────────────────────┘
```

### What to Cache

| Data Type | TTL | Strategy |
|-----------|-----|----------|
| Plans | 1 hour | Cache all, invalidate on update |
| Organization structure | 30 minutes | Cache per tenant |
| User permissions | 15 minutes | Cache per user |
| Resource limits | 5 minutes | Cache per subscription |
| Usage statistics | 1 minute | Cache per organization |
| API responses | 30 seconds | Cache for identical requests |

### Cache Keys Pattern
```
Format: {module}:{entity}:{id}:{optional_suffix}

Examples:
- billing:plan:123
- org:hierarchy:tenant_456
- user:permissions:user_789
- billing:usage:org_123:current
```

---

## Queue Architecture

### Job Categories

```
┌─────────────────────────────────────────────────┐
│           HIGH PRIORITY QUEUE                    │
│  - Alert notifications                          │
│  - Critical emails                              │
│  - Resource limit breaches                      │
└─────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────┐
│           DEFAULT QUEUE                          │
│  - User notifications                           │
│  - Approval workflow emails                     │
│  - Standard emails                              │
└─────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────┐
│           LOW PRIORITY QUEUE                     │
│  - Usage logging (daily)                        │
│  - Report generation                            │
│  - Data cleanup                                 │
└─────────────────────────────────────────────────┘
```

### Scheduled Jobs

```php
// Daily at 00:00 IST
Schedule::job(new LogResourceUsageJob())
    ->dailyAt('00:00')
    ->withoutOverlapping()
    ->onOneServer();

// Weekly on Sundays at 02:00
Schedule::job(new CleanupOldUsageLogsJob(90))
    ->weekly()
    ->sundays()
    ->at('02:00');
```

---

## Security Architecture

### Authentication Flow

```
1. User Login Request
   ↓
2. Validate Credentials
   ↓
3. Generate JWT Token
   - Include user_id
   - Include organization_id
   - Set expiration (24 hours)
   ↓
4. Return Token to Client
   ↓
5. Client Includes Token in Headers
   Authorization: Bearer {token}
   ↓
6. Middleware Validates Token
   - Verify signature
   - Check expiration
   - Load user context
   ↓
7. Set Organization Scope
   - Filter all queries by organization_id
   - Prevent cross-tenant access
```

### Authorization Layers

```
Layer 1: Authentication
  - Verify user identity
  - Validate JWT token

Layer 2: Organization Context
  - Verify user belongs to organization
  - Set tenant scope

Layer 3: Permission Check
  - Check role permissions
  - Verify module access

Layer 4: Resource Limits
  - Check subscription status
  - Verify resource availability
  - Enforce plan limits
```

---

## Error Handling Architecture

### Error Hierarchy

```
Exception
├── AppException (Base)
│   ├── ValidationException
│   ├── AuthenticationException
│   ├── AuthorizationException
│   └── BusinessLogicException
│       ├── ResourceLimitException
│       ├── SubscriptionException
│       └── ApprovalException
└── InfrastructureException
    ├── DatabaseException
    └── ExternalServiceException
```

### Error Response Format

```json
{
  "success": false,
  "error": {
    "code": "RESOURCE_LIMIT_EXCEEDED",
    "message": "User limit reached. Please upgrade your plan.",
    "details": {
      "resource": "users",
      "current": 10,
      "limit": 10,
      "plan": "Basic Plan"
    },
    "suggestions": [
      "Upgrade to Professional Plan (50 users)",
      "Purchase 'Additional Users' add-on"
    ]
  },
  "timestamp": "2025-10-30T06:21:52Z",
  "trace_id": "abc123def456"
}
```

---

## Scalability Considerations

### Horizontal Scaling

```
Load Balancer
    ├── App Server 1
    ├── App Server 2
    ├── App Server 3
    └── App Server N

Shared Resources:
├── Database (Master-Replica)
│   ├── Master (Write)
│   └── Replicas (Read)
├── Redis Cache (Cluster)
└── Queue Workers (Multiple)
```

### Database Optimization

1. **Read Replicas:**
   - Route SELECT queries to replicas
   - Write operations to master only
   - Automatic failover

2. **Indexing Strategy:**
   - organization_id on all tables
   - Composite indexes for common queries
   - Full-text search indexes

3. **Partitioning (Future):**
   - Partition large tables by organization_id
   - Separate hot/cold data

---

## Monitoring Architecture

### Observability Stack

```
┌─────────────────────────────────────┐
│        Application Logs              │
│  Laravel Log → JSON Format           │
└──────────────┬──────────────────────┘
               ↓
┌─────────────────────────────────────┐
│        Metrics Collection            │
│  - Response times                   │
│  - Error rates                      │
│  - Resource usage                   │
└──────────────┬──────────────────────┘
               ↓
┌─────────────────────────────────────┐
│        Alerting System               │
│  - Critical errors → SMS             │
│  - Performance issues → Email        │
│  - Resource alerts → Dashboard       │
└─────────────────────────────────────┘
```

### Key Metrics to Monitor

| Metric | Threshold | Action |
|--------|-----------|--------|
| API Response Time | >500ms | Alert |
| Error Rate | >1% | Investigate |
| Queue Backlog | >1000 jobs | Scale workers |
| Database Queries | >100ms | Optimize |
| Memory Usage | >80% | Scale up |
| Disk Space | >85% | Cleanup/Expand |

---

## Deployment Architecture

### Environments

```
┌─────────────────────────────────────┐
│        Production                    │
│  - MySQL Database                   │
│  - Redis Cache & Queue              │
│  - Multiple App Servers             │
│  - Load Balancer                    │
└─────────────────────────────────────┘

┌─────────────────────────────────────┐
│        Staging                       │
│  - MySQL Database (Smaller)         │
│  - Redis                            │
│  - Single App Server                │
└─────────────────────────────────────┘

┌─────────────────────────────────────┐
│        Development                   │
│  - SQLite Database                  │
│  - Sync Queue (No Redis)            │
│  - Local PHP Server                 │
└─────────────────────────────────────┘
```

---

## Backup & Recovery Architecture

### Backup Strategy

```
Daily Full Backups
├── Database dump (gzipped)
├── File storage snapshot
└── Configuration files

Hourly Incremental Backups
└── Transaction logs

Real-time Replication
└── Standby database server
```

### Recovery Procedures

1. **Database Corruption:**
   - Switch to replica
   - Restore from latest backup
   - Replay transaction logs

2. **Data Loss:**
   - Point-in-time recovery
   - Validate data integrity
   - Test restored environment

3. **Complete Failure:**
   - Disaster recovery site
   - Full system restoration
   - Data validation and testing

---

*Next Document: 04_MODULE_STRUCTURE.md - Detailed module breakdown and responsibilities*
