import { legacy_createStore as createStore, combineReducers } from 'redux'

// UI state reducer
const initialUIState = {
  sidebarShow: true,
  sidebarUnfoldable: false,
  theme: 'light',
}

const uiReducer = (state = initialUIState, { type, ...rest }) => {
  switch (type) {
    case 'set':
      return { ...state, ...rest }
    default:
      return state
  }
}

// Auth reducer
const initialAuthState = {
  user: null,
  token: localStorage.getItem('authToken'),
  isAuthenticated: !!localStorage.getItem('authToken'),
  loading: false,
  error: null,
}

const authReducer = (state = initialAuthState, action) => {
  switch (action.type) {
    case 'AUTH_LOGIN':
      return { ...state, isAuthenticated: true, token: action.payload, error: null }
    case 'AUTH_LOGOUT':
      return { ...state, isAuthenticated: false, token: null, user: null }
    case 'AUTH_ERROR':
      return { ...state, error: action.payload }
    default:
      return state
  }
}

// Data reducer
const initialDataState = {
  users: { data: [], loading: false, error: null },
  organizations: { data: [], loading: false, error: null },
  roles: { data: [], loading: false, error: null },
  subscriptions: { data: [], loading: false, error: null },
  payments: { data: [], loading: false, error: null },
  invoices: { data: [], loading: false, error: null },
  approvals: { data: [], loading: false, error: null },
  notifications: { data: [], loading: false, error: null },
  webhooks: { data: [], loading: false, error: null },
}

const dataReducer = (state = initialDataState, action) => {
  switch (action.type) {
    case 'SET_DATA':
      return { ...state, [action.module]: { data: action.payload, loading: false, error: null } }
    case 'SET_LOADING':
      return { ...state, [action.module]: { ...state[action.module], loading: true } }
    case 'SET_ERROR':
      return { ...state, [action.module]: { ...state[action.module], error: action.payload, loading: false } }
    default:
      return state
  }
}

const rootReducer = combineReducers({
  ui: uiReducer,
  auth: authReducer,
  data: dataReducer,
})

const store = createStore(rootReducer)

export default store
