<?php

namespace App\Modules\Users\Domain\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Modules\Shared\Domain\Traits\HasUUID;
use App\Modules\Shared\Domain\Traits\HasOrganizationId;

class UserCredential extends Model
{
    use HasFactory, SoftDeletes, HasUUID, HasOrganizationId;

    protected $table = 'user_credentials';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'organization_id',
        'user_id',
        'type',
        'value',
        'metadata',
        'is_primary',
        'last_used_at',
        'expires_at',
    ];

    protected $casts = [
        'metadata' => 'array',
        'is_primary' => 'boolean',
        'last_used_at' => 'datetime',
        'expires_at' => 'datetime',
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
