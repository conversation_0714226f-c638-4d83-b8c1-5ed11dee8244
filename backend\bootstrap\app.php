<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware): void {
        // Custom middleware aliases
        $middleware->alias([
            'check.organization' => \App\Http\Middleware\CheckOrganizationAccess::class,
            'check.permission' => \App\Http\Middleware\CheckPermission::class,
            'auth.token' => \App\Http\Middleware\AuthenticateByToken::class,
        ]);

        // Ensure API requests authenticate via bearer token if provided
        if (method_exists($middleware, 'appendToGroup')) {
            $middleware->appendToGroup('api', \App\Http\Middleware\AuthenticateByToken::class);
        }
    })
    ->withExceptions(function (Exceptions $exceptions): void {
        //
    })
    ->withProviders([
        \App\Providers\AuthServiceProvider::class,
    ])->create();
