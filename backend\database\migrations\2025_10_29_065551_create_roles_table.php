<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('roles', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('organization_id')->comment('Organization this role belongs to');
            $table->string('name')->comment('Role name');
            $table->string('slug')->comment('Role slug for system identification');
            $table->text('description')->nullable();
            $table->boolean('is_system')->default(false)->comment('System role that cannot be deleted');
            $table->timestamps();
            $table->softDeletes();
            
            // Unique constraint with organization scoping
            $table->unique(['organization_id', 'slug']);
            
            // Indexes
            $table->index('organization_id');
            $table->index('slug');
            $table->index('is_system');
            
            // Foreign key
            $table->foreign('organization_id')
                  ->references('id')
                  ->on('organizations')
                  ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('roles');
    }
};
