<?php

namespace Tests\Feature\RolesPermissions;

use Tests\TestCase;
use App\Modules\Users\Domain\Models\User;
use App\Modules\Organizations\Domain\Models\Organization;
use App\Modules\RolesPermissions\Domain\Models\Role;
use App\Modules\RolesPermissions\Domain\Models\RoleAssignment;
use Illuminate\Foundation\Testing\RefreshDatabase;

class RoleAssignmentsTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Organization $org;
    private Role $role;

    protected function setUp(): void
    {
        parent::setUp();
        $this->org = Organization::factory()->create();
        $this->user = User::factory()->create(['organization_id' => $this->org->id]);
        $this->role = Role::factory()->create(['organization_id' => $this->org->id]);
    }

    public function test_can_list_role_assignments()
    {
        RoleAssignment::factory()->create(['organization_id' => $this->org->id]);
        $response = $this->actingAs($this->user)->getJson('/api/v1/role-assignments');
        $response->assertStatus(200);
    }

    public function test_can_create_role_assignment()
    {
        $user2 = User::factory()->create(['organization_id' => $this->org->id]);
        $response = $this->actingAs($this->user)->postJson('/api/v1/role-assignments', [
            'user_id' => $user2->id,
            'role_id' => $this->role->id,
        ]);
        $response->assertStatus(201);
    }

    public function test_can_show_role_assignment()
    {
        $assignment = RoleAssignment::factory()->create(['organization_id' => $this->org->id]);
        $response = $this->actingAs($this->user)->getJson("/api/v1/role-assignments/{$assignment->id}");
        $response->assertStatus(200);
    }

    public function test_can_update_role_assignment()
    {
        $assignment = RoleAssignment::factory()->create(['organization_id' => $this->org->id]);
        $response = $this->actingAs($this->user)->patchJson("/api/v1/role-assignments/{$assignment->id}", [
            'scope' => 'department',
            'scope_id' => 'dept-123',
        ]);
        $response->assertStatus(200);
    }

    public function test_can_delete_role_assignment()
    {
        $assignment = RoleAssignment::factory()->create(['organization_id' => $this->org->id]);
        $response = $this->actingAs($this->user)->deleteJson("/api/v1/role-assignments/{$assignment->id}");
        $response->assertStatus(200);
        $this->assertNull(RoleAssignment::find($assignment->id));
    }
}
