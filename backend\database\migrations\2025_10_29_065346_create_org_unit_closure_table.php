<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('org_unit_closure', function (Blueprint $table) {
            $table->uuid('ancestor_id')->comment('Ancestor organization ID');
            $table->uuid('descendant_id')->comment('Descendant organization ID');
            $table->integer('depth')->comment('Distance between ancestor and descendant');
            
            // Primary key on ancestor and descendant
            $table->primary(['ancestor_id', 'descendant_id']);
            
            // Indexes for fast queries
            $table->index('ancestor_id');
            $table->index('descendant_id');
            $table->index('depth');
            
            // Foreign keys
            $table->foreign('ancestor_id')
                  ->references('id')
                  ->on('organizations')
                  ->onDelete('cascade');
            
            $table->foreign('descendant_id')
                  ->references('id')
                  ->on('organizations')
                  ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('org_unit_closure');
    }
};
