<?php

namespace Database\Factories\Modules\Users\Domain\Models;

use App\Modules\Users\Domain\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class UserFactory extends Factory
{
    protected $model = User::class;

    public function definition(): array
    {
        return [
            'organization_id' => \App\Modules\Organizations\Domain\Models\Organization::factory(),
            'name' => $this->faker->name(),
            'email' => $this->faker->unique()->safeEmail(),
            'mobile' => $this->faker->e164PhoneNumber(),
            'password' => Hash::make('password'),
            'status' => 'active',
            'email_verified_at' => now(),
            'timezone' => 'UTC',
            'language' => 'en',
            'preferences' => [],
        ];
    }
}
