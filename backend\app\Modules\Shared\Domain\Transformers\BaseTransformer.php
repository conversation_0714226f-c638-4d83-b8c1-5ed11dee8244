<?php

namespace App\Modules\Shared\Domain\Transformers;

use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Model;

abstract class BaseTransformer
{
    /**
     * Transform a single model to array.
     */
    abstract public function transform(Model $model): array;

    /**
     * Transform a list of models.
     */
    public function collection(iterable $items): array
    {
        $data = [];
        foreach ($items as $item) {
            $data[] = $this->transform($item);
        }
        return $data;
    }

    /**
     * Transform a paginator to a standard structure.
     */
    public function paginate(LengthAwarePaginator $paginator): array
    {
        return [
            'data' => $this->collection($paginator->items()),
            'meta' => [
                'current_page' => $paginator->currentPage(),
                'per_page' => $paginator->perPage(),
                'total' => $paginator->total(),
                'last_page' => $paginator->lastPage(),
            ],
        ];
    }
}
