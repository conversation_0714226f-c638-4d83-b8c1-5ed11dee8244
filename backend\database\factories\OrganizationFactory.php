<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use App\Modules\Organizations\Domain\Models\Organization as DomainOrganization;

class OrganizationFactory extends Factory
{
    protected $model = DomainOrganization::class;

    public function definition(): array
    {
        return [
            'parent_id' => null,
            'type' => 'organization',
            'name' => fake()->company(),
            'code' => strtoupper(Str::random(8)),
            'description' => fake()->sentence(),
            'email' => fake()->unique()->safeEmail(),
            'phone' => fake()->phoneNumber(),
            'address' => fake()->address(),
            'city' => fake()->city(),
            'state' => fake()->state(),
            'country' => fake()->country(),
            'postal_code' => fake()->postcode(),
            'timezone' => 'UTC',
            'currency' => 'USD',
            'language' => 'en',
            'status' => 'active',
            'settings' => [],
        ];
    }
}
