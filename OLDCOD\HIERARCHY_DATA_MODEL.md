# ERP System Hierarchy - Complete Data Model Reference

## 📊 Database Tables & Relationships

### 1. Organizations Table
**Purpose**: Store organizational units at all hierarchy levels  
**Scope**: Global (not scoped by organization_id)

```sql
CREATE TABLE organizations (
    id VARCHAR(36) PRIMARY KEY,
    parent_id VARCHAR(36) NULLABLE,          -- Self-referencing for hierarchy
    type VARCHAR(50) DEFAULT 'organization', -- 'organization', 'department', etc.
    name VARCHAR(255) NOT NULL,
    code VARCHAR(20) UNIQUE,                 -- Org code (e.g., "ACME-ABC1")
    description TEXT NULLABLE,
    email VARCHAR(255) NULLABLE,
    phone VARCHAR(20) NULLABLE,
    address VARCHAR(255) NULLABLE,
    city VARCHAR(100) NULLABLE,
    state VARCHAR(100) NULLABLE,
    country VARCHAR(100) NULLABLE,
    postal_code VARCHAR(20) NULLABLE,
    timezone VARCHAR(50) DEFAULT 'UTC',
    currency VARCHAR(3) DEFAULT 'USD',
    language VARCHAR(10) DEFAULT 'en',
    status VARCHAR(20) DEFAULT 'active',    -- 'active', 'inactive', 'archived'
    settings JSON NULLABLE,                  -- Custom settings
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    deleted_at TIMESTAMP NULLABLE,
    
    FOREIGN KEY (parent_id) REFERENCES organizations(id),
    INDEX idx_parent_id (parent_id),
    INDEX idx_status (status)
);
```

**Relationships**:
```
Organization (1) ──── (Many) Organization (via parent_id)
Organization (1) ──── (Many) User
Organization (1) ──── (Many) Role
Organization (1) ──── (Many) Permission
Organization (1) ──── (Many) RoleAssignment
Organization (1) ──── (Many) HierarchyLevel
```

---

### 2. OrgUnitClosure Table
**Purpose**: Store hierarchical relationships (Adjacency List Pattern)  
**Scope**: Global

```sql
CREATE TABLE org_unit_closure (
    ancestor_id VARCHAR(36) NOT NULL,
    descendant_id VARCHAR(36) NOT NULL,
    depth INT DEFAULT 0,                    -- 0 = self, 1 = direct child, etc.
    
    PRIMARY KEY (ancestor_id, descendant_id),
    FOREIGN KEY (ancestor_id) REFERENCES organizations(id) ON DELETE CASCADE,
    FOREIGN KEY (descendant_id) REFERENCES organizations(id) ON DELETE CASCADE,
    INDEX idx_descendant (descendant_id),
    INDEX idx_depth (depth)
);
```

**Purpose of Closure Table**:
- Enables efficient hierarchical queries
- Avoids recursive queries
- Stores all ancestor-descendant relationships including self-references

**Example Data**:
```
Org Structure:
    A
    ├── B
    │   └── D
    └── C

Closure Table:
ancestor_id | descendant_id | depth
─────────────────────────────────
    A       |      A        |  0    (self)
    A       |      B        |  1    (direct child)
    A       |      C        |  1    (direct child)
    A       |      D        |  2    (grandchild)
    B       |      B        |  0    (self)
    B       |      D        |  1    (direct child)
    C       |      C        |  0    (self)
    D       |      D        |  0    (self)
```

---

### 3. HierarchyLevel Table
**Purpose**: Define organizational hierarchy levels  
**Scope**: Per organization (organization_id)

```sql
CREATE TABLE hierarchy_levels (
    id VARCHAR(36) PRIMARY KEY,
    organization_id VARCHAR(36) NOT NULL,
    name VARCHAR(100) NOT NULL,             -- 'Company', 'Division', 'Department'
    slug VARCHAR(100) NOT NULL,
    level INT NOT NULL,                     -- 1, 2, 3, etc.
    description TEXT NULLABLE,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    deleted_at TIMESTAMP NULLABLE,
    
    FOREIGN KEY (organization_id) REFERENCES organizations(id),
    UNIQUE KEY unique_org_level (organization_id, level),
    INDEX idx_organization (organization_id)
);
```

**Example Data**:
```
organization_id | name        | level | slug
─────────────────────────────────────────────
    org-1       | Company     |  1    | company
    org-1       | Division    |  2    | division
    org-1       | Department  |  3    | department
    org-1       | Team        |  4    | team
```

---

### 4. Users Table
**Purpose**: Store user accounts  
**Scope**: Per organization (organization_id)

```sql
CREATE TABLE users (
    id VARCHAR(36) PRIMARY KEY,
    organization_id VARCHAR(36) NOT NULL,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    mobile VARCHAR(20) NULLABLE,
    password VARCHAR(255) NOT NULL,         -- Hashed
    status VARCHAR(20) DEFAULT 'active',    -- 'active', 'inactive', 'locked'
    email_verified_at TIMESTAMP NULLABLE,
    mobile_verified_at TIMESTAMP NULLABLE,
    mfa_enabled BOOLEAN DEFAULT FALSE,
    mfa_secret VARCHAR(255) NULLABLE,
    failed_login_attempts INT DEFAULT 0,
    locked_until TIMESTAMP NULLABLE,
    last_login_at TIMESTAMP NULLABLE,
    last_login_ip VARCHAR(45) NULLABLE,
    avatar VARCHAR(255) NULLABLE,
    timezone VARCHAR(50) DEFAULT 'UTC',
    language VARCHAR(10) DEFAULT 'en',
    preferences JSON NULLABLE,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    deleted_at TIMESTAMP NULLABLE,
    
    FOREIGN KEY (organization_id) REFERENCES organizations(id),
    UNIQUE KEY unique_email_per_org (organization_id, email),
    INDEX idx_organization (organization_id),
    INDEX idx_status (status)
);
```

**Relationships**:
```
User (1) ──── (Many) UserSession
User (1) ──── (Many) UserCredential
User (1) ──── (Many) UserProfile
User (1) ──── (Many) RoleAssignment
User (1) ──── (Many) UserPermission
```

---

### 5. Roles Table
**Purpose**: Store role definitions  
**Scope**: Per organization (organization_id)

```sql
CREATE TABLE roles (
    id VARCHAR(36) PRIMARY KEY,
    organization_id VARCHAR(36) NOT NULL,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) NOT NULL,
    description TEXT NULLABLE,
    is_system BOOLEAN DEFAULT FALSE,        -- System roles cannot be deleted
    level INT NULLABLE,                     -- Role hierarchy level
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    deleted_at TIMESTAMP NULLABLE,
    
    FOREIGN KEY (organization_id) REFERENCES organizations(id),
    UNIQUE KEY unique_slug_per_org (organization_id, slug),
    INDEX idx_organization (organization_id),
    INDEX idx_is_system (is_system)
);
```

**Example Roles**:
```
organization_id | name              | slug            | is_system | level
─────────────────────────────────────────────────────────────────────────
    org-1       | System Admin      | system-admin    | TRUE      | 1
    org-1       | Organization Admin| org-admin       | FALSE     | 2
    org-1       | Manager           | manager         | FALSE     | 3
    org-1       | Team Lead         | team-lead       | FALSE     | 4
    org-1       | Member            | member          | FALSE     | 5
```

---

### 6. Permissions Table
**Purpose**: Store permission definitions  
**Scope**: Per organization (organization_id)

```sql
CREATE TABLE permissions (
    id VARCHAR(36) PRIMARY KEY,
    organization_id VARCHAR(36) NOT NULL,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) NOT NULL,
    module VARCHAR(50) NOT NULL,            -- 'users', 'roles', 'organizations'
    description TEXT NULLABLE,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    deleted_at TIMESTAMP NULLABLE,
    
    FOREIGN KEY (organization_id) REFERENCES organizations(id),
    UNIQUE KEY unique_slug_per_org (organization_id, slug),
    INDEX idx_organization (organization_id),
    INDEX idx_module (module)
);
```

**Example Permissions**:
```
organization_id | name           | slug           | module
─────────────────────────────────────────────────────────
    org-1       | View Users     | users.view     | users
    org-1       | Create Users   | users.create   | users
    org-1       | Update Users   | users.update   | users
    org-1       | Delete Users   | users.delete   | users
    org-1       | View Roles     | roles.view     | roles
    org-1       | Create Roles   | roles.create   | roles
    ...
```

---

### 7. RolePermissions Table
**Purpose**: Map permissions to roles (Many-to-Many)  
**Scope**: Per organization (organization_id)

```sql
CREATE TABLE role_permissions (
    role_id VARCHAR(36) NOT NULL,
    permission_id VARCHAR(36) NOT NULL,
    organization_id VARCHAR(36) NOT NULL,
    created_at TIMESTAMP,
    
    PRIMARY KEY (role_id, permission_id),
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
    FOREIGN KEY (organization_id) REFERENCES organizations(id),
    INDEX idx_permission (permission_id)
);
```

---

### 8. RoleAssignments Table
**Purpose**: Assign roles to users with scope and expiration  
**Scope**: Per organization (organization_id)

```sql
CREATE TABLE role_assignments (
    id VARCHAR(36) PRIMARY KEY,
    organization_id VARCHAR(36) NOT NULL,
    user_id VARCHAR(36) NOT NULL,
    role_id VARCHAR(36) NOT NULL,
    assigned_by VARCHAR(36) NULLABLE,       -- Admin who assigned
    assigned_at TIMESTAMP NOT NULL,
    expires_at TIMESTAMP NULLABLE,          -- Optional expiration
    scope VARCHAR(50) DEFAULT 'organization', -- 'global', 'organization', 'department', 'team', 'user'
    scope_id VARCHAR(36) NULLABLE,          -- Specific scope identifier
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    deleted_at TIMESTAMP NULLABLE,
    
    FOREIGN KEY (organization_id) REFERENCES organizations(id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES users(id),
    INDEX idx_user (user_id),
    INDEX idx_role (role_id),
    INDEX idx_scope (scope),
    INDEX idx_expires (expires_at)
);
```

**Scope Values**:
| Scope | Meaning | scope_id | Example |
|-------|---------|----------|---------|
| `global` | System-wide | NULL | System admin role |
| `organization` | Entire org | NULL | Org admin role |
| `department` | Specific dept | dept_id | Dept manager role |
| `team` | Specific team | team_id | Team lead role |
| `user` | Individual user | user_id | User-specific permissions |

---

### 9. UserPermissions Table
**Purpose**: Grant permissions directly to users (bypassing roles)  
**Scope**: Per organization (organization_id)

```sql
CREATE TABLE user_permissions (
    id VARCHAR(36) PRIMARY KEY,
    organization_id VARCHAR(36) NOT NULL,
    user_id VARCHAR(36) NOT NULL,
    permission_id VARCHAR(36) NOT NULL,
    granted_by VARCHAR(36) NULLABLE,        -- Admin who granted
    granted_at TIMESTAMP NOT NULL,
    expires_at TIMESTAMP NULLABLE,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    deleted_at TIMESTAMP NULLABLE,
    
    FOREIGN KEY (organization_id) REFERENCES organizations(id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES users(id),
    UNIQUE KEY unique_user_permission (user_id, permission_id),
    INDEX idx_user (user_id),
    INDEX idx_permission (permission_id)
);
```

---

### 10. UserSessions Table
**Purpose**: Track user login sessions and tokens  
**Scope**: Per organization (organization_id)

```sql
CREATE TABLE user_sessions (
    id VARCHAR(36) PRIMARY KEY,
    organization_id VARCHAR(36) NOT NULL,
    user_id VARCHAR(36) NOT NULL,
    token VARCHAR(500) NOT NULL UNIQUE,     -- Bearer token
    ip_address VARCHAR(45) NULLABLE,
    user_agent TEXT NULLABLE,
    is_active BOOLEAN DEFAULT TRUE,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    
    FOREIGN KEY (organization_id) REFERENCES organizations(id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user (user_id),
    INDEX idx_token (token),
    INDEX idx_expires (expires_at)
);
```

---

## 🔄 Complete Entity Relationship Diagram

```
┌─────────────────────────────────────────────────────────────────┐
│                    ORGANIZATION HIERARCHY                       │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  organizations                                                  │
│  ├─ id (PK)                                                    │
│  ├─ parent_id (FK) ──────┐                                     │
│  ├─ type                 │                                     │
│  ├─ name                 │                                     │
│  ├─ code                 │                                     │
│  └─ ...                  │                                     │
│       │                  │                                     │
│       └──────────────────┘ (Self-referencing)                 │
│                                                                 │
│  org_unit_closure (Adjacency List)                            │
│  ├─ ancestor_id (FK) ──────┐                                  │
│  ├─ descendant_id (FK) ────┤─── organizations                 │
│  └─ depth                  │                                  │
│                            └──────┘                            │
│                                                                 │
│  hierarchy_levels                                              │
│  ├─ id (PK)                                                    │
│  ├─ organization_id (FK) ──────┐                               │
│  ├─ name                       │                               │
│  ├─ level                      │                               │
│  └─ ...                        │                               │
│                                └─── organizations              │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────┐
│                      USER MANAGEMENT                            │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  users                                                          │
│  ├─ id (PK)                                                    │
│  ├─ organization_id (FK) ──────┐                               │
│  ├─ email                      │                               │
│  ├─ password                   │                               │
│  └─ ...                        │                               │
│       │                        └─── organizations              │
│       │                                                         │
│       ├──── user_sessions                                      │
│       │      ├─ id (PK)                                        │
│       │      ├─ user_id (FK)                                   │
│       │      ├─ token                                          │
│       │      └─ expires_at                                     │
│       │                                                         │
│       ├──── role_assignments                                   │
│       │      ├─ id (PK)                                        │
│       │      ├─ user_id (FK)                                   │
│       │      ├─ role_id (FK) ──────┐                           │
│       │      ├─ scope              │                           │
│       │      ├─ scope_id           │                           │
│       │      └─ expires_at         │                           │
│       │                            └─── roles                  │
│       │                                                         │
│       └──── user_permissions                                   │
│              ├─ id (PK)                                        │
│              ├─ user_id (FK)                                   │
│              ├─ permission_id (FK) ──────┐                     │
│              └─ expires_at               │                     │
│                                          └─── permissions      │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────┐
│                   ROLES & PERMISSIONS                           │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  roles                                                          │
│  ├─ id (PK)                                                    │
│  ├─ organization_id (FK) ──────┐                               │
│  ├─ name                       │                               │
│  ├─ slug                       │                               │
│  └─ ...                        │                               │
│       │                        └─── organizations              │
│       │                                                         │
│       └──── role_permissions                                   │
│              ├─ role_id (FK)                                   │
│              ├─ permission_id (FK) ──────┐                     │
│              └─ organization_id           │                    │
│                                           │                    │
│  permissions                              │                    │
│  ├─ id (PK)                              │                    │
│  ├─ organization_id (FK) ────────────────┼─── organizations   │
│  ├─ name                                 │                    │
│  ├─ slug                                 │                    │
│  ├─ module                               │                    │
│  └─ ...                                  │                    │
│       │                                  │                    │
│       └──────────────────────────────────┘                    │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

---

## 🔐 Authorization Query Examples

### Get User's Effective Permissions
```sql
-- Direct permissions
SELECT DISTINCT p.slug
FROM user_permissions up
JOIN permissions p ON p.id = up.permission_id
WHERE up.user_id = ? 
  AND up.organization_id = ?
  AND (up.expires_at IS NULL OR up.expires_at > NOW())

UNION

-- Permissions via roles
SELECT DISTINCT p.slug
FROM role_assignments ra
JOIN role_permissions rp ON rp.role_id = ra.role_id
JOIN permissions p ON p.id = rp.permission_id
WHERE ra.user_id = ?
  AND ra.organization_id = ?
  AND (ra.expires_at IS NULL OR ra.expires_at > NOW());
```

### Get User's Roles at Specific Scope
```sql
SELECT r.*, ra.scope, ra.scope_id, ra.expires_at
FROM role_assignments ra
JOIN roles r ON r.id = ra.role_id
WHERE ra.user_id = ?
  AND ra.organization_id = ?
  AND ra.scope = ?
  AND (ra.scope_id = ? OR ra.scope_id IS NULL)
  AND (ra.expires_at IS NULL OR ra.expires_at > NOW());
```

### Get Organization Hierarchy
```sql
SELECT o.*, c.depth
FROM organizations o
JOIN org_unit_closure c ON c.descendant_id = o.id
WHERE c.ancestor_id = ?
ORDER BY c.depth;
```

### Get Organization Ancestors
```sql
SELECT o.*, c.depth
FROM organizations o
JOIN org_unit_closure c ON c.ancestor_id = o.id
WHERE c.descendant_id = ?
ORDER BY c.depth DESC;
```

---

## 📈 Scalability Considerations

### Indexing Strategy
```sql
-- Organization hierarchy queries
CREATE INDEX idx_org_parent ON organizations(parent_id);
CREATE INDEX idx_org_status ON organizations(status);

-- User queries
CREATE INDEX idx_user_org ON users(organization_id);
CREATE INDEX idx_user_email ON users(email);
CREATE INDEX idx_user_status ON users(status);

-- Role assignment queries
CREATE INDEX idx_ra_user ON role_assignments(user_id);
CREATE INDEX idx_ra_role ON role_assignments(role_id);
CREATE INDEX idx_ra_scope ON role_assignments(scope);
CREATE INDEX idx_ra_expires ON role_assignments(expires_at);

-- Permission queries
CREATE INDEX idx_perm_module ON permissions(module);
CREATE INDEX idx_rp_permission ON role_permissions(permission_id);

-- Session queries
CREATE INDEX idx_session_token ON user_sessions(token);
CREATE INDEX idx_session_expires ON user_sessions(expires_at);
```

### Query Optimization
1. **Use closure table** for hierarchical queries (O(1) instead of O(n))
2. **Cache permissions** in session/memory (refresh on role change)
3. **Batch load** related data (roles, permissions, organizations)
4. **Soft deletes** for audit trail (use deleted_at index)
5. **Partition large tables** by organization_id if needed

---

## 🔄 Data Flow Example

### User Login Flow
```
1. User submits credentials
   ↓
2. Verify email & password
   ↓
3. Create UserSession with token
   ↓
4. Return token to client
   ↓
5. Client includes token in Authorization header
   ↓
6. API validates token from UserSession
   ↓
7. Load User from users table
   ↓
8. Load User's roles from role_assignments
   ↓
9. Load permissions from role_permissions + user_permissions
   ↓
10. Store in request context for authorization checks
```

### Permission Check Flow
```
1. Request comes in with Bearer token
   ↓
2. Validate token exists in user_sessions
   ↓
3. Load User and organization_id
   ↓
4. Check if permission required
   ↓
5. Query effective permissions (cached or fresh)
   ↓
6. Check if permission in list
   ↓
7. Check scope match (if scope-specific)
   ↓
8. Allow or deny request
```

---

**Document Generated**: October 29, 2025  
**System**: ERP Backend API  
**Architecture**: DDD Modular Monolith with Multi-Level Hierarchy
