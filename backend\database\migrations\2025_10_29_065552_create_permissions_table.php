<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('permissions', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('organization_id')->comment('Organization this permission belongs to');
            $table->string('name')->comment('Permission name');
            $table->string('slug')->comment('Permission slug (e.g., users.create)');
            $table->string('module', 50)->comment('Module this permission belongs to');
            $table->text('description')->nullable();
            $table->timestamps();
            $table->softDeletes();
            
            // Unique constraint with organization scoping
            $table->unique(['organization_id', 'slug']);
            
            // Indexes
            $table->index('organization_id');
            $table->index('slug');
            $table->index('module');
            
            // Foreign key
            $table->foreign('organization_id')
                  ->references('id')
                  ->on('organizations')
                  ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('permissions');
    }
};
