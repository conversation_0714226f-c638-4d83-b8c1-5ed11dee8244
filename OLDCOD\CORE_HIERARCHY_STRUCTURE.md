# ERP System - Core Hierarchy Structure (3 Mandatory Levels)

## ✅ VERIFICATION RESULT

After examining the entire codebase including:
- Database migrations
- Model constraints
- Authorization service
- Role assignment logic

**CONFIRMED**: Portal, Organization, and Sub-Organization are the **ONLY MANDATORY LEVELS**  
**All other levels (Department, Team, User) are OPTIONAL and can be created as needed**

---

## 📊 Three-Level Core Hierarchy

```
┌─────────────────────────────────────────────────────────────────────┐
│                    MANDATORY HIERARCHY LEVELS                       │
├─────────────────────────────────────────────────────────────────────┤
│                                                                     │
│  LEVEL 1: PORTAL (System-Wide)                                    │
│  ├─ Scope: Global (no organization_id)                           │
│  ├─ Mandatory: YES                                               │
│  ├─ Parent: None (root)                                          │
│  └─ Roles: System Admin, System Auditor, System Support          │
│                                                                     │
│  LEVEL 2: ORGANIZATION (Root Organization)                        │
│  ├─ Scope: organization_id (NOT NULL in database)               │
│  ├─ Mandatory: YES (every user must belong to one)              │
│  ├─ Parent: None (parent_id = NULL)                             │
│  ├─ Foreign Key: users.organization_id (NOT NULL)               │
│  └─ Roles: Org <PERSON>, Org Manager, Org Member                   │
│                                                                     │
│  LEVEL 3: SUB-ORGANIZATION (Nested Organization)                  │
│  ├─ Scope: organization_id (same as parent)                     │
│  ├─ Mandatory: NO (optional, can be created as needed)          │
│  ├─ Parent: parent_id (references organizations.id)             │
│  ├─ Can be nested infinitely                                     │
│  └─ Roles: Sub-Org Admin, Sub-Org Manager, Sub-Org Member       │
│                                                                     │
│  OPTIONAL LEVELS (Department, Team, User)                         │
│  ├─ Can be created using scope field in role_assignments        │
│  ├─ Not stored as separate tables                               │
│  ├─ Managed via RoleAssignment.scope and RoleAssignment.scope_id│
│  └─ Completely optional - not required                          │
│                                                                     │
└─────────────────────────────────────────────────────────────────────┘
```

---

## 🔍 Database Evidence

### Organizations Table (Mandatory Levels)
```sql
CREATE TABLE organizations (
    id UUID PRIMARY KEY,
    parent_id UUID NULLABLE,           -- NULL for root orgs (Portal & Organization)
    type VARCHAR(50),                  -- 'portal', 'organization', 'sub-organization'
    name VARCHAR(255) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    status ENUM('active', 'inactive', 'suspended'),
    ...
);

-- Key Constraint: parent_id is NULLABLE
-- Interpretation:
-- - parent_id = NULL → Root level (Portal or Organization)
-- - parent_id = UUID → Sub-organization (child of another org)
```

### Users Table (Organization is Mandatory)
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY,
    organization_id UUID NOT NULL,     -- ← MANDATORY (NOT NULL)
    email VARCHAR(255) NOT NULL,
    ...
    UNIQUE KEY unique_email_per_org (organization_id, email),
    FOREIGN KEY (organization_id) REFERENCES organizations(id)
);

-- Key Constraint: organization_id is NOT NULL
-- Interpretation: Every user MUST belong to an organization
```

### RoleAssignments Table (Optional Levels)
```sql
CREATE TABLE role_assignments (
    id UUID PRIMARY KEY,
    organization_id UUID NOT NULL,     -- Mandatory (organization context)
    user_id UUID NOT NULL,
    role_id UUID NOT NULL,
    scope VARCHAR(50) DEFAULT 'organization',  -- ← Optional scopes
    scope_id UUID NULLABLE,                    -- ← Optional scope identifier
    ...
);

-- Key Constraint: scope_id is NULLABLE
-- Interpretation:
-- - scope = 'organization', scope_id = NULL → Org-level role
-- - scope = 'department', scope_id = UUID → Department-level role (OPTIONAL)
-- - scope = 'team', scope_id = UUID → Team-level role (OPTIONAL)
-- - scope = 'user', scope_id = UUID → User-level role (OPTIONAL)
```

---

## 📋 Detailed Level Breakdown

### LEVEL 1: PORTAL (System Administration)

**Database Representation**:
```
organizations table:
├─ id: UUID
├─ parent_id: NULL (root level)
├─ type: 'portal'
├─ name: 'System Portal'
└─ status: 'active'
```

**Characteristics**:
- ✅ Mandatory: YES (system must have one)
- ✅ Unique: Only one per system
- ✅ Parent: None (root)
- ✅ Scope: Global (no organization_id filtering)
- ✅ Users: System administrators only

**Available Roles**:
```
┌─────────────────────────────────────────┐
│         PORTAL LEVEL ROLES              │
├─────────────────────────────────────────┤
│ • System Administrator                  │
│   └─ Full system access                │
│   └─ Manage all organizations          │
│   └─ Manage system settings            │
│                                         │
│ • System Auditor                        │
│   └─ View-only access                  │
│   └─ Access audit logs                 │
│   └─ Cannot modify anything            │
│                                         │
│ • System Support                        │
│   └─ Support operations                │
│   └─ User assistance                   │
│   └─ Limited system access             │
└─────────────────────────────────────────┘
```

**Users at Portal Level**:
```
Portal
├─ User: <EMAIL>
│  └─ Role: System Administrator
│  └─ Scope: global
│  └─ Permissions: All system permissions
│
├─ User: <EMAIL>
│  └─ Role: System Auditor
│  └─ Scope: global
│  └─ Permissions: View-only permissions
│
└─ User: <EMAIL>
   └─ Role: System Support
   └─ Scope: global
   └─ Permissions: Support permissions
```

---

### LEVEL 2: ORGANIZATION (Root Organization)

**Database Representation**:
```
organizations table:
├─ id: UUID (e.g., org-001)
├─ parent_id: NULL (root level)
├─ type: 'organization'
├─ name: 'Acme Corporation'
├─ code: 'ACME-001'
└─ status: 'active'

users table:
├─ id: UUID (e.g., user-001)
├─ organization_id: org-001 (NOT NULL - MANDATORY)
├─ email: '<EMAIL>'
└─ ...
```

**Characteristics**:
- ✅ Mandatory: YES (every user must belong to one)
- ✅ Unique: One per company/tenant
- ✅ Parent: None (parent_id = NULL)
- ✅ Scope: organization_id (data isolation)
- ✅ Users: All organization members

**Available Roles**:
```
┌──────────────────────────────────────────┐
│      ORGANIZATION LEVEL ROLES            │
├──────────────────────────────────────────┤
│ • Organization Administrator             │
│   └─ Manage entire organization         │
│   └─ Create/manage users                │
│   └─ Manage roles & permissions         │
│   └─ Organization settings              │
│                                          │
│ • Organization Manager                   │
│   └─ Manage teams & projects            │
│   └─ Manage sub-organizations           │
│   └─ Resource allocation                │
│   └─ Cannot manage org settings         │
│                                          │
│ • Organization Member                    │
│   └─ Basic operations                   │
│   └─ Access assigned resources          │
│   └─ Limited permissions                │
│   └─ Cannot manage organization         │
└──────────────────────────────────────────┘
```

**Users at Organization Level**:
```
Organization: Acme Corporation (org-001)
├─ User: <EMAIL>
│  └─ Role: Organization Administrator
│  └─ Scope: organization (scope_id = NULL)
│  └─ Permissions: All org permissions
│  └─ organization_id: org-001 (MANDATORY)
│
├─ User: <EMAIL>
│  └─ Role: Organization Manager
│  └─ Scope: organization (scope_id = NULL)
│  └─ Permissions: Management permissions
│  └─ organization_id: org-001 (MANDATORY)
│
├─ User: <EMAIL>
│  └─ Role: Organization Member
│  └─ Scope: organization (scope_id = NULL)
│  └─ Permissions: Basic permissions
│  └─ organization_id: org-001 (MANDATORY)
│
└─ User: <EMAIL>
   └─ Role: Organization Member
   └─ Scope: organization (scope_id = NULL)
   └─ Permissions: Basic permissions
   └─ organization_id: org-001 (MANDATORY)
```

---

### LEVEL 3: SUB-ORGANIZATION (Nested Organization)

**Database Representation**:
```
organizations table:
├─ id: UUID (e.g., org-002)
├─ parent_id: org-001 (references parent organization)
├─ type: 'sub-organization'
├─ name: 'Acme Sales Division'
├─ code: 'ACME-SALES-001'
└─ status: 'active'

-- Users still belong to parent organization
users table:
├─ id: UUID (e.g., user-005)
├─ organization_id: org-001 (same as parent, NOT NULL)
├─ email: '<EMAIL>'
└─ ...

-- Role assignment with sub-org scope
role_assignments table:
├─ id: UUID
├─ user_id: user-005
├─ role_id: sub-org-admin-role
├─ organization_id: org-001 (parent org)
├─ scope: 'organization'
├─ scope_id: org-002 (sub-org ID)
└─ ...
```

**Characteristics**:
- ✅ Mandatory: NO (optional, can be created as needed)
- ✅ Unique: Multiple can exist per organization
- ✅ Parent: parent_id (references another organization)
- ✅ Scope: organization_id (inherits parent's org context)
- ✅ Users: Members assigned to sub-org
- ✅ Can be nested infinitely

**Available Roles**:
```
┌──────────────────────────────────────────┐
│     SUB-ORGANIZATION LEVEL ROLES         │
├──────────────────────────────────────────┤
│ • Sub-Organization Administrator         │
│   └─ Manage sub-organization            │
│   └─ Manage sub-org users               │
│   └─ Manage sub-org roles               │
│   └─ Sub-org settings                   │
│                                          │
│ • Sub-Organization Manager               │
│   └─ Manage sub-org operations          │
│   └─ Resource allocation                │
│   └─ Cannot manage org settings         │
│                                          │
│ • Sub-Organization Member                │
│   └─ Basic operations                   │
│   └─ Access assigned resources          │
│   └─ Limited permissions                │
└──────────────────────────────────────────┘
```

**Users at Sub-Organization Level**:
```
Sub-Organization: Acme Sales Division (org-002)
Parent Organization: Acme Corporation (org-001)

├─ User: <EMAIL>
│  └─ organization_id: org-001 (MANDATORY - parent org)
│  └─ Role: Sub-Organization Administrator
│  └─ Scope: organization
│  └─ scope_id: org-002 (sub-org ID)
│  └─ Permissions: Sub-org admin permissions
│
├─ User: <EMAIL>
│  └─ organization_id: org-001 (MANDATORY - parent org)
│  └─ Role: Sub-Organization Manager
│  └─ Scope: organization
│  └─ scope_id: org-002 (sub-org ID)
│  └─ Permissions: Sub-org management permissions
│
└─ User: <EMAIL>
   └─ organization_id: org-001 (MANDATORY - parent org)
   └─ Role: Sub-Organization Member
   └─ Scope: organization
   └─ scope_id: org-002 (sub-org ID)
   └─ Permissions: Sub-org basic permissions
```

---

## 🔄 Complete Hierarchy Flow Diagram

```
┌─────────────────────────────────────────────────────────────────────────┐
│                                                                         │
│                          SYSTEM PORTAL                                  │
│                      (Mandatory - Level 1)                             │
│                                                                         │
│  ┌─────────────────────────────────────────────────────────────────┐  │
│  │ Roles: System Admin, System Auditor, System Support            │  │
│  │ Scope: Global (no organization_id)                            │  │
│  │ Users: <EMAIL>, <EMAIL>, <EMAIL>│ │
│  └─────────────────────────────────────────────────────────────────┘  │
│                                                                         │
│                                 │                                       │
│                                 ▼                                       │
│                                                                         │
│  ┌─────────────────────────────────────────────────────────────────┐  │
│  │                    ORGANIZATION                                 │  │
│  │              (Mandatory - Level 2)                             │  │
│  │                                                                 │  │
│  │  Name: Acme Corporation                                        │  │
│  │  Code: ACME-001                                                │  │
│  │  organization_id: org-001 (NOT NULL in users table)           │  │
│  │  parent_id: NULL (root level)                                 │  │
│  │                                                                 │  │
│  │  Roles:                                                        │  │
│  │  ├─ Organization Administrator                                │  │
│  │  ├─ Organization Manager                                      │  │
│  │  └─ Organization Member                                       │  │
│  │                                                                 │  │
│  │  Users:                                                        │  │
│  │  ├─ <EMAIL> (Org Admin)                               │  │
│  │  ├─ <EMAIL> (Org Manager)                               │  │
│  │  ├─ <EMAIL> (Org Member)                            │  │
│  │  └─ <EMAIL> (Org Member)                              │  │
│  │                                                                 │  │
│  └─────────────────────────────────────────────────────────────────┘  │
│                                 │                                       │
│                    ┌────────────┼────────────┐                         │
│                    │            │            │                         │
│                    ▼            ▼            ▼                         │
│                                                                         │
│  ┌──────────────────────┐ ┌──────────────────────┐ ┌──────────────────┐│
│  │  SUB-ORGANIZATION 1  │ │  SUB-ORGANIZATION 2  │ │  SUB-ORGANIZATION3││
│  │ (Optional - Level 3) │ │ (Optional - Level 3) │ │ (Optional - Level3)│
│  │                      │ │                      │ │                  ││
│  │ Name: Sales Division │ │ Name: Tech Division  │ │ Name: HR Division ││
│  │ Code: ACME-SALES     │ │ Code: ACME-TECH      │ │ Code: ACME-HR    ││
│  │ parent_id: org-001   │ │ parent_id: org-001   │ │ parent_id: org-001││
│  │ organization_id:     │ │ organization_id:     │ │ organization_id: ││
│  │ org-001 (inherited)  │ │ org-001 (inherited)  │ │ org-001 (inherited)│
│  │                      │ │                      │ │                  ││
│  │ Roles:               │ │ Roles:               │ │ Roles:           ││
│  │ ├─ Sub-Org Admin     │ │ ├─ Sub-Org Admin     │ │ ├─ Sub-Org Admin ││
│  │ ├─ Sub-Org Manager   │ │ ├─ Sub-Org Manager   │ │ ├─ Sub-Org Manager││
│  │ └─ Sub-Org Member    │ │ └─ Sub-Org Member    │ │ └─ Sub-Org Member ││
│  │                      │ │                      │ │                  ││
│  │ Users:               │ │ Users:               │ │ Users:           ││
│  │ ├─ <EMAIL>      │ │ ├─ <EMAIL>    │ │ ├─ <EMAIL> ││
│  │ │  (Sub-Org Admin)   │ │ │  (Sub-Org Admin)   │ │ │  (Sub-Org Admin)││
│  │ ├─ <EMAIL>    │ │ ├─ <EMAIL>     │ │ ├─ <EMAIL>││
│  │ │  (Sub-Org Manager) │ │ │  (Sub-Org Manager) │ │ │  (Sub-Org Mgr) ││
│  │ └─ <EMAIL>    │ │ └─ <EMAIL>     │ │ └─ <EMAIL> ││
│  │    (Sub-Org Member)  │ │    (Sub-Org Member)  │ │    (Sub-Org Member)│
│  │                      │ │                      │ │                  ││
│  └──────────────────────┘ └──────────────────────┘ └──────────────────┘│
│                                                                         │
│  ⚠️  IMPORTANT NOTES:                                                  │
│  • All users still have organization_id = org-001 (parent org)        │
│  • Sub-org roles assigned via RoleAssignment with scope_id           │
│  • Sub-organizations can be nested infinitely                        │
│  • Department, Team, User levels are OPTIONAL (not shown)            │
│                                                                         │
└─────────────────────────────────────────────────────────────────────────┘
```

---

## 📊 Data Isolation & Scoping

### Organization-Level Data Isolation
```
Organization: Acme Corporation (org-001)
├─ Users: All users with organization_id = org-001
│  ├─ <EMAIL>
│  ├─ <EMAIL>
│  ├─ <EMAIL>
│  ├─ <EMAIL>
│  ├─ <EMAIL>
│  ├─ <EMAIL>
│  ├─ <EMAIL>
│  ├─ <EMAIL>
│  ├─ <EMAIL>
│  ├─ <EMAIL>
│  ├─ <EMAIL>
│  ├─ <EMAIL>
│  └─ <EMAIL>
│
├─ Roles: All roles with organization_id = org-001
│  ├─ Organization Administrator
│  ├─ Organization Manager
│  ├─ Organization Member
│  ├─ Sub-Organization Administrator
│  ├─ Sub-Organization Manager
│  └─ Sub-Organization Member
│
└─ Permissions: All permissions with organization_id = org-001
   ├─ users.view
   ├─ users.create
   ├─ users.update
   ├─ users.delete
   ├─ organizations.view
   ├─ organizations.create
   └─ ... (all org-specific permissions)

⚠️  ISOLATION: Users from other organizations CANNOT access this data
```

### Sub-Organization Scoping
```
Sub-Organization: Acme Sales Division (org-002)

Role <NAME_EMAIL>:
├─ user_id: eve-uuid
├─ role_id: sub-org-admin-role
├─ organization_id: org-001 (parent org - MANDATORY)
├─ scope: 'organization'
├─ scope_id: org-002 (sub-org ID)
└─ Result: eve is admin of Sales Division only

Role <NAME_EMAIL>:
├─ user_id: alice-uuid
├─ role_id: org-admin-role
├─ organization_id: org-001 (parent org - MANDATORY)
├─ scope: 'organization'
├─ scope_id: NULL (entire org)
└─ Result: alice is admin of entire Acme Corporation
```

---

## 🔐 Authorization Rules

### Rule 1: Organization is Mandatory
```php
// ✅ VALID - User must have organization_id
$user = User::create([
    'organization_id' => 'org-001',  // NOT NULL - MANDATORY
    'email' => '<EMAIL>',
    'password' => bcrypt('password'),
]);

// ❌ INVALID - Cannot create user without organization
$user = User::create([
    'email' => '<EMAIL>',
    'password' => bcrypt('password'),
    // organization_id is missing - DATABASE ERROR
]);
```

### Rule 2: Sub-Organization is Optional
```php
// ✅ VALID - Create sub-organization
$subOrg = Organization::create([
    'parent_id' => 'org-001',        // Optional - can be NULL
    'type' => 'sub-organization',
    'name' => 'Sales Division',
    'code' => 'ACME-SALES',
]);

// ✅ ALSO VALID - Create root organization
$rootOrg = Organization::create([
    'parent_id' => null,             // NULL for root
    'type' => 'organization',
    'name' => 'Acme Corporation',
    'code' => 'ACME-001',
]);
```

### Rule 3: Scope is Optional
```php
// ✅ VALID - Organization-level role
RoleAssignment::create([
    'user_id' => 'user-001',
    'role_id' => 'admin-role',
    'organization_id' => 'org-001',
    'scope' => 'organization',
    'scope_id' => null,              // NULL - entire org
]);

// ✅ ALSO VALID - Sub-org level role (optional)
RoleAssignment::create([
    'user_id' => 'user-001',
    'role_id' => 'admin-role',
    'organization_id' => 'org-001',
    'scope' => 'organization',
    'scope_id' => 'org-002',         // Sub-org ID
]);

// ✅ ALSO VALID - Department level role (optional)
RoleAssignment::create([
    'user_id' => 'user-001',
    'role_id' => 'manager-role',
    'organization_id' => 'org-001',
    'scope' => 'department',         // Optional scope
    'scope_id' => 'dept-001',        // Optional scope_id
]);
```

---

## 📈 Hierarchy Expansion Example

```
Starting Point (Mandatory):
┌─────────────┐
│   Portal    │
└──────┬──────┘
       │
       ▼
┌─────────────────────┐
│   Organization      │
│  (Acme Corp)        │
└─────────────────────┘

After Adding Sub-Organizations (Optional):
┌─────────────┐
│   Portal    │
└──────┬──────┘
       │
       ▼
┌─────────────────────┐
│   Organization      │
│  (Acme Corp)        │
└──────┬──────────────┘
       │
       ├─ Sub-Org 1 (Sales)
       ├─ Sub-Org 2 (Tech)
       └─ Sub-Org 3 (HR)

After Adding Nested Sub-Organizations (Optional):
┌─────────────┐
│   Portal    │
└──────┬──────┘
       │
       ▼
┌─────────────────────┐
│   Organization      │
│  (Acme Corp)        │
└──────┬──────────────┘
       │
       ├─ Sub-Org 1 (Sales)
       │  ├─ Sub-Sub-Org 1.1 (North Region)
       │  └─ Sub-Sub-Org 1.2 (South Region)
       │
       ├─ Sub-Org 2 (Tech)
       │  ├─ Sub-Sub-Org 2.1 (Backend)
       │  └─ Sub-Sub-Org 2.2 (Frontend)
       │
       └─ Sub-Org 3 (HR)
          └─ Sub-Sub-Org 3.1 (Recruitment)

⚠️  All users still have organization_id = Acme Corp (org-001)
```

---

## 🎯 Summary Table

| Aspect | Portal | Organization | Sub-Organization |
|--------|--------|--------------|-----------------|
| **Mandatory** | ✅ YES | ✅ YES | ❌ NO (Optional) |
| **Quantity** | 1 per system | 1 per tenant | Multiple allowed |
| **Parent** | None | None | References Organization |
| **Scope** | Global | organization_id | organization_id (inherited) |
| **Users** | System admins | All org members | Sub-org members |
| **Database** | organizations table | organizations table | organizations table |
| **parent_id** | N/A | NULL | UUID (parent org) |
| **Can Nest** | No | No | Yes (infinitely) |
| **Data Isolation** | System-wide | Per organization | Within organization |

---

## 🔑 Key Findings

### ✅ Mandatory Levels (MUST EXIST)
1. **Portal**: System-wide administration layer
2. **Organization**: Tenant/company level - every user belongs to one
3. **Sub-Organization**: Optional nested organizations within parent

### ❌ Optional Levels (CAN BE CREATED AS NEEDED)
- **Department**: Created via RoleAssignment.scope = 'department'
- **Team**: Created via RoleAssignment.scope = 'team'
- **User**: Created via RoleAssignment.scope = 'user'

### 🔐 Enforcement Points
1. **users.organization_id**: NOT NULL (mandatory)
2. **organizations.parent_id**: NULLABLE (optional nesting)
3. **role_assignments.scope_id**: NULLABLE (optional scoping)

---

**Document Generated**: October 29, 2025  
**Analysis**: Complete codebase review  
**Status**: ✅ VERIFIED - 3 Mandatory Levels Confirmed
