<?php

namespace Tests\Unit\Jobs;

use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;
use Illuminate\Foundation\Testing\DatabaseMigrations;
use Illuminate\Support\Facades\Queue;
use App\Jobs\LogResourceUsageJob;
use App\Jobs\CleanupOldUsageLogsJob;
use App\Modules\Organizations\Domain\Models\Organization;
use App\Modules\Billing\Domain\Models\Plan;
use App\Modules\Billing\Domain\Models\Subscription;
use App\Modules\Billing\Domain\Models\ResourceUsageLog;
use App\Modules\Billing\Application\Services\UsageTrackingService;

class ScheduledJobsTest extends TestCase
{
    use DatabaseMigrations;

    protected Organization $tenant;
    protected Plan $plan;
    protected Subscription $subscription;

    protected function setUp(): void
    {
        parent::setUp();

        $portalOwner = Organization::factory()->create([
            'type' => 'portal_owner',
            'parent_id' => null,
        ]);

        $this->tenant = Organization::factory()->create([
            'type' => 'tenant',
            'parent_id' => $portalOwner->id,
        ]);

        $this->plan = Plan::factory()->create([
            'user_limit' => 10,
            'sub_org_limit' => 5,
            'storage_limit' => 50,
        ]);

        $this->subscription = Subscription::factory()->create([
            'organization_id' => $this->tenant->id,
            'plan_id' => $this->plan->id,
            'status' => 'active',
            'user_count' => 5,
            'sub_org_count' => 2,
            'storage_used' => 10 * 1024 * 1024 * 1024,
        ]);
    }
    #[Test]
    public function log_resource_usage_job_can_be_dispatched()
    {
        Queue::fake();

        LogResourceUsageJob::dispatch();

        Queue::assertPushed(LogResourceUsageJob::class);
    }
    #[Test]
    public function log_resource_usage_job_logs_all_active_subscriptions()
    {
        $job = new LogResourceUsageJob();
        $job->handle(app(UsageTrackingService::class));

        // Verify logs were created
        $this->assertDatabaseHas('resource_usage_logs', [
            'subscription_id' => $this->subscription->id,
            'tenant_organization_id' => $this->tenant->id,
        ]);
    }
    #[Test]
    public function log_resource_usage_job_logs_multiple_subscriptions()
    {
        // Create additional subscription
        $anotherTenant = Organization::factory()->create([
            'type' => 'tenant',
            'parent_id' => $this->tenant->parent_id,
        ]);

        $anotherSubscription = Subscription::factory()->create([
            'organization_id' => $anotherTenant->id,
            'plan_id' => $this->plan->id,
            'status' => 'active',
        ]);

        $job = new LogResourceUsageJob();
        $job->handle(app(UsageTrackingService::class));

        // Verify logs for both subscriptions
        $this->assertDatabaseHas('resource_usage_logs', [
            'subscription_id' => $this->subscription->id,
        ]);

        $this->assertDatabaseHas('resource_usage_logs', [
            'subscription_id' => $anotherSubscription->id,
        ]);
    }
    #[Test]
    public function log_resource_usage_job_skips_inactive_subscriptions()
    {
        $inactiveSubscription = Subscription::factory()->create([
            'organization_id' => $this->tenant->id,
            'plan_id' => $this->plan->id,
            'status' => 'cancelled',
        ]);

        $job = new LogResourceUsageJob();
        $job->handle(app(UsageTrackingService::class));

        // Should have logs for active subscription
        $this->assertDatabaseHas('resource_usage_logs', [
            'subscription_id' => $this->subscription->id,
        ]);

        // Should NOT have logs for inactive subscription
        $this->assertDatabaseMissing('resource_usage_logs', [
            'subscription_id' => $inactiveSubscription->id,
        ]);
    }
    #[Test]
    public function cleanup_old_usage_logs_job_can_be_dispatched()
    {
        Queue::fake();

        CleanupOldUsageLogsJob::dispatch(90);

        Queue::assertPushed(CleanupOldUsageLogsJob::class);
    }
    #[Test]
    public function cleanup_old_usage_logs_job_deletes_old_logs()
    {
        // Create old logs
        ResourceUsageLog::factory()->count(5)->create([
            'subscription_id' => $this->subscription->id,
            'tenant_organization_id' => $this->tenant->id,
            'recorded_at' => now()->subDays(100),
        ]);

        // Create recent logs
        ResourceUsageLog::factory()->count(3)->create([
            'subscription_id' => $this->subscription->id,
            'tenant_organization_id' => $this->tenant->id,
            'recorded_at' => now()->subDays(30),
        ]);

        $this->assertEquals(8, ResourceUsageLog::count());

        $job = new CleanupOldUsageLogsJob(90);
        $job->handle(app(UsageTrackingService::class));

        // Should only have recent logs left
        $this->assertEquals(3, ResourceUsageLog::count());
    }
    #[Test]
    public function cleanup_old_usage_logs_job_respects_days_to_keep()
    {
        // Create logs at various ages
        ResourceUsageLog::factory()->create([
            'subscription_id' => $this->subscription->id,
            'tenant_organization_id' => $this->tenant->id,
            'recorded_at' => now()->subDays(100), // Too old
        ]);

        ResourceUsageLog::factory()->create([
            'subscription_id' => $this->subscription->id,
            'tenant_organization_id' => $this->tenant->id,
            'recorded_at' => now()->subDays(60), // Within limit
        ]);

        ResourceUsageLog::factory()->create([
            'subscription_id' => $this->subscription->id,
            'tenant_organization_id' => $this->tenant->id,
            'recorded_at' => now()->subDays(30), // Within limit
        ]);

        $job = new CleanupOldUsageLogsJob(90);
        $job->handle(app(UsageTrackingService::class));

        $this->assertEquals(2, ResourceUsageLog::count());
    }
    #[Test]
    public function cleanup_old_usage_logs_job_with_custom_retention()
    {
        // Create logs
        ResourceUsageLog::factory()->create([
            'subscription_id' => $this->subscription->id,
            'tenant_organization_id' => $this->tenant->id,
            'recorded_at' => now()->subDays(40),
        ]);

        ResourceUsageLog::factory()->create([
            'subscription_id' => $this->subscription->id,
            'tenant_organization_id' => $this->tenant->id,
            'recorded_at' => now()->subDays(20),
        ]);

        // Keep only 30 days
        $job = new CleanupOldUsageLogsJob(30);
        $job->handle(app(UsageTrackingService::class));

        // Should only have the recent one
        $this->assertEquals(1, ResourceUsageLog::count());
    }
    #[Test]
    public function cleanup_job_does_nothing_when_no_old_logs()
    {
        // Create only recent logs
        ResourceUsageLog::factory()->count(5)->create([
            'subscription_id' => $this->subscription->id,
            'tenant_organization_id' => $this->tenant->id,
            'recorded_at' => now()->subDays(10),
        ]);

        $job = new CleanupOldUsageLogsJob(90);
        $job->handle(app(UsageTrackingService::class));

        // All logs should remain
        $this->assertEquals(5, ResourceUsageLog::count());
    }
    #[Test]
    public function log_resource_usage_job_creates_correct_alert_levels()
    {
        // Update subscription to exceed limits
        $this->subscription->update(['user_count' => 9]); // 90% = critical

        $job = new LogResourceUsageJob();
        $job->handle(app(UsageTrackingService::class));

        // Verify critical alert level was set
        $this->assertDatabaseHas('resource_usage_logs', [
            'subscription_id' => $this->subscription->id,
            'resource_type' => 'users',
            'alert_level' => 'critical',
        ]);
    }
}
