<?php

namespace Tests\Unit\Billing;

use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Modules\Organizations\Domain\Models\Organization;
use App\Modules\Users\Domain\Models\User;
use App\Modules\Billing\Domain\Models\Plan;
use App\Modules\Billing\Domain\Models\Subscription;
use App\Modules\Billing\Application\Services\ResourceLimitService;
use App\Modules\Approvals\Domain\Services\ApprovalService;

class ResourceLimitServiceTest extends TestCase
{
    use RefreshDatabase;

    protected ResourceLimitService $service;
    protected Organization $portalOwner;
    protected Organization $tenant;
    protected Plan $plan;
    protected Subscription $subscription;

    protected function setUp(): void
    {
        parent::setUp();

        $this->service = app(ResourceLimitService::class);

        $this->portalOwner = Organization::factory()->create([
            'type' => 'portal_owner',
            'parent_id' => null,
        ]);

        $this->tenant = Organization::factory()->create([
            'type' => 'tenant',
            'parent_id' => $this->portalOwner->id,
        ]);

        $this->plan = Plan::factory()->create([
            'name' => 'Test Plan',
            'slug' => 'test',
            'user_limit' => 5,
            'sub_org_limit' => 2,
            'storage_limit' => 10, // GB
            'hierarchy_depth_limit' => 3,
            'api_calls_limit' => 1000,
            'modules' => ['inventory', 'accounting'],
        ]);

        $this->subscription = Subscription::factory()->create([
            'organization_id' => $this->tenant->id,
            'plan_id' => $this->plan->id,
            'status' => 'active',
            'user_count' => 0,
            'sub_org_count' => 0,
            'storage_used' => 0,
            'hierarchy_depth' => 1,
        ]);
    }

    #[Test]
    public function can_create_user_when_within_limit()
    {
        $result = $this->service->canCreateUser($this->tenant);

        $this->assertTrue($result['allowed']);
        $this->assertEquals('within_limit', $result['reason']);
        $this->assertEquals(5, $result['limit']);
        $this->assertEquals(0, $result['current']);
        $this->assertEquals(5, $result['remaining']);
    }

    #[Test]
    public function cannot_create_user_when_limit_exceeded()
    {
        $this->subscription->update(['user_count' => 5]);

        $result = $this->service->canCreateUser($this->tenant);

        $this->assertFalse($result['allowed']);
        $this->assertEquals('limit_exceeded', $result['reason']);
        $this->assertTrue($result['requires_approval']);
        $this->assertStringContainsString('User limit reached', $result['message']);
    }

    #[Test]
    public function can_create_user_when_unlimited()
    {
        $this->plan->update(['user_limit' => 0]);

        $result = $this->service->canCreateUser($this->tenant);

        $this->assertTrue($result['allowed']);
        $this->assertEquals('unlimited', $result['reason']);
    }

    #[Test]
    public function cannot_create_user_without_subscription()
    {
        $noSubOrg = Organization::factory()->create([
            'type' => 'tenant',
            'parent_id' => $this->portalOwner->id,
        ]);

        $result = $this->service->canCreateUser($noSubOrg);

        $this->assertFalse($result['allowed']);
        $this->assertEquals('no_active_subscription', $result['reason']);
    }

    #[Test]
    public function can_create_sub_org_when_within_limit()
    {
        $result = $this->service->canCreateSubOrganization($this->tenant);

        $this->assertTrue($result['allowed']);
        $this->assertEquals('within_limit', $result['reason']);
        $this->assertEquals(2, $result['limit']);
        $this->assertEquals(0, $result['current']);
        $this->assertEquals(2, $result['remaining']);
    }

    #[Test]
    public function cannot_create_sub_org_when_limit_exceeded()
    {
        $this->subscription->update(['sub_org_count' => 2]);

        $result = $this->service->canCreateSubOrganization($this->tenant);

        $this->assertFalse($result['allowed']);
        $this->assertEquals('limit_exceeded', $result['reason']);
        $this->assertTrue($result['requires_approval']);
    }

    #[Test]
    public function can_use_storage_when_within_limit()
    {
        $oneGB = 1024 * 1024 * 1024;
        $result = $this->service->canUseStorage($this->tenant, $oneGB);

        $this->assertTrue($result['allowed']);
        $this->assertEquals('within_limit', $result['reason']);
    }

    #[Test]
    public function cannot_use_storage_when_limit_exceeded()
    {
        $tenGB = 10 * 1024 * 1024 * 1024;
        $this->subscription->update(['storage_used' => $tenGB]);

        $oneGB = 1024 * 1024 * 1024;
        $result = $this->service->canUseStorage($this->tenant, $oneGB);

        $this->assertFalse($result['allowed']);
        $this->assertEquals('limit_exceeded', $result['reason']);
        $this->assertTrue($result['requires_approval']);
    }

    #[Test]
    public function can_create_at_hierarchy_depth_when_within_limit()
    {
        $result = $this->service->canCreateAtHierarchyDepth($this->tenant, 2);

        $this->assertTrue($result['allowed']);
        $this->assertEquals('within_limit', $result['reason']);
    }

    #[Test]
    public function cannot_create_at_hierarchy_depth_when_limit_exceeded()
    {
        $result = $this->service->canCreateAtHierarchyDepth($this->tenant, 4);

        $this->assertFalse($result['allowed']);
        $this->assertEquals('limit_exceeded', $result['reason']);
        $this->assertTrue($result['requires_approval']);
    }

    #[Test]
    public function can_access_module_when_included_in_plan()
    {
        $result = $this->service->canAccessModule($this->tenant, 'inventory');

        $this->assertTrue($result['allowed']);
        $this->assertEquals('module_included', $result['reason']);
    }

    #[Test]
    public function cannot_access_module_when_not_included_in_plan()
    {
        $result = $this->service->canAccessModule($this->tenant, 'hr');

        $this->assertFalse($result['allowed']);
        $this->assertEquals('module_not_included', $result['reason']);
        $this->assertArrayHasKey('available_modules', $result);
    }

    #[Test]
    public function increment_user_count_increases_count()
    {
        $this->assertEquals(0, $this->subscription->user_count);

        $success = $this->service->incrementUserCount($this->tenant);

        $this->assertTrue($success);
        $this->subscription->refresh();
        $this->assertEquals(1, $this->subscription->user_count);
    }

    #[Test]
    public function decrement_user_count_decreases_count()
    {
        $this->subscription->update(['user_count' => 3]);

        $success = $this->service->decrementUserCount($this->tenant);

        $this->assertTrue($success);
        $this->subscription->refresh();
        $this->assertEquals(2, $this->subscription->user_count);
    }

    #[Test]
    public function decrement_user_count_does_not_go_below_zero()
    {
        $this->subscription->update(['user_count' => 0]);

        $success = $this->service->decrementUserCount($this->tenant);

        $this->assertTrue($success);
        $this->subscription->refresh();
        $this->assertEquals(0, $this->subscription->user_count);
    }

    #[Test]
    public function increment_sub_org_count_increases_count()
    {
        $this->assertEquals(0, $this->subscription->sub_org_count);

        $success = $this->service->incrementSubOrgCount($this->tenant);

        $this->assertTrue($success);
        $this->subscription->refresh();
        $this->assertEquals(1, $this->subscription->sub_org_count);
    }

    #[Test]
    public function decrement_sub_org_count_decreases_count()
    {
        $this->subscription->update(['sub_org_count' => 2]);

        $success = $this->service->decrementSubOrgCount($this->tenant);

        $this->assertTrue($success);
        $this->subscription->refresh();
        $this->assertEquals(1, $this->subscription->sub_org_count);
    }

    #[Test]
    public function update_storage_usage_increases_usage()
    {
        $oneGB = 1024 * 1024 * 1024;
        $success = $this->service->updateStorageUsage($this->tenant, $oneGB);

        $this->assertTrue($success);
        $this->subscription->refresh();
        $this->assertEquals($oneGB, $this->subscription->storage_used);
    }

    #[Test]
    public function update_storage_usage_can_decrease_usage()
    {
        $twoGB = 2 * 1024 * 1024 * 1024;
        $this->subscription->update(['storage_used' => $twoGB]);

        $oneGB = 1024 * 1024 * 1024;
        $success = $this->service->updateStorageUsage($this->tenant, -$oneGB);

        $this->assertTrue($success);
        $this->subscription->refresh();
        $this->assertEquals($oneGB, $this->subscription->storage_used);
    }

    #[Test]
    public function update_storage_usage_does_not_go_below_zero()
    {
        $success = $this->service->updateStorageUsage($this->tenant, -1000);

        $this->assertTrue($success);
        $this->subscription->refresh();
        $this->assertEquals(0, $this->subscription->storage_used);
    }

    #[Test]
    public function update_hierarchy_depth_updates_when_greater()
    {
        $success = $this->service->updateHierarchyDepth($this->tenant, 3);

        $this->assertTrue($success);
        $this->subscription->refresh();
        $this->assertEquals(3, $this->subscription->hierarchy_depth);
    }

    #[Test]
    public function update_hierarchy_depth_does_not_update_when_smaller()
    {
        $this->subscription->update(['hierarchy_depth' => 3]);

        $success = $this->service->updateHierarchyDepth($this->tenant, 2);

        $this->assertTrue($success);
        $this->subscription->refresh();
        $this->assertEquals(3, $this->subscription->hierarchy_depth);
    }

    #[Test]
    public function get_usage_summary_returns_complete_data()
    {
        $this->subscription->update([
            'user_count' => 3,
            'sub_org_count' => 1,
            'storage_used' => 5 * 1024 * 1024 * 1024, // 5 GB
            'hierarchy_depth' => 2,
        ]);

        $summary = $this->service->getUsageSummary($this->tenant);

        $this->assertTrue($summary['has_subscription']);
        $this->assertEquals('Test Plan', $summary['plan']['name']);
        $this->assertEquals(3, $summary['users']['current']);
        $this->assertEquals(5, $summary['users']['limit']);
        $this->assertEquals(2, $summary['users']['remaining']);
        $this->assertEquals(60, $summary['users']['percentage']);
        $this->assertEquals(1, $summary['sub_organizations']['current']);
        $this->assertEquals(2, $summary['sub_organizations']['limit']);
        $this->assertStringContainsString('GB', $summary['storage']['current_formatted']);
        $this->assertEquals(2, $summary['hierarchy']['current']);
        $this->assertContains('inventory', $summary['modules']);
        $this->assertContains('accounting', $summary['modules']);
    }

    #[Test]
    public function get_usage_summary_handles_no_subscription()
    {
        $noSubOrg = Organization::factory()->create([
            'type' => 'tenant',
            'parent_id' => $this->portalOwner->id,
        ]);

        $summary = $this->service->getUsageSummary($noSubOrg);

        $this->assertFalse($summary['has_subscription']);
        $this->assertArrayHasKey('message', $summary);
    }

    #[Test]
    public function get_usage_summary_handles_unlimited_limits()
    {
        $this->plan->update([
            'user_limit' => 0,
            'sub_org_limit' => 0,
            'storage_limit' => 0,
        ]);

        $summary = $this->service->getUsageSummary($this->tenant);

        $this->assertTrue($summary['users']['unlimited']);
        $this->assertTrue($summary['sub_organizations']['unlimited']);
        $this->assertTrue($summary['storage']['unlimited']);
        $this->assertNull($summary['users']['remaining']);
    }

    #[Test]
    public function cannot_increment_user_count_without_subscription()
    {
        $noSubOrg = Organization::factory()->create([
            'type' => 'tenant',
            'parent_id' => $this->portalOwner->id,
        ]);

        $success = $this->service->incrementUserCount($noSubOrg);

        $this->assertFalse($success);
    }

    #[Test]
    public function cannot_update_storage_without_subscription()
    {
        $noSubOrg = Organization::factory()->create([
            'type' => 'tenant',
            'parent_id' => $this->portalOwner->id,
        ]);

        $success = $this->service->updateStorageUsage($noSubOrg, 1000);

        $this->assertFalse($success);
    }

    #[Test]
    public function can_handle_inactive_subscription()
    {
        $this->subscription->update(['status' => 'cancelled']);

        $result = $this->service->canCreateUser($this->tenant);

        $this->assertFalse($result['allowed']);
        $this->assertEquals('no_active_subscription', $result['reason']);
    }
}
