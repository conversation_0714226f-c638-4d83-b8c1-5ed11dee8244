<?php

namespace App\Modules\Integration\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Integration\Domain\Services\WebhookService;
use App\Modules\Integration\Domain\Models\Webhook;
use Illuminate\Http\Request;

class WebhookController extends Controller
{
    public function __construct(private readonly WebhookService $webhookService)
    {
    }

    /**
     * List all webhooks
     */
    public function index(Request $request)
    {
        try {
            $webhooks = $this->webhookService->listWebhooks([
                'organization_id' => auth()->user()->organization_id,
            ]);

            return response()->json([
                'data' => $webhooks,
                'count' => $webhooks->count(),
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Create new webhook
     */
    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
                'name' => 'nullable|string|max:100',
                'url' => 'required|url',
                'events' => 'required|array',
                'events.*' => 'string',
                'secret' => 'nullable|string',
                'is_active' => 'boolean',
                'retry_count' => 'integer|min:1|max:10',
                'metadata' => 'array',
            ]);

            $validated['organization_id'] = auth()->user()->organization_id;

            $webhook = $this->webhookService->createWebhook($validated);

            return response()->json($webhook, 201);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json(['errors' => $e->errors()], 422);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Get webhook details
     */
    public function show(string $id)
    {
        try {
            $webhook = $this->webhookService->getWebhookById($id);

            if (!$webhook || $webhook->organization_id !== auth()->user()->organization_id) {
                return response()->json(['error' => 'Webhook not found'], 404);
            }

            return response()->json($webhook);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Update webhook
     */
    public function update(Request $request, string $id)
    {
        try {
            $webhook = $this->webhookService->getWebhookById($id);

            if (!$webhook || $webhook->organization_id !== auth()->user()->organization_id) {
                return response()->json(['error' => 'Webhook not found'], 404);
            }

            $validated = $request->validate([
                'url' => 'url',
                'events' => 'array',
                'is_active' => 'boolean',
                'retry_count' => 'integer|min:1|max:10',
                'timeout' => 'integer|min:5|max:60',
            ]);

            $updated = $this->webhookService->updateWebhook($webhook, $validated);

            return response()->json($updated);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json(['errors' => $e->errors()], 422);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Delete webhook
     */
    public function destroy(string $id)
    {
        try {
            $webhook = $this->webhookService->getWebhookById($id);

            if (!$webhook || $webhook->organization_id !== auth()->user()->organization_id) {
                return response()->json(['error' => 'Webhook not found'], 404);
            }

            $this->webhookService->deleteWebhook($webhook);

            return response()->json(['deleted' => true]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Get webhook events
     */
    public function events(string $id, Request $request)
    {
        try {
            $webhook = $this->webhookService->getWebhookById($id);

            if (!$webhook || $webhook->organization_id !== auth()->user()->organization_id) {
                return response()->json(['error' => 'Webhook not found'], 404);
            }

            $filters = [
                'status' => $request->input('status'),
                'event' => $request->input('event'),
            ];

            $events = $this->webhookService->getWebhookEvents($webhook, array_filter($filters));

            return response()->json([
                'data' => $events,
                'count' => $events->count(),
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }
}
