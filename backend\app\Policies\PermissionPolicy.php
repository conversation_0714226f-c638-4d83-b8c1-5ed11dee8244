<?php

namespace App\Policies;

use App\Modules\Users\Domain\Models\User;
use App\Modules\RolesPermissions\Domain\Models\Permission;
use App\Modules\RolesPermissions\Domain\Services\AuthorizationService;

class PermissionPolicy
{
    public function __construct(private readonly AuthorizationService $authz) {}

    public function view(User $user, Permission $permission): bool
    {
        return $user->organization_id === $permission->organization_id;
    }

    public function create(User $user): bool
    {
        return $this->authz->userHasPermission($user, 'permissions.create');
    }

    public function update(User $user, Permission $permission): bool
    {
        return $user->organization_id === $permission->organization_id && $this->authz->userHasPermission($user, 'permissions.update');
    }

    public function delete(User $user, Permission $permission): bool
    {
        return $user->organization_id === $permission->organization_id && $this->authz->userHasPermission($user, 'permissions.delete');
    }
}
