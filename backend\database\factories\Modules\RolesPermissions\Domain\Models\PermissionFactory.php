<?php

namespace Database\Factories\Modules\RolesPermissions\Domain\Models;

use App\Modules\RolesPermissions\Domain\Models\Permission;
use App\Modules\Organizations\Domain\Models\Organization;
use Illuminate\Database\Eloquent\Factories\Factory;

class PermissionFactory extends Factory
{
    protected $model = Permission::class;

    public function definition(): array
    {
        $name = $this->faker->unique()->word() . '.' . $this->faker->word();
        return [
            'id' => $this->faker->uuid(),
            'organization_id' => Organization::factory(),
            'name' => $name,
            'slug' => \Illuminate\Support\Str::slug($name),
            'module' => $this->faker->word(),
            'description' => $this->faker->sentence(),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}
