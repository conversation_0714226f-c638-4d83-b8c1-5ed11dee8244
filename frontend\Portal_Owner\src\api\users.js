import client from './client'

export const usersAPI = {
  // Users CRUD
  getUsers: (params) => client.get('/users', { params }),
  createUser: (data) => client.post('/users', data),
  getUser: (id) => client.get(`/users/${id}`),
  updateUser: (id, data) => client.patch(`/users/${id}`, data),
  deleteUser: (id) => client.delete(`/users/${id}`),
  
  // User actions
  bulkImport: (file) => {
    const formData = new FormData()
    formData.append('file', file)
    return client.post('/users/bulk-import', formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
    })
  },
  getUserActivity: (id, params) => client.get(`/users/${id}/activity`, { params }),
  changeUserStatus: (id, status) => client.patch(`/users/${id}/status`, { status }),
}
