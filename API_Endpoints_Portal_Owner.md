# API Endpoints for Portal Owner

## 1. Authentication
- `POST /v1/auth/register` - Register a new user
- `POST /v1/auth/login` - Authenticate user (rate limited)
- `POST /v1/auth/logout` - Invalidate current session
- `POST /v1/auth/refresh-token` - Refresh authentication token
- `POST /v1/auth/request-otp` - Request One-Time Password (rate limited)
- `POST /v1/auth/verify-otp` - Verify OTP
- `POST /v1/auth/reset-password` - Reset user password
- `POST /v1/auth/setup-mfa` - Set up Multi-Factor Authentication
- `POST /v1/auth/verify-mfa` - Verify MFA code

## 2. User Management
- `GET /v1/me` - Get current user profile
- `PATCH /v1/me` - Update current user profile
- `GET /v1/users` - List all users
- `POST /v1/users` - Create a new user
- `GET /v1/users/{id}` - Get user details
- `PATCH /v1/users/{id}` - Update user
- `DELETE /v1/users/{id}` - Delete user
- `POST /v1/users/bulk-import` - Import multiple users
- `GET /v1/users/{id}/activity` - Get user activity log
- `PATCH /v1/users/{id}/status` - Update user status

## 3. Organizations
- `GET /v1/organizations` - List all organizations
- `POST /v1/organizations` - Create new organization
- `GET /v1/organizations/{id}` - Get organization details
- `PATCH /v1/organizations/{id}` - Update organization
- `DELETE /v1/organizations/{id}` - Delete organization
- `GET /v1/organizations/{id}/hierarchy` - View organization hierarchy
- `GET /v1/organizations/{id}/members` - List organization members

## 4. Roles & Permissions
- `GET /v1/roles` - List all roles
- `POST /v1/roles` - Create new role
- `GET /v1/roles/{id}` - Get role details
- `PATCH /v1/roles/{id}` - Update role
- `DELETE /v1/roles/{id}` - Delete role
- `POST /v1/roles/{id}/permissions/{permissionId}` - Assign permission to role
- `DELETE /v1/roles/{id}/permissions/{permissionId}` - Remove permission from role

- `GET /v1/permissions` - List all permissions
- `POST /v1/permissions` - Create new permission
- `GET /v1/permissions/{id}` - Get permission details
- `PATCH /v1/permissions/{id}` - Update permission
- `DELETE /v1/permissions/{id}` - Delete permission

- `GET /v1/role-assignments` - List all role assignments
- `POST /v1/role-assignments` - Assign role to user
- `GET /v1/role-assignments/{id}` - Get role assignment details
- `PATCH /v1/role-assignments/{id}` - Update role assignment
- `DELETE /v1/role-assignments/{id}` - Remove role assignment

## 5. Billing & Subscriptions
### Subscriptions
- `GET /v1/subscriptions` - List all subscriptions
- `POST /v1/subscriptions` - Create new subscription
- `GET /v1/subscriptions/{id}` - Get subscription details
- `PATCH /v1/subscriptions/{id}` - Update subscription
- `DELETE /v1/subscriptions/{id}` - Cancel subscription
- `POST /v1/subscriptions/{id}/upgrade` - Upgrade subscription
- `POST /v1/subscriptions/{id}/downgrade` - Downgrade subscription
- `POST /v1/subscriptions/{id}/cancel` - Cancel subscription

### Enhanced Subscription Management
- `POST /v1/subscriptions/{subscriptionId}/addons` - Add add-on to subscription
- `DELETE /v1/subscriptions/{subscriptionId}/addons/{addonId}` - Remove add-on
- `POST /v1/subscriptions/{subscriptionId}/upgrade-plan` - Upgrade subscription plan
- `POST /v1/subscriptions/{subscriptionId}/downgrade-plan` - Downgrade subscription plan
- `POST /v1/subscriptions/{subscriptionId}/cancel-subscription` - Cancel subscription
- `POST /v1/subscriptions/{subscriptionId}/calculate-upgrade-price` - Calculate upgrade cost

### Resource Usage
- `GET /v1/organizations/{organizationId}/usage` - Get resource usage
- `GET /v1/organizations/{organizationId}/usage/alerts` - Get usage alerts
- `GET /v1/subscriptions/{subscriptionId}/usage/history` - Get usage history

### Payments
- `GET /v1/payments` - List all payments
- `POST /v1/payments` - Record new payment
- `GET /v1/payments/{id}` - Get payment details
- `POST /v1/payments/{id}/retry` - Retry failed payment
- `POST /v1/payments/{id}/refund` - Process refund
- `POST /v1/payments/webhook` - Payment gateway webhook

### Invoices
- `GET /v1/invoices` - List all invoices
- `POST /v1/invoices` - Create new invoice
- `GET /v1/invoices/{id}` - Get invoice details
- `GET /v1/invoices/{id}/pdf` - Download PDF invoice
- `POST /v1/invoices/{id}/send` - Send invoice to customer

## 6. Approvals
- `GET /v1/approvals` - List all approval requests
- `POST /v1/approvals` - Create new approval request
- `GET /v1/approvals/{id}` - Get approval details
- `POST /v1/approvals/{id}/approve` - Approve request
- `POST /v1/approvals/{id}/reject` - Reject request
- `POST /v1/approvals/{id}/comments` - Add comment to approval

## 7. Notifications
- `GET /v1/notifications` - List all notifications
- `GET /v1/notifications/{id}` - Get notification details
- `PATCH /v1/notifications/{id}/read` - Mark as read

## 8. Reports
- `GET /v1/reports/subscriptions` - Subscription reports
- `GET /v1/reports/payments` - Payment reports
- `GET /v1/reports/revenue` - Revenue reports
- `GET /v1/reports/users` - User activity reports
- `GET /v1/reports/approvals` - Approval workflow reports

## 9. Settings
- `GET /v1/settings` - Get all settings
- `PATCH /v1/settings` - Update multiple settings
- `GET /v1/settings/{key}` - Get specific setting
- `PATCH /v1/settings/{key}` - Update specific setting

## 10. Integration (Webhooks)
- `GET /v1/webhooks` - List all webhooks
- `POST /v1/webhooks` - Create new webhook
- `GET /v1/webhooks/{id}` - Get webhook details
- `PATCH /v1/webhooks/{id}` - Update webhook
- `DELETE /v1/webhooks/{id}` - Delete webhook
- `GET /v1/webhooks/{id}/events` - List webhook events
