import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { CCard, CCardBody, CCardHeader, CCol, CRow, CButton, CTable, CTableBody, CTableDataCell, CTableHead, CTableHeaderCell, CTableRow, CSpinner, CAlert, CBadge } from '@coreui/react'
import CIcon from '@coreui/icons-react'
import { cilPlus, cilPencil, cilTrash } from '@coreui/icons'
import { approvalsAPI } from '../../api/approvals'

const Approvals = () => {
  const navigate = useNavigate()
  const [approvals, setApprovals] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => { loadApprovals() }, [])

  const loadApprovals = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await approvalsAPI.getApprovals()
      const jsonData = await response.json()
      setApprovals(jsonData.data || [])
    } catch (err) {
      setError(err.message || 'Failed to load approvals')
      console.error('Error:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async (id) => {
    if (window.confirm('Are you sure?')) {
      try {
        await approvalsAPI.deleteApproval(id)
        setApprovals(approvals.filter((a) => a.id !== id))
      } catch (err) {
        setError(err.response?.data?.message || 'Failed to delete')
      }
    }
  }

  return (
    <CRow>
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardHeader>
            <div className="d-flex justify-content-between align-items-center">
              <strong>Approvals</strong>
              <CButton color="primary" size="sm" onClick={() => navigate('/approvals/new')}>
                <CIcon icon={cilPlus} className="me-2" />
                New Approval
              </CButton>
            </div>
          </CCardHeader>
          <CCardBody>
            {error && <CAlert color="danger">{error}</CAlert>}
            {loading ? (
              <div className="text-center"><CSpinner color="primary" /></div>
            ) : (
              <CTable hover responsive>
                <CTableHead>
                  <CTableRow>
                    <CTableHeaderCell scope="col">#</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Entity Type</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Status</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Created</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Actions</CTableHeaderCell>
                  </CTableRow>
                </CTableHead>
                <CTableBody>
                  {approvals.length === 0 ? (
                    <CTableRow>
                      <CTableDataCell colSpan="5" className="text-center text-muted">
                        No approval requests found.
                      </CTableDataCell>
                    </CTableRow>
                  ) : (
                    approvals.map((approval, idx) => (
                      <CTableRow key={approval.id}>
                        <CTableDataCell>{idx + 1}</CTableDataCell>
                        <CTableDataCell>{approval.entity_type || '-'}</CTableDataCell>
                        <CTableDataCell><CBadge color={approval.status === 'pending' ? 'warning' : 'success'}>{approval.status}</CBadge></CTableDataCell>
                        <CTableDataCell>{approval.created_at || '-'}</CTableDataCell>
                        <CTableDataCell>
                          <CButton color="info" size="sm" className="me-2" onClick={() => navigate(`/approvals/${approval.id}`)}><CIcon icon={cilPencil} /></CButton>
                          <CButton color="danger" size="sm" onClick={() => handleDelete(approval.id)}><CIcon icon={cilTrash} /></CButton>
                        </CTableDataCell>
                      </CTableRow>
                    ))
                  )}
                </CTableBody>
              </CTable>
            )}
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  )
}

export default Approvals
