<?php

namespace Tests\Feature\Billing;

use Tests\TestCase;
use App\Modules\Billing\Domain\Services\SubscriptionService;
use App\Modules\Billing\Domain\Models\Plan;
use App\Modules\Billing\Domain\Models\Subscription;
use App\Modules\Organizations\Domain\Models\Organization;

class SubscriptionServiceTest extends TestCase
{
    private SubscriptionService $subscriptionService;
    private Organization $organization;
    private Plan $plan;

    protected function setUp(): void
    {
        parent::setUp();
        $this->subscriptionService = app(SubscriptionService::class);
        $this->organization = Organization::factory()->create();
        $this->plan = Plan::factory()->create(['organization_id' => $this->organization->id]);
    }

    public function test_can_create_subscription()
    {
        $data = [
            'organization_id' => $this->organization->id,
            'plan_id' => $this->plan->id,
            'billing_period' => 'monthly',
        ];

        $subscription = $this->subscriptionService->createSubscription($data);

        $this->assertNotNull($subscription);
        $this->assertEquals('active', $subscription->status);
        $this->assertEquals($this->plan->id, $subscription->plan_id);
        $this->assertTrue($subscription->auto_renew);
    }

    public function test_can_upgrade_subscription()
    {
        $subscription = Subscription::factory()->create([
            'organization_id' => $this->organization->id,
            'plan_id' => $this->plan->id,
        ]);

        $newPlan = Plan::factory()->create([
            'organization_id' => $this->organization->id,
            'price' => 99.99,
        ]);

        $upgraded = $this->subscriptionService->upgradeSubscription($subscription, $newPlan);

        $this->assertEquals($newPlan->id, $upgraded->plan_id);
        $this->assertEquals(99.99, $upgraded->price);
    }

    public function test_can_downgrade_subscription()
    {
        $subscription = Subscription::factory()->create([
            'organization_id' => $this->organization->id,
            'plan_id' => $this->plan->id,
            'price' => 99.99,
        ]);

        $lowerPlan = Plan::factory()->create([
            'organization_id' => $this->organization->id,
            'price' => 49.99,
        ]);

        $downgraded = $this->subscriptionService->downgradeSubscription($subscription, $lowerPlan);

        $this->assertEquals($lowerPlan->id, $downgraded->plan_id);
        $this->assertEquals(49.99, $downgraded->price);
    }

    public function test_can_cancel_subscription()
    {
        $subscription = Subscription::factory()->create([
            'organization_id' => $this->organization->id,
            'status' => 'active',
        ]);

        $cancelled = $this->subscriptionService->cancelSubscription($subscription);

        $this->assertEquals('cancelled', $cancelled->status);
        $this->assertFalse($cancelled->auto_renew);
        $this->assertNotNull($cancelled->cancelled_at);
    }

    public function test_can_renew_subscription()
    {
        $subscription = Subscription::factory()->create([
            'organization_id' => $this->organization->id,
            'status' => 'active',
            'auto_renew' => true,
            'ends_at' => now()->subDay(),
        ]);

        $renewed = $this->subscriptionService->renewSubscription($subscription);

        $this->assertTrue($renewed->ends_at->isFuture());
    }

    public function test_can_list_subscriptions()
    {
        Subscription::factory()->count(3)->create(['organization_id' => $this->organization->id]);

        $subscriptions = $this->subscriptionService->listSubscriptions([
            'organization_id' => $this->organization->id,
        ]);

        $this->assertCount(3, $subscriptions);
    }

    public function test_can_check_subscription_status()
    {
        $activeSubscription = Subscription::factory()->create([
            'organization_id' => $this->organization->id,
            'status' => 'active',
            'ends_at' => now()->addMonth(),
        ]);

        $status = $this->subscriptionService->checkSubscriptionStatus($activeSubscription);

        $this->assertEquals('active', $status);
    }

    public function test_can_detect_expired_subscription()
    {
        $expiredSubscription = Subscription::factory()->create([
            'organization_id' => $this->organization->id,
            'status' => 'active',
            'ends_at' => now()->subDay(),
        ]);

        $status = $this->subscriptionService->checkSubscriptionStatus($expiredSubscription);

        $this->assertEquals('expired', $status);
    }
}
