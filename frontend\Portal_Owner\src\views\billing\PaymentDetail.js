import React, { useState, useEffect } from 'react'
import { use<PERSON>arams, useNavigate } from 'react-router-dom'
import { CCard, CCardBody, CCardHeader, CCol, CRow, CForm, CFormLabel, CFormInput, CFormSelect, CButton, CSpinner, CAlert } from '@coreui/react'
import { billingAPI } from '../../api/billing'

const PaymentDetail = () => {
  const { id } = useParams()
  const navigate = useNavigate()
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [formData, setFormData] = useState({ amount: '', payment_method: '', status: 'pending' })
  const [saving, setSaving] = useState(false)

  useEffect(() => {
    if (id && id !== 'new') {
      loadPayment()
    } else {
      setLoading(false)
    }
  }, [id])

  const loadPayment = async () => {
    try {
      setLoading(true)
      const response = await billingAPI.getPayment(id)
      setFormData(response.data.data)
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to load payment')
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    try {
      setSaving(true)
      if (id && id !== 'new') {
        await billingAPI.updatePayment(id, formData)
      } else {
        await billingAPI.createPayment(formData)
      }
      navigate('/payments')
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to save payment')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return <CRow><CCol xs={12} className="text-center"><CSpinner color="primary" /></CCol></CRow>
  }

  return (
    <CRow>
      <CCol xs={12} md={8}>
        <CCard>
          <CCardHeader>
            <strong>{id && id !== 'new' ? 'Edit Payment' : 'Record Payment'}</strong>
          </CCardHeader>
          <CCardBody>
            {error && <CAlert color="danger">{error}</CAlert>}
            <CForm onSubmit={handleSubmit}>
              <div className="mb-3">
                <CFormLabel htmlFor="amount">Amount</CFormLabel>
                <CFormInput id="amount" name="amount" type="number" value={formData.amount} onChange={handleChange} required />
              </div>
              <div className="mb-3">
                <CFormLabel htmlFor="payment_method">Payment Method</CFormLabel>
                <CFormSelect id="payment_method" name="payment_method" value={formData.payment_method} onChange={handleChange}>
                  <option value="">Select Method</option>
                  <option value="credit_card">Credit Card</option>
                  <option value="bank_transfer">Bank Transfer</option>
                  <option value="check">Check</option>
                </CFormSelect>
              </div>
              <div className="mb-3">
                <CFormLabel htmlFor="status">Status</CFormLabel>
                <CFormSelect id="status" name="status" value={formData.status} onChange={handleChange}>
                  <option value="pending">Pending</option>
                  <option value="completed">Completed</option>
                  <option value="failed">Failed</option>
                </CFormSelect>
              </div>
              <div className="d-grid gap-2">
                <CButton type="submit" color="primary" disabled={saving}>
                  {saving ? <CSpinner size="sm" className="me-2" /> : null}
                  {id && id !== 'new' ? 'Update Payment' : 'Record Payment'}
                </CButton>
                <CButton type="button" color="secondary" onClick={() => navigate('/payments')}>
                  Cancel
                </CButton>
              </div>
            </CForm>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  )
}

export default PaymentDetail
