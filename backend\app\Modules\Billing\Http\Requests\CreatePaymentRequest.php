<?php

namespace App\Modules\Billing\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CreatePaymentRequest extends FormRequest
{
    public function authorize(): bool { return true; }
    public function rules(): array
    {
        return [
            'organization_id' => ['required','uuid'],
            'subscription_id' => ['required','uuid'],
            'amount' => ['required','numeric','min:0'],
            'payment_method_id' => ['required','uuid'],
        ];
    }
}
