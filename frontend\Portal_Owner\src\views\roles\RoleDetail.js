import React, { useState, useEffect } from 'react'
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom'
import { CCard, CCardBody, CCardHeader, CCol, CRow, CForm, CFormLabel, CFormInput, CFormTextarea, CButton, CSpinner, CAlert } from '@coreui/react'
import { rolesAPI } from '../../api/roles'

const RoleDetail = () => {
  const { id } = useParams()
  const navigate = useNavigate()
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [formData, setFormData] = useState({ name: '', description: '' })
  const [saving, setSaving] = useState(false)

  useEffect(() => {
    if (id && id !== 'new') {
      loadRole()
    } else {
      setLoading(false)
    }
  }, [id])

  const loadRole = async () => {
    try {
      setLoading(true)
      const response = await rolesAPI.getRole(id)
      setFormData(response.data.data)
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to load role')
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    try {
      setSaving(true)
      if (id && id !== 'new') {
        await rolesAPI.updateRole(id, formData)
      } else {
        await rolesAPI.createRole(formData)
      }
      navigate('/roles')
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to save role')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return <CRow><CCol xs={12} className="text-center"><CSpinner color="primary" /></CCol></CRow>
  }

  return (
    <CRow>
      <CCol xs={12} md={8}>
        <CCard>
          <CCardHeader>
            <strong>{id && id !== 'new' ? 'Edit Role' : 'Create Role'}</strong>
          </CCardHeader>
          <CCardBody>
            {error && <CAlert color="danger">{error}</CAlert>}
            <CForm onSubmit={handleSubmit}>
              <div className="mb-3">
                <CFormLabel htmlFor="name">Role Name</CFormLabel>
                <CFormInput id="name" name="name" value={formData.name} onChange={handleChange} required />
              </div>
              <div className="mb-3">
                <CFormLabel htmlFor="description">Description</CFormLabel>
                <CFormTextarea id="description" name="description" value={formData.description} onChange={handleChange} rows="3" />
              </div>
              <div className="d-grid gap-2">
                <CButton type="submit" color="primary" disabled={saving}>
                  {saving ? <CSpinner size="sm" className="me-2" /> : null}
                  {id && id !== 'new' ? 'Update Role' : 'Create Role'}
                </CButton>
                <CButton type="button" color="secondary" onClick={() => navigate('/roles')}>
                  Cancel
                </CButton>
              </div>
            </CForm>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  )
}

export default RoleDetail
