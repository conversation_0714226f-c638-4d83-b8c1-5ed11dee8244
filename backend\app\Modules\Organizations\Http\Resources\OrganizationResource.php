<?php

namespace App\Modules\Organizations\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class OrganizationResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'code' => $this->code,
            'type' => $this->type,
            'status' => $this->status,
            'timezone' => $this->timezone,
            'currency' => $this->currency,
            'language' => $this->language,
            'created_at' => $this->created_at?->toIso8601String(),
        ];
    }
}
