import client from './client'

export const webhooksAPI = {
  // Webhooks CRUD
  getWebhooks: (params) => client.get('/webhooks', { params }),
  createWebhook: (data) => client.post('/webhooks', data),
  getWebhook: (id) => client.get(`/webhooks/${id}`),
  updateWebhook: (id, data) => client.patch(`/webhooks/${id}`, data),
  deleteWebhook: (id) => client.delete(`/webhooks/${id}`),
  
  // Webhook events
  getWebhookEvents: (id, params) => client.get(`/webhooks/${id}/events`, { params }),
}
