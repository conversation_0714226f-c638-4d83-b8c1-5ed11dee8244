<?php

namespace App\Modules\Billing\Domain\Services;

use App\Modules\Shared\Domain\Services\BaseService;
use App\Modules\Billing\Domain\Models\Subscription;
use App\Modules\Billing\Domain\Models\Plan;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Carbon;

class SubscriptionService extends BaseService
{
    public function createSubscription(array $data): Subscription
    {
        return DB::transaction(function () use ($data) {
            $plan = Plan::findOrFail($data['plan_id']);

            $startsAt = $data['starts_at'] ?? now();
            $billingPeriod = $data['billing_period'] ?? ($plan->billing_period ?? 'monthly');
            $price = $data['price'] ?? ($billingPeriod === 'yearly' ? ($plan->yearly_price ?? $plan->price * 12) : $plan->price);

            $endsAt = $this->calculateEndsAt($startsAt, $billingPeriod);

            return Subscription::create([
                'organization_id' => $data['organization_id'] ?? $this->getCurrentOrganizationId(),
                'plan_id' => $plan->id,
                'status' => 'active',
                'starts_at' => $startsAt,
                'ends_at' => $endsAt,
                'trial_ends_at' => $data['trial_ends_at'] ?? null,
                'auto_renew' => $data['auto_renew'] ?? true,
                'price' => $price,
                'billing_period' => $billingPeriod,
                'user_count' => $data['user_count'] ?? 0,
                'metadata' => $data['metadata'] ?? [],
            ]);
        });
    }

    public function upgradeSubscription(Subscription $subscription, Plan $newPlan): Subscription
    {
        return $this->changePlan($subscription, $newPlan, 'upgrade');
    }

    public function downgradeSubscription(Subscription $subscription, Plan $newPlan): Subscription
    {
        return $this->changePlan($subscription, $newPlan, 'downgrade');
    }

    public function cancelSubscription(Subscription $subscription): Subscription
    {
        $subscription->update([
            'status' => 'cancelled',
            'cancelled_at' => now(),
            'auto_renew' => false,
        ]);
        return $subscription;
    }

    public function renewSubscription(Subscription $subscription): Subscription
    {
        if ($subscription->status !== 'active' || !$subscription->auto_renew) {
            return $subscription;
        }

        $newEndsAt = $this->calculateEndsAt($subscription->ends_at ?? now(), $subscription->billing_period);
        $subscription->update([
            'ends_at' => $newEndsAt,
        ]);
        return $subscription;
    }

    public function getSubscriptionById(string $id): ?Subscription
    {
        return Subscription::find($id);
    }

    public function listSubscriptions(array $filters = []): Collection
    {
        $q = Subscription::query()->where('organization_id', $filters['organization_id'] ?? $this->getCurrentOrganizationId());
        if (isset($filters['status'])) {
            $q->whereIn('status', (array) $filters['status']);
        }
        return $q->get();
    }

    public function checkSubscriptionStatus(Subscription $subscription): string
    {
        if ($subscription->status === 'cancelled') return 'cancelled';
        if ($subscription->ends_at && $subscription->ends_at->isPast()) return 'expired';
        return 'active';
    }

    private function changePlan(Subscription $subscription, Plan $newPlan, string $type): Subscription
    {
        return DB::transaction(function () use ($subscription, $newPlan, $type) {
            $price = $subscription->billing_period === 'yearly' ? ($newPlan->yearly_price ?? $newPlan->price * 12) : $newPlan->price;
            $subscription->update([
                'plan_id' => $newPlan->id,
                'price' => $price,
            ]);
            return $subscription->fresh();
        });
    }

    private function calculateEndsAt($startsAt, string $billingPeriod)
    {
        return match ($billingPeriod) {
            'yearly' => \Carbon\Carbon::parse($startsAt)->addYear(),
            default => \Carbon\Carbon::parse($startsAt)->addMonth(),
        };
    }
}
