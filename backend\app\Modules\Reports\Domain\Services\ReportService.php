<?php

namespace App\Modules\Reports\Domain\Services;

use App\Modules\Shared\Domain\Services\BaseService;
use App\Modules\Billing\Domain\Models\Subscription;
use App\Modules\Billing\Domain\Models\Payment;
use App\Modules\Billing\Domain\Models\Invoice;
use App\Modules\Users\Domain\Models\User;
use App\Modules\Approvals\Domain\Models\ApprovalRequest;
use Illuminate\Support\Facades\DB;

class ReportService extends BaseService
{
    /**
     * Get subscription report
     */
    public function getSubscriptionReport(array $filters = []): array
    {
        // Portal Owner gets system-wide reports
        if ($this->isPortalOwner()) {
            $orgId = $filters['organization_id'] ?? null; // null = all orgs
        } else {
            $orgId = $filters['organization_id'] ?? $this->getCurrentOrganizationId();
        }
        $startDate = $filters['start_date'] ?? now()->subMonth();
        $endDate = $filters['end_date'] ?? now();

        $query = Subscription::query();
        if ($orgId) {
            $query->where('organization_id', $orgId);
        }
        $subscriptions = $query->whereBetween('created_at', [$startDate, $endDate])
            ->get();

        return [
            'total_subscriptions' => $subscriptions->count(),
            'active_subscriptions' => $subscriptions->where('status', 'active')->count(),
            'cancelled_subscriptions' => $subscriptions->where('status', 'cancelled')->count(),
            'total_revenue' => $subscriptions->sum('price'),
            'average_subscription_value' => $subscriptions->avg('price'),
            'by_plan' => $subscriptions->groupBy('plan_id')->map(function ($group) {
                return [
                    'count' => $group->count(),
                    'total_revenue' => $group->sum('price'),
                ];
            }),
        ];
    }

    /**
     * Get payment report
     */
    public function getPaymentReport(array $filters = []): array
    {
        // Portal Owner gets system-wide reports
        if ($this->isPortalOwner()) {
            $orgId = $filters['organization_id'] ?? null; // null = all orgs
        } else {
            $orgId = $filters['organization_id'] ?? $this->getCurrentOrganizationId();
        }
        $startDate = $filters['start_date'] ?? now()->subMonth();
        $endDate = $filters['end_date'] ?? now();

        $query = Payment::query();
        if ($orgId) {
            $query->where('organization_id', $orgId);
        }
        $payments = $query->whereBetween('created_at', [$startDate, $endDate])
            ->get();

        return [
            'total_payments' => $payments->count(),
            'completed_payments' => $payments->where('status', 'completed')->count(),
            'failed_payments' => $payments->where('status', 'failed')->count(),
            'refunded_payments' => $payments->where('status', 'refunded')->count(),
            'total_amount' => $payments->sum('amount'),
            'completed_amount' => $payments->where('status', 'completed')->sum('amount'),
            'refunded_amount' => $payments->where('status', 'refunded')->sum('amount'),
            'by_provider' => $payments->groupBy('provider')->map(function ($group) {
                return [
                    'count' => $group->count(),
                    'total_amount' => $group->sum('amount'),
                ];
            }),
        ];
    }

    /**
     * Get revenue report
     */
    public function getRevenueReport(array $filters = []): array
    {
        // Portal Owner gets system-wide reports
        if ($this->isPortalOwner()) {
            $orgId = $filters['organization_id'] ?? null; // null = all orgs
        } else {
            $orgId = $filters['organization_id'] ?? $this->getCurrentOrganizationId();
        }
        $startDate = $filters['start_date'] ?? now()->subMonth();
        $endDate = $filters['end_date'] ?? now();

        $paymentQuery = Payment::query();
        if ($orgId) {
            $paymentQuery->where('organization_id', $orgId);
        }
        $payments = $paymentQuery->where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->get();

        $invoiceQuery = Invoice::query();
        if ($orgId) {
            $invoiceQuery->where('organization_id', $orgId);
        }
        $invoices = $invoiceQuery->where('status', 'paid')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->get();

        $monthlyRevenue = [];
        for ($i = 0; $i < 12; $i++) {
            $date = now()->subMonths($i);
            $month = $date->format('Y-m');
            $monthlyRevenue[$month] = $payments->filter(function ($p) use ($date) {
                return $p->created_at->format('Y-m') === $date->format('Y-m');
            })->sum('amount');
        }

        return [
            'total_revenue' => $payments->sum('amount'),
            'total_invoiced' => $invoices->sum('total'),
            'average_transaction' => $payments->avg('amount'),
            'monthly_revenue' => array_reverse($monthlyRevenue),
            'currency' => config('app.currency', 'USD'),
        ];
    }

    /**
     * Get user activity report
     */
    public function getUserActivityReport(array $filters = []): array
    {
        // Portal Owner gets system-wide reports
        if ($this->isPortalOwner()) {
            $orgId = $filters['organization_id'] ?? null; // null = all orgs
        } else {
            $orgId = $filters['organization_id'] ?? $this->getCurrentOrganizationId();
        }
        $startDate = $filters['start_date'] ?? now()->subMonth();
        $endDate = $filters['end_date'] ?? now();

        $userQuery = User::query();
        if ($orgId) {
            $userQuery->where('organization_id', $orgId);
        }
        $users = $userQuery->whereBetween('created_at', [$startDate, $endDate])
            ->get();

        $activeUserQuery = User::query();
        if ($orgId) {
            $activeUserQuery->where('organization_id', $orgId);
        }
        $activeUsers = $activeUserQuery->where('last_login_at', '>=', now()->subDays(30))
            ->count();

        return [
            'total_users' => $users->count(),
            'active_users' => $activeUsers,
            'inactive_users' => $users->count() - $activeUsers,
            'new_users_this_month' => $users->where('created_at', '>=', now()->startOfMonth())->count(),
            'by_status' => $users->groupBy('status')->map(fn($group) => $group->count()),
        ];
    }

    /**
     * Get approval report
     */
    public function getApprovalReport(array $filters = []): array
    {
        // Portal Owner gets system-wide reports
        if ($this->isPortalOwner()) {
            $orgId = $filters['organization_id'] ?? null; // null = all orgs
        } else {
            $orgId = $filters['organization_id'] ?? $this->getCurrentOrganizationId();
        }
        $startDate = $filters['start_date'] ?? now()->subMonth();
        $endDate = $filters['end_date'] ?? now();

        $approvalQuery = ApprovalRequest::query();
        if ($orgId) {
            $approvalQuery->where('organization_id', $orgId);
        }
        $approvals = $approvalQuery->whereBetween('created_at', [$startDate, $endDate])
            ->get();

        $avgApprovalQuery = \App\Modules\Approvals\Domain\Models\ApprovalRequest::query();
        if ($orgId) {
            $avgApprovalQuery->where('organization_id', $orgId);
        }
        $avgApprovalTime = $avgApprovalQuery->whereNotNull('completed_at')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->get()
            ->map(fn($row) => $row->created_at?->diffInHours($row->completed_at) ?? 0)
            ->avg();

        return [
            'total_approvals' => $approvals->count(),
            'approved' => $approvals->where('status', 'approved')->count(),
            'rejected' => $approvals->where('status', 'rejected')->count(),
            'pending' => $approvals->where('status', 'pending')->count(),
            'escalated' => $approvals->where('status', 'escalated')->count(),
            'average_approval_time_hours' => round($avgApprovalTime ?? 0, 2),
            'by_type' => $approvals->groupBy('type')->map(fn($group) => $group->count()),
        ];
    }

    /**
     * Generate custom report
     */
    public function generateCustomReport(string $type, array $filters = []): array
    {
        return match ($type) {
            'subscriptions' => $this->getSubscriptionReport($filters),
            'payments' => $this->getPaymentReport($filters),
            'revenue' => $this->getRevenueReport($filters),
            'users' => $this->getUserActivityReport($filters),
            'approvals' => $this->getApprovalReport($filters),
            default => throw new \Exception("Unknown report type: {$type}"),
        };
    }
}
