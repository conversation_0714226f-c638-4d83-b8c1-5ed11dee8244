<?php

namespace Database\Factories\Modules\Organizations\Domain\Models;

use App\Modules\Organizations\Domain\Models\Organization;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class OrganizationFactory extends Factory
{
    protected $model = Organization::class;

    public function definition(): array
    {
        $name = $this->faker->company();
        return [
            'name' => $name,
            'code' => strtoupper(Str::random(8)),
            'type' => 'organization',
            'email' => $this->faker->companyEmail(),
            'phone' => $this->faker->e164PhoneNumber(),
            'address' => $this->faker->streetAddress(),
            'city' => $this->faker->city(),
            'state' => $this->faker->state(),
            'country' => $this->faker->country(),
            'postal_code' => $this->faker->postcode(),
            'timezone' => 'UTC',
            'currency' => 'USD',
            'language' => 'en',
            'status' => 'active',
            'settings' => [],
        ];
    }
}
