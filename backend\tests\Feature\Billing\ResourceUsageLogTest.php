<?php

namespace Tests\Feature\Billing;

use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Modules\Organizations\Domain\Models\Organization;
use App\Modules\Users\Domain\Models\User;
use App\Modules\Billing\Domain\Models\Plan;
use App\Modules\Billing\Domain\Models\Subscription;
use App\Modules\Billing\Domain\Models\ResourceUsageLog;

class ResourceUsageLogTest extends TestCase
{
    use RefreshDatabase;

    protected Organization $tenant;
    protected User $user;
    protected Subscription $subscription;

    protected function setUp(): void
    {
        parent::setUp();

        $portalOwner = Organization::factory()->create([
            'type' => 'portal_owner',
            'parent_id' => null,
        ]);

        $this->tenant = Organization::factory()->create([
            'type' => 'tenant',
            'parent_id' => $portalOwner->id,
        ]);

        $this->user = User::factory()->create([
            'organization_id' => $this->tenant->id,
        ]);

        $plan = Plan::factory()->create([
            'user_limit' => 10,
            'storage_limit' => 100,
            'api_calls_limit' => 5000,
        ]);

        $this->subscription = Subscription::factory()->create([
            'organization_id' => $this->tenant->id,
            'plan_id' => $plan->id,
            'status' => 'active',
        ]);
    }
    #[Test]
    public function resource_usage_log_can_be_created()
    {
        $log = ResourceUsageLog::create([
            'subscription_id' => $this->subscription->id,
            'tenant_organization_id' => $this->tenant->id,
            'resource_type' => 'users',
            'usage_value' => 5,
            'limit_value' => 10,
            'usage_percentage' => 50.00,
            'recorded_at' => now(),
        ]);

        $this->assertDatabaseHas('resource_usage_logs', [
            'subscription_id' => $this->subscription->id,
            'resource_type' => 'users',
            'usage_value' => 5,
        ]);
    }
    #[Test]
    public function resource_usage_log_can_calculate_usage_percentage()
    {
        $log = ResourceUsageLog::create([
            'subscription_id' => $this->subscription->id,
            'tenant_organization_id' => $this->tenant->id,
            'resource_type' => 'users',
            'usage_value' => 7,
            'limit_value' => 10,
            'usage_percentage' => 70.00,
            'recorded_at' => now(),
        ]);

        $this->assertEquals(70, $log->getUsagePercentage());
    }
    #[Test]
    public function resource_usage_log_returns_null_for_unlimited_limit()
    {
        $log = ResourceUsageLog::create([
            'subscription_id' => $this->subscription->id,
            'tenant_organization_id' => $this->tenant->id,
            'resource_type' => 'users',
            'usage_value' => 100,
            'limit_value' => 0, // Unlimited
            'usage_percentage' => 0,
            'recorded_at' => now(),
        ]);

        $this->assertNull($log->getUsagePercentage());
    }
    #[Test]
    public function resource_usage_log_can_detect_if_limit_exceeded()
    {
        $exceeded = ResourceUsageLog::create([
            'subscription_id' => $this->subscription->id,
            'tenant_organization_id' => $this->tenant->id,
            'resource_type' => 'users',
            'usage_value' => 12,
            'limit_value' => 10,
            'usage_percentage' => 120.00,
            'recorded_at' => now(),
        ]);

        $this->assertTrue($exceeded->isLimitExceeded());

        $withinLimit = ResourceUsageLog::create([
            'subscription_id' => $this->subscription->id,
            'tenant_organization_id' => $this->tenant->id,
            'resource_type' => 'storage',
            'usage_value' => 8,
            'limit_value' => 10,
            'usage_percentage' => 80.00,
            'recorded_at' => now(),
        ]);

        $this->assertFalse($withinLimit->isLimitExceeded());
    }
    #[Test]
    public function resource_usage_log_belongs_to_subscription()
    {
        $log = ResourceUsageLog::create([
            'subscription_id' => $this->subscription->id,
            'tenant_organization_id' => $this->tenant->id,
            'resource_type' => 'api_calls',
            'usage_value' => 2000,
            'limit_value' => 5000,
            'usage_percentage' => 40.00,
            'recorded_at' => now(),
        ]);

        $this->assertInstanceOf(Subscription::class, $log->subscription);
        $this->assertEquals($this->subscription->id, $log->subscription->id);
    }
    #[Test]
    public function subscription_has_many_resource_usage_logs()
    {
        ResourceUsageLog::create([
            'subscription_id' => $this->subscription->id,
            'tenant_organization_id' => $this->tenant->id,
            'resource_type' => 'users',
            'usage_value' => 5,
            'limit_value' => 10,
            'usage_percentage' => 50.00,
            'recorded_at' => now(),
        ]);

        ResourceUsageLog::create([
            'subscription_id' => $this->subscription->id,
            'tenant_organization_id' => $this->tenant->id,
            'resource_type' => 'storage',
            'usage_value' => 50,
            'limit_value' => 100,
            'usage_percentage' => 50.00,
            'recorded_at' => now(),
        ]);

        $this->assertCount(2, $this->subscription->resourceUsageLogs);
    }
    #[Test]
    public function resource_usage_log_can_be_filtered_by_resource_type()
    {
        ResourceUsageLog::create([
            'subscription_id' => $this->subscription->id,
            'tenant_organization_id' => $this->tenant->id,
            'resource_type' => 'users',
            'usage_value' => 5,
            'limit_value' => 10,
            'usage_percentage' => 50.00,
            'recorded_at' => now(),
        ]);

        ResourceUsageLog::create([
            'subscription_id' => $this->subscription->id,
            'tenant_organization_id' => $this->tenant->id,
            'resource_type' => 'storage',
            'usage_value' => 50,
            'limit_value' => 100,
            'usage_percentage' => 50.00,
            'recorded_at' => now(),
        ]);

        ResourceUsageLog::create([
            'subscription_id' => $this->subscription->id,
            'tenant_organization_id' => $this->tenant->id,
            'resource_type' => 'users',
            'usage_value' => 7,
            'limit_value' => 10,
            'usage_percentage' => 70.00,
            'recorded_at' => now()->addHour(),
        ]);

        $userLogs = ResourceUsageLog::where('subscription_id', $this->subscription->id)
            ->where('resource_type', 'users')
            ->get();

        $this->assertCount(2, $userLogs);
    }
    #[Test]
    public function resource_usage_log_can_track_changes_over_time()
    {
        $log1 = ResourceUsageLog::create([
            'subscription_id' => $this->subscription->id,
            'tenant_organization_id' => $this->tenant->id,
            'resource_type' => 'users',
            'usage_value' => 5,
            'limit_value' => 10,
            'usage_percentage' => 50.00,
            'recorded_at' => now()->subHours(2),
        ]);

        $log2 = ResourceUsageLog::create([
            'subscription_id' => $this->subscription->id,
            'tenant_organization_id' => $this->tenant->id,
            'resource_type' => 'users',
            'usage_value' => 7,
            'limit_value' => 10,
            'usage_percentage' => 70.00,
            'recorded_at' => now()->subHour(),
        ]);

        $log3 = ResourceUsageLog::create([
            'subscription_id' => $this->subscription->id,
            'tenant_organization_id' => $this->tenant->id,
            'resource_type' => 'users',
            'usage_value' => 9,
            'limit_value' => 10,
            'usage_percentage' => 90.00,
            'recorded_at' => now(),
        ]);

        $logs = ResourceUsageLog::where('subscription_id', $this->subscription->id)
            ->where('resource_type', 'users')
            ->orderBy('recorded_at', 'asc')
            ->get();

        $this->assertEquals(5, $logs[0]->usage_value);
        $this->assertEquals(7, $logs[1]->usage_value);
        $this->assertEquals(9, $logs[2]->usage_value);
    }
    #[Test]
    public function resource_usage_log_can_get_latest_usage_for_resource_type()
    {
        ResourceUsageLog::create([
            'subscription_id' => $this->subscription->id,
            'tenant_organization_id' => $this->tenant->id,
            'resource_type' => 'storage',
            'usage_value' => 40,
            'limit_value' => 100,
            'usage_percentage' => 40.00,
            'recorded_at' => now()->subHours(2),
        ]);

        ResourceUsageLog::create([
            'subscription_id' => $this->subscription->id,
            'tenant_organization_id' => $this->tenant->id,
            'resource_type' => 'storage',
            'usage_value' => 60,
            'limit_value' => 100,
            'usage_percentage' => 60.00,
            'recorded_at' => now()->subHour(),
        ]);

        $latest = ResourceUsageLog::create([
            'subscription_id' => $this->subscription->id,
            'tenant_organization_id' => $this->tenant->id,
            'resource_type' => 'storage',
            'usage_value' => 75,
            'limit_value' => 100,
            'usage_percentage' => 75.00,
            'recorded_at' => now(),
        ]);

        $latestLog = ResourceUsageLog::where('subscription_id', $this->subscription->id)
            ->where('resource_type', 'storage')
            ->orderBy('recorded_at', 'desc')
            ->first();

        $this->assertEquals($latest->id, $latestLog->id);
        $this->assertEquals(75, $latestLog->usage_value);
    }
}
