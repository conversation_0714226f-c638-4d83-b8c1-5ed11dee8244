<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;
use App\Jobs\LogResourceUsageJob;
use App\Jobs\CleanupOldUsageLogsJob;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

// Schedule daily resource usage logging
Schedule::job(new LogResourceUsageJob())
    ->dailyAt('00:00')
    ->name('log-resource-usage')
    ->withoutOverlapping()
    ->onOneServer();

// Schedule weekly cleanup of old usage logs (keep 90 days)
Schedule::job(new CleanupOldUsageLogsJob(90))
    ->weekly()
    ->sundays()
    ->at('02:00')
    ->name('cleanup-old-usage-logs')
    ->withoutOverlapping()
    ->onOneServer();
