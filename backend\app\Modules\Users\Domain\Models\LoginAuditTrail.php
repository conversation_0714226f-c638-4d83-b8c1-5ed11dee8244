<?php

namespace App\Modules\Users\Domain\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Modules\Shared\Domain\Traits\HasUUID;
use App\Modules\Shared\Domain\Traits\HasOrganizationId;

class LoginAuditTrail extends Model
{
    use HasFactory, HasUUID, HasOrganizationId;

    protected $table = 'login_audit_trails';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'organization_id',
        'user_id',
        'event',
        'ip_address',
        'user_agent',
        'location',
        'metadata',
        'is_successful',
        'failure_reason',
    ];

    protected $casts = [
        'metadata' => 'array',
        'is_successful' => 'boolean',
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
