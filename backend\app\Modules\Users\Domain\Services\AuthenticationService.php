<?php

namespace App\Modules\Users\Domain\Services;

use App\Modules\Shared\Domain\Services\BaseService;
use App\Modules\Users\Domain\Models\User;
use App\Modules\Users\Domain\Models\OtpToken;
use App\Modules\Users\Domain\Models\PasswordResetToken;
use App\Modules\Users\Domain\Models\UserSession;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;

class AuthenticationService extends BaseService
{
    public function register(array $data): User
    {
        return DB::transaction(function () use ($data) {
            $data['password'] = Hash::make($data['password']);
            $data['organization_id'] = $data['organization_id'] ?? $this->getCurrentOrganizationId();
            return User::create($data);
        });
    }

    public function login(string $email, string $password): array
    {
        $user = User::where('email', $email)->first();
        if (!$user || !Hash::check($password, $user->password)) {
            throw ValidationException::withMessages(['email' => ['Invalid credentials.']]);
        }

        $session = UserSession::create([
            'organization_id' => $user->organization_id,
            'user_id' => $user->id,
            'token' => bin2hex(random_bytes(32)),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'last_activity' => now(),
            'expires_at' => now()->addSeconds(config('modules.Users.settings.session_timeout', 3600)),
            'is_active' => true,
        ]);

        return [
            'user' => $user,
            'token' => $session->token,
            'expires_at' => $session->expires_at,
        ];
    }

    public function logout(User $user, string $token): void
    {
        UserSession::where('user_id', $user->id)->where('token', $token)->update(['is_active' => false]);
    }

    public function refreshToken(User $user, string $oldToken): array
    {
        $session = UserSession::where('user_id', $user->id)->where('token', $oldToken)->firstOrFail();
        $session->update([
            'token' => bin2hex(random_bytes(32)),
            'last_activity' => now(),
            'expires_at' => now()->addSeconds(config('modules.Users.settings.session_timeout', 3600)),
            'is_active' => true,
        ]);

        return [
            'token' => $session->token,
            'expires_at' => $session->expires_at,
        ];
    }

    public function requestOtp(User $user, string $purpose = 'login'): OtpToken
    {
        return OtpToken::create([
            'organization_id' => $user->organization_id,
            'user_id' => $user->id,
            'token' => (string) random_int(100000, 999999),
            'type' => 'email',
            'purpose' => $purpose,
            'is_used' => false,
            'expires_at' => now()->addSeconds(config('modules.Users.settings.otp_expiry', 300)),
        ]);
    }

    public function verifyOtp(User $user, string $code, string $purpose = 'login'): bool
    {
        $otp = OtpToken::where('user_id', $user->id)
            ->where('purpose', $purpose)
            ->where('is_used', false)
            ->where('expires_at', '>', now())
            ->latest()
            ->first();

        if (!$otp || $otp->token !== $code) {
            return false;
        }

        $otp->update(['is_used' => true, 'used_at' => now()]);
        return true;
    }

    public function resetPasswordRequest(User $user): PasswordResetToken
    {
        return PasswordResetToken::create([
            'organization_id' => $user->organization_id,
            'user_id' => $user->id,
            'email' => $user->email,
            'token' => bin2hex(random_bytes(32)),
            'is_used' => false,
            'expires_at' => now()->addHour(),
        ]);
    }

    public function resetPassword(string $token, string $newPassword): bool
    {
        $prt = PasswordResetToken::where('token', $token)
            ->where('is_used', false)
            ->where('expires_at', '>', now())
            ->first();

        if (!$prt) {
            return false;
        }

        $user = User::findOrFail($prt->user_id);
        $user->update(['password' => Hash::make($newPassword)]);
        $prt->update(['is_used' => true, 'used_at' => now()]);

        return true;
    }

    public function setupMFA(User $user, string $secret): bool
    {
        return $user->update([
            'mfa_enabled' => true,
            'mfa_secret' => $secret,
        ]);
    }

    public function verifyMFA(User $user, string $code): bool
    {
        // Hook TOTP verification here if integrated
        return !empty($user->mfa_secret) && $code !== '';
    }
}
