<?php

use Illuminate\Support\Facades\Route;

Route::middleware('auth')->group(function () {
    // Settings
    Route::get('settings', [\App\Modules\Settings\Http\Controllers\SettingsController::class, 'index']);
    Route::patch('settings', [\App\Modules\Settings\Http\Controllers\SettingsController::class, 'update']);
    Route::get('settings/{key}', [\App\Modules\Settings\Http\Controllers\SettingsController::class, 'showKey']);
    Route::patch('settings/{key}', [\App\Modules\Settings\Http\Controllers\SettingsController::class, 'updateKey']);
});
