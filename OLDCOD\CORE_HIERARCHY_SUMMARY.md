# Core Hierarchy Structure - Executive Summary

## ✅ VERIFICATION COMPLETE

**Status**: Confirmed through complete codebase analysis  
**Date**: October 29, 2025  
**Finding**: Portal, Organization, and Sub-Organization are the **ONLY MANDATORY LEVELS**

---

## 📊 Three-Level Core Hierarchy

### Level 1: PORTAL (Mandatory)
- **Scope**: Global system administration
- **Database**: `organizations` table with `parent_id = NULL` and `type = 'portal'`
- **Enforcement**: System-wide (no organization_id filtering)
- **Roles**: System Admin, System Auditor, System Support
- **Users**: 3 (admin, auditor, support)
- **Quantity**: 1 per system

### Level 2: ORGANIZATION (Mandatory)
- **Scope**: `organization_id` (NOT NULL in users table)
- **Database**: `organizations` table with `parent_id = NULL` and `type = 'organization'`
- **Enforcement**: Every user MUST have `organization_id` (database constraint)
- **Roles**: Org <PERSON>, Org Manager, Org Member
- **Users**: 7+ (all organization members)
- **Quantity**: 1 per tenant/company

### Level 3: SUB-ORGANIZATION (Optional)
- **Scope**: `organization_id` (inherited from parent)
- **Database**: `organizations` table with `parent_id = UUID` and `type = 'sub-organization'`
- **Enforcement**: Optional nesting via `parent_id` (NULLABLE)
- **Roles**: Sub-Org Admin, Sub-Org Manager, Sub-Org Member
- **Users**: 9+ (sub-org members)
- **Quantity**: Multiple allowed, can nest infinitely

---

## 🔍 Evidence from Codebase

### 1. Users Table (Mandatory Organization)
```sql
CREATE TABLE users (
    organization_id UUID NOT NULL,  -- ← MANDATORY
    ...
    FOREIGN KEY (organization_id) REFERENCES organizations(id)
);
```
**Interpretation**: Every user MUST belong to an organization.

### 2. Organizations Table (Optional Sub-Org)
```sql
CREATE TABLE organizations (
    parent_id UUID NULLABLE,        -- ← OPTIONAL
    ...
    FOREIGN KEY (parent_id) REFERENCES organizations(id)
);
```
**Interpretation**: Sub-organizations are optional (parent_id can be NULL).

### 3. Role Assignments (Optional Scoping)
```sql
CREATE TABLE role_assignments (
    scope VARCHAR(50) DEFAULT 'organization',
    scope_id UUID NULLABLE,         -- ← OPTIONAL
    ...
);
```
**Interpretation**: Department, Team, User scopes are optional.

---

## 📋 Complete Structure

```
PORTAL (Mandatory)
  └─ ORGANIZATION (Mandatory)
      ├─ SUB-ORGANIZATION 1 (Optional)
      ├─ SUB-ORGANIZATION 2 (Optional)
      └─ SUB-ORGANIZATION 3 (Optional)
          ├─ SUB-SUB-ORGANIZATION 1.1 (Optional)
          └─ SUB-SUB-ORGANIZATION 1.2 (Optional)
```

---

## 👥 User Distribution

| Level | Name | Users | Roles |
|-------|------|-------|-------|
| 1 | Portal | 3 | System Admin, Auditor, Support |
| 2 | Organization | 7 | Org Admin, Manager, Member |
| 3 | Sub-Org 1 | 3 | Sub-Org Admin, Manager, Member |
| 3 | Sub-Org 2 | 3 | Sub-Org Admin, Manager, Member |
| 3 | Sub-Org 3 | 3 | Sub-Org Admin, Manager, Member |

**Total Users**: 19 (some users have multiple roles across levels)

---

## 🔐 Authorization Rules

### Rule 1: Organization is Mandatory
```php
// ✅ VALID
User::create(['organization_id' => 'org-001', ...]);

// ❌ INVALID - Database error
User::create([...]);  // organization_id missing
```

### Rule 2: Sub-Organization is Optional
```php
// ✅ VALID - Root organization
Organization::create(['parent_id' => null, ...]);

// ✅ VALID - Sub-organization
Organization::create(['parent_id' => 'org-001', ...]);
```

### Rule 3: Scope is Optional
```php
// ✅ VALID - Organization-level role
RoleAssignment::create(['scope' => 'organization', 'scope_id' => null, ...]);

// ✅ VALID - Sub-org level role
RoleAssignment::create(['scope' => 'organization', 'scope_id' => 'org-002', ...]);

// ✅ VALID - Department level role (optional)
RoleAssignment::create(['scope' => 'department', 'scope_id' => 'dept-001', ...]);
```

---

## 📊 Database Constraints Summary

| Constraint | Type | Meaning |
|-----------|------|---------|
| `users.organization_id NOT NULL` | Mandatory | Every user must belong to organization |
| `organizations.parent_id NULLABLE` | Optional | Sub-org nesting is optional |
| `role_assignments.scope_id NULLABLE` | Optional | Department/Team/User scopes are optional |

---

## 🎯 Key Takeaways

1. **Portal Level**: System-wide administration (1 per system)
2. **Organization Level**: Tenant/company (1 per tenant, mandatory for all users)
3. **Sub-Organization Level**: Optional nested organizations (multiple allowed)
4. **All Other Levels**: Optional (Department, Team, User via scoping)

---

## 📄 Related Documents

1. **CORE_HIERARCHY_STRUCTURE.md** - Detailed analysis with code examples
2. **CORE_HIERARCHY_VISUAL.txt** - ASCII diagrams and visualizations
3. **HIERARCHY_LEVELS_DIAGRAM.md** - Complete 6-level hierarchy (for reference)
4. **HIERARCHY_DATA_MODEL.md** - Database schema details

---

**Status**: ✅ VERIFIED AND CONFIRMED
