# Portal Owner Frontend - Component Templates

## Template 1: List View with API Integration

Use this template for: Organizations, Roles, Subscriptions, Payments, Invoices, Approvals, Notifications, Webhooks

```javascript
import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import {
  CCard, CCardBody, CCardHeader, CCol, CRow, CButton, CTable,
  CTableBody, CTableDataCell, CTableHead, CTableHeaderCell, CTableRow,
  CSpinner, CAlert, CBadge
} from '@coreui/react'
import CIcon from '@coreui/icons-react'
import { cilPlus, cilPencil, cilTrash } from '@coreui/icons'
import { [MODULE]API } from '../../api/[module]'

const [ModuleName] = () => {
  const navigate = useNavigate()
  const [items, setItems] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    loadItems()
  }, [])

  const loadItems = async () => {
    try {
      setLoading(true)
      const response = await [MODULE]API.get[ModuleName]()
      setItems(response.data.data || [])
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to load items')
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async (id) => {
    if (window.confirm('Are you sure?')) {
      try {
        await [MODULE]API.delete[ModuleName](id)
        setItems(items.filter((i) => i.id !== id))
      } catch (err) {
        setError(err.response?.data?.message || 'Failed to delete')
      }
    }
  }

  return (
    <CRow>
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardHeader>
            <div className="d-flex justify-content-between align-items-center">
              <strong>[Module Name]</strong>
              <CButton color="primary" size="sm" onClick={() => navigate('/[path]/new')}>
                <CIcon icon={cilPlus} className="me-2" />
                Add [Item]
              </CButton>
            </div>
          </CCardHeader>
          <CCardBody>
            {error && <CAlert color="danger">{error}</CAlert>}
            {loading ? (
              <div className="text-center">
                <CSpinner color="primary" />
              </div>
            ) : (
              <CTable hover responsive>
                <CTableHead>
                  <CTableRow>
                    <CTableHeaderCell scope="col">#</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Name</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Status</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Actions</CTableHeaderCell>
                  </CTableRow>
                </CTableHead>
                <CTableBody>
                  {items.length === 0 ? (
                    <CTableRow>
                      <CTableDataCell colSpan="4" className="text-center text-muted">
                        No items found
                      </CTableDataCell>
                    </CTableRow>
                  ) : (
                    items.map((item, idx) => (
                      <CTableRow key={item.id}>
                        <CTableDataCell>{idx + 1}</CTableDataCell>
                        <CTableDataCell>{item.name}</CTableDataCell>
                        <CTableDataCell>
                          <CBadge color={item.status === 'active' ? 'success' : 'secondary'}>
                            {item.status}
                          </CBadge>
                        </CTableDataCell>
                        <CTableDataCell>
                          <CButton
                            color="info"
                            size="sm"
                            className="me-2"
                            onClick={() => navigate(`/[path]/${item.id}`)}
                          >
                            <CIcon icon={cilPencil} />
                          </CButton>
                          <CButton
                            color="danger"
                            size="sm"
                            onClick={() => handleDelete(item.id)}
                          >
                            <CIcon icon={cilTrash} />
                          </CButton>
                        </CTableDataCell>
                      </CTableRow>
                    ))
                  )}
                </CTableBody>
              </CTable>
            )}
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  )
}

export default [ModuleName]
```

---

## Template 2: Detail Page with Form

Use this template for: Roles, Subscriptions, Payments, Invoices, Approvals, Notifications, Webhooks

```javascript
import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import {
  CCard, CCardBody, CCardHeader, CCol, CRow, CForm, CFormLabel,
  CFormInput, CFormSelect, CButton, CSpinner, CAlert
} from '@coreui/react'
import { [MODULE]API } from '../../api/[module]'

const [ModuleName]Detail = () => {
  const { id } = useParams()
  const navigate = useNavigate()
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    status: 'active',
  })
  const [saving, setSaving] = useState(false)

  useEffect(() => {
    if (id && id !== 'new') {
      loadItem()
    } else {
      setLoading(false)
    }
  }, [id])

  const loadItem = async () => {
    try {
      setLoading(true)
      const response = await [MODULE]API.get[ModuleName](id)
      setFormData(response.data.data)
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to load item')
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    try {
      setSaving(true)
      if (id && id !== 'new') {
        await [MODULE]API.update[ModuleName](id, formData)
      } else {
        await [MODULE]API.create[ModuleName](formData)
      }
      navigate('/[path]')
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to save')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <CRow>
        <CCol xs={12} className="text-center">
          <CSpinner color="primary" />
        </CCol>
      </CRow>
    )
  }

  return (
    <CRow>
      <CCol xs={12} md={8}>
        <CCard>
          <CCardHeader>
            <strong>{id && id !== 'new' ? 'Edit' : 'Create'} [Item]</strong>
          </CCardHeader>
          <CCardBody>
            {error && <CAlert color="danger">{error}</CAlert>}
            <CForm onSubmit={handleSubmit}>
              <div className="mb-3">
                <CFormLabel htmlFor="name">Name</CFormLabel>
                <CFormInput
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                />
              </div>
              <div className="mb-3">
                <CFormLabel htmlFor="description">Description</CFormLabel>
                <CFormInput
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                />
              </div>
              <div className="mb-3">
                <CFormLabel htmlFor="status">Status</CFormLabel>
                <CFormSelect
                  id="status"
                  name="status"
                  value={formData.status}
                  onChange={handleChange}
                >
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </CFormSelect>
              </div>
              <div className="d-grid gap-2">
                <CButton type="submit" color="primary" disabled={saving}>
                  {saving ? <CSpinner size="sm" className="me-2" /> : null}
                  {id && id !== 'new' ? 'Update' : 'Create'}
                </CButton>
                <CButton type="button" color="secondary" onClick={() => navigate('/[path]')}>
                  Cancel
                </CButton>
              </div>
            </CForm>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  )
}

export default [ModuleName]Detail
```

---

## Template 3: Dashboard with Metrics

```javascript
import React, { useState, useEffect } from 'react'
import {
  CCard, CCardBody, CCardHeader, CCol, CRow, CSpinner, CAlert
} from '@coreui/react'
import { reportsAPI } from '../../api/reports'

const Dashboard = () => {
  const [metrics, setMetrics] = useState({})
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    loadMetrics()
  }, [])

  const loadMetrics = async () => {
    try {
      setLoading(true)
      const [subs, payments, revenue, users, approvals] = await Promise.all([
        reportsAPI.getSubscriptionsReport(),
        reportsAPI.getPaymentsReport(),
        reportsAPI.getRevenueReport(),
        reportsAPI.getUsersReport(),
        reportsAPI.getApprovalsReport(),
      ])
      setMetrics({
        subscriptions: subs.data.data,
        payments: payments.data.data,
        revenue: revenue.data.data,
        users: users.data.data,
        approvals: approvals.data.data,
      })
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to load metrics')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <CRow>
        <CCol xs={12} className="text-center">
          <CSpinner color="primary" />
        </CCol>
      </CRow>
    )
  }

  return (
    <CRow>
      {error && (
        <CCol xs={12}>
          <CAlert color="danger">{error}</CAlert>
        </CCol>
      )}
      
      <CCol xs={12} sm={6} lg={3}>
        <CCard className="mb-4">
          <CCardBody>
            <div className="text-muted small">Active Subscriptions</div>
            <div className="fs-5 fw-bold">{metrics.subscriptions?.active || 0}</div>
          </CCardBody>
        </CCard>
      </CCol>

      <CCol xs={12} sm={6} lg={3}>
        <CCard className="mb-4">
          <CCardBody>
            <div className="text-muted small">Total Revenue</div>
            <div className="fs-5 fw-bold">${metrics.revenue?.total || 0}</div>
          </CCardBody>
        </CCard>
      </CCol>

      <CCol xs={12} sm={6} lg={3}>
        <CCard className="mb-4">
          <CCardBody>
            <div className="text-muted small">Total Users</div>
            <div className="fs-5 fw-bold">{metrics.users?.total || 0}</div>
          </CCardBody>
        </CCard>
      </CCol>

      <CCol xs={12} sm={6} lg={3}>
        <CCard className="mb-4">
          <CCardBody>
            <div className="text-muted small">Pending Approvals</div>
            <div className="fs-5 fw-bold">{metrics.approvals?.pending || 0}</div>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  )
}

export default Dashboard
```

---

## Template 4: Custom Hook for API Calls

```javascript
// src/hooks/useApi.js
import { useState, useEffect } from 'react'

export const useApi = (apiCall, dependencies = []) => {
  const [data, setData] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true)
        const response = await apiCall()
        setData(response.data.data)
        setError(null)
      } catch (err) {
        setError(err.response?.data?.message || 'An error occurred')
        setData(null)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, dependencies)

  return { data, loading, error }
}

// Usage:
// const { data: users, loading, error } = useApi(() => usersAPI.getUsers())
```

---

## Template 5: Utility Formatter Functions

```javascript
// src/utils/formatters.js

export const formatDate = (date) => {
  if (!date) return '-'
  return new Date(date).toLocaleDateString()
}

export const formatCurrency = (amount, currency = 'USD') => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
  }).format(amount)
}

export const formatStatus = (status) => {
  const statusMap = {
    active: 'Active',
    inactive: 'Inactive',
    pending: 'Pending',
    completed: 'Completed',
    failed: 'Failed',
  }
  return statusMap[status] || status
}

export const getStatusColor = (status) => {
  const colorMap = {
    active: 'success',
    inactive: 'secondary',
    pending: 'warning',
    completed: 'success',
    failed: 'danger',
  }
  return colorMap[status] || 'secondary'
}
```

---

## Quick Implementation Checklist

### For Each List View:
- [ ] Import API module
- [ ] Add useState for items, loading, error
- [ ] Add useEffect to load items
- [ ] Add delete handler
- [ ] Add navigation to detail page
- [ ] Add error alert
- [ ] Add loading spinner

### For Each Detail Page:
- [ ] Import API module
- [ ] Add useState for form data
- [ ] Add useEffect to load item
- [ ] Add form fields
- [ ] Add submit handler
- [ ] Add error alert
- [ ] Add loading spinner

### For Dashboard:
- [ ] Import all report APIs
- [ ] Fetch all metrics on mount
- [ ] Display metric cards
- [ ] Add loading state
- [ ] Add error handling

---

## File Naming Convention

```
List Views: [Module].js
Detail Pages: [Module]Detail.js
Hooks: use[Module].js
Utils: [module]Utils.js
API: [module].js
```

---

## Testing the Components

1. **List View**: Navigate to `/[path]`, should show loading then items
2. **Detail Page**: Click edit button, should load item data
3. **Create**: Click add button, should show empty form
4. **Delete**: Click delete, should show confirmation
5. **Error**: Disconnect API, should show error message

