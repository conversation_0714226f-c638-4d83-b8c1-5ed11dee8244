<?php

namespace App\Modules\Billing\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Billing\Domain\Services\InvoiceService;
use App\Modules\Billing\Domain\Models\Invoice;
use App\Modules\Billing\Http\Requests\CreateInvoiceRequest;
use App\Modules\Billing\Http\Resources\InvoiceResource;
use Illuminate\Http\Request;

class InvoiceController extends Controller
{
    public function __construct(private readonly InvoiceService $service) {}

    public function index()
    {
        return response()->json(InvoiceResource::collection($this->service->listInvoices()));
    }

    public function store(CreateInvoiceRequest $request)
    {
        $invoice = $this->service->generateInvoice($request->validated());
        return response()->json(new InvoiceResource($invoice), 201);
    }

    public function show(string $id)
    {
        $invoice = $this->service->getInvoiceById($id);
        abort_if(!$invoice, 404);
        return response()->json(new InvoiceResource($invoice));
    }

    public function pdf(string $id)
    {
        $invoice = Invoice::findOrFail($id);
        return response()->json(['pdf' => $this->service->generatePdf($invoice)]);
    }

    public function send(string $id)
    {
        $invoice = Invoice::findOrFail($id);
        $this->service->sendInvoice($invoice);
        return response()->json(['sent' => true]);
    }
}
