<?php

namespace App\Modules\Billing\Domain\Events;

use App\Modules\Shared\Domain\Events\DomainEvent;

class SubscriptionCreated extends DomainEvent
{
    public function __construct(public readonly string $subscriptionId, ?string $organizationId = null, ?string $userId = null, array $payload = [])
    {
        parent::__construct($organizationId, $userId, array_merge($payload, ['subscription_id' => $subscriptionId]));
    }
}
