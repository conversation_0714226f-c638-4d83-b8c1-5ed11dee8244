<?php

require 'vendor/autoload.php';

$app = require 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== PORTAL OWNER VERIFICATION ===\n\n";

// Check Organization
$org = \App\Modules\Organizations\Domain\Models\Organization::where('type', 'portal_owner')->first();
if ($org) {
    echo "✅ Portal Owner Organization Found:\n";
    echo "   Name: " . $org->name . "\n";
    echo "   ID: " . $org->id . "\n";
    echo "   Code: " . $org->code . "\n";
    echo "   Email: " . $org->email . "\n";
    echo "   Status: " . $org->status . "\n\n";
} else {
    echo "❌ Portal Owner Organization NOT FOUND\n\n";
    exit(1);
}

// Check Admin User
$user = \App\Modules\Users\Domain\Models\User::where('organization_id', $org->id)->first();
if ($user) {
    echo "✅ Admin User Found:\n";
    echo "   Name: " . $user->name . "\n";
    echo "   Email: " . $user->email . "\n";
    echo "   Status: " . $user->status . "\n";
    echo "   Email Verified: " . ($user->email_verified_at ? 'Yes' : 'No') . "\n\n";
} else {
    echo "❌ Admin User NOT FOUND\n\n";
    exit(1);
}

// Check Admin Role
$role = \App\Modules\RolesPermissions\Domain\Models\Role::where('organization_id', $org->id)->first();
if ($role) {
    echo "✅ Admin Role Found:\n";
    echo "   Name: " . $role->name . "\n";
    echo "   Slug: " . $role->slug . "\n";
    echo "   System Role: " . ($role->is_system ? 'Yes' : 'No') . "\n";
    echo "   Permissions: " . $role->permissions->count() . "\n\n";
} else {
    echo "❌ Admin Role NOT FOUND\n\n";
    exit(1);
}

// Check Role Assignment
$assignment = \App\Modules\RolesPermissions\Domain\Models\RoleAssignment::where('user_id', $user->id)->first();
if ($assignment) {
    echo "✅ Role Assignment Found:\n";
    echo "   User: " . $assignment->user->name . "\n";
    echo "   Role: " . $assignment->role->name . "\n";
    echo "   Scope: " . $assignment->scope . "\n\n";
} else {
    echo "❌ Role Assignment NOT FOUND\n\n";
    exit(1);
}

// Check Plan
$plan = \App\Modules\Billing\Domain\Models\Plan::where('organization_id', $org->id)->first();
if ($plan) {
    echo "✅ Portal Owner Plan Found:\n";
    echo "   Name: " . $plan->name . "\n";
    echo "   Slug: " . $plan->slug . "\n";
    echo "   Price: $" . $plan->price . "\n";
    echo "   Active: " . ($plan->is_active ? 'Yes' : 'No') . "\n\n";
} else {
    echo "❌ Plan NOT FOUND\n\n";
    exit(1);
}

// Check Subscription
$subscription = \App\Modules\Billing\Domain\Models\Subscription::where('organization_id', $org->id)->first();
if ($subscription) {
    echo "✅ Portal Owner Subscription Found:\n";
    echo "   Plan: " . $subscription->plan->name . "\n";
    echo "   Status: " . $subscription->status . "\n";
    echo "   Auto Renew: " . ($subscription->auto_renew ? 'Yes' : 'No') . "\n";
    echo "   Price: $" . $subscription->price . "\n\n";
} else {
    echo "❌ Subscription NOT FOUND\n\n";
    exit(1);
}

echo "=== ALL CHECKS PASSED ✅ ===\n";
echo "\nLOGIN CREDENTIALS:\n";
echo "Email: <EMAIL>\n";
echo "Password: Admin@123456\n";
