# Backend API Summary

**Tags**: backend, api, documentation, endpoints, modules

## API Overview
- **Total Endpoints**: 78
- **Authentication**: JWT (24h expiration)
- **Base URL**: `http://localhost:8000/api/v1`
- **Response Format**: JSON
- **Error Handling**: Standardized error responses

## Module Breakdown

### 1. Authentication & Users (9 endpoints)
- `POST /auth/register` - User registration
- `POST /auth/login` - User login
- `POST /auth/logout` - User logout
- `POST /auth/refresh-token` - Refresh JWT token
- `POST /auth/request-otp` - Request OTP
- `POST /auth/verify-otp` - Verify OTP
- `POST /auth/reset-password` - Reset password
- `POST /auth/setup-mfa` - Setup MFA
- `POST /auth/verify-mfa` - Verify MFA
- `GET /me` - Get current user
- `PATCH /me` - Update current user
- `GET /users` - List users
- `POST /users` - Create user
- `GET /users/{id}` - Get user
- `PATCH /users/{id}` - Update user
- `DELETE /users/{id}` - Delete user
- `POST /users/bulk-import` - Bulk import users
- `GET /users/{id}/activity` - Get user activity
- `PATCH /users/{id}/status` - Update user status

### 2. Organizations (7 endpoints)
- `GET /organizations` - List organizations
- `POST /organizations` - Create organization
- `GET /organizations/{id}` - Get organization
- `PATCH /organizations/{id}` - Update organization
- `DELETE /organizations/{id}` - Delete organization
- `GET /organizations/{id}/hierarchy` - Get organization hierarchy
- `GET /organizations/{id}/members` - Get organization members

### 3. Roles & Permissions (13 endpoints)
- `GET /roles` - List roles
- `POST /roles` - Create role
- `GET /roles/{id}` - Get role
- `PATCH /roles/{id}` - Update role
- `DELETE /roles/{id}` - Delete role
- `GET /permissions` - List permissions
- `POST /permissions` - Create permission
- `GET /permissions/{id}` - Get permission
- `PATCH /permissions/{id}` - Update permission
- `DELETE /permissions/{id}` - Delete permission
- `POST /roles/{id}/permissions/{permissionId}` - Assign permission to role
- `DELETE /roles/{id}/permissions/{permissionId}` - Remove permission from role
- `GET /role-assignments` - List role assignments
- `POST /role-assignments` - Create role assignment
- `GET /role-assignments/{id}` - Get role assignment
- `PATCH /role-assignments/{id}` - Update role assignment
- `DELETE /role-assignments/{id}` - Delete role assignment

### 4. Billing & Subscriptions (23 endpoints)
- `GET /plans` - List plans
- `GET /plans/{id}` - Get plan
- `GET /subscriptions` - List subscriptions
- `POST /subscriptions` - Create subscription
- `GET /subscriptions/{id}` - Get subscription
- `PATCH /subscriptions/{id}` - Update subscription
- `DELETE /subscriptions/{id}` - Delete subscription
- `POST /subscriptions/{id}/upgrade` - Upgrade subscription
- `POST /subscriptions/{id}/downgrade` - Downgrade subscription
- `POST /subscriptions/{id}/cancel` - Cancel subscription
- `POST /subscriptions/{subscriptionId}/addons` - Add add-on to subscription
- `DELETE /subscriptions/{subscriptionId}/addons/{addonId}` - Remove add-on from subscription
- `GET /organizations/{organizationId}/usage` - Get organization usage
- `GET /organizations/{organizationId}/usage/alerts` - Get organization usage alerts
- `GET /subscriptions/{subscriptionId}/usage/history` - Get subscription usage history
- `GET /payments` - List payments
- `POST /payments` - Create payment
- `GET /payments/{id}` - Get payment
- `POST /payments/{id}/retry` - Retry payment
- `POST /payments/{id}/refund` - Refund payment
- `GET /invoices` - List invoices
- `POST /invoices` - Create invoice
- `GET /invoices/{id}` - Get invoice
- `GET /invoices/{id}/pdf` - Get invoice PDF
- `POST /invoices/{id}/send` - Send invoice

### 5. Approvals (5 endpoints)
- `GET /approvals` - List approvals
- `POST /approvals` - Create approval
- `GET /approvals/{id}` - Get approval
- `POST /approvals/{id}/approve` - Approve approval
- `POST /approvals/{id}/reject` - Reject approval
- `POST /approvals/{id}/comments` - Add comment to approval

### 6. Notifications (3 endpoints)
- `GET /notifications` - List notifications
- `GET /notifications/{id}` - Get notification
- `PATCH /notifications/{id}/read` - Mark notification as read

### 7. Reports (5 endpoints)
- `GET /reports/subscriptions` - Get subscriptions report
- `GET /reports/payments` - Get payments report
- `GET /reports/revenue` - Get revenue report
- `GET /reports/users` - Get users report
- `GET /reports/approvals` - Get approvals report

### 8. Settings (4 endpoints)
- `GET /settings` - Get all settings
- `PATCH /settings` - Update multiple settings
- `GET /settings/{key}` - Get setting by key
- `PATCH /settings/{key}` - Update setting by key

### 9. Webhooks (5 endpoints)
- `GET /webhooks` - List webhooks
- `POST /webhooks` - Create webhook
- `GET /webhooks/{id}` - Get webhook
- `PATCH /webhooks/{id}` - Update webhook
- `DELETE /webhooks/{id}` - Delete webhook
- `GET /webhooks/{id}/events` - Get webhook events

## Error Handling
All error responses follow the format:
```json
{
  "success": false,
  "error": {
    "code": "error_code",
    "message": "Human-readable error message",
    "details": {}
  }
}
```

## Authentication
All endpoints except `/auth/*` require a valid JWT token in the `Authorization` header:
```
Authorization: Bearer <token>
```

## Rate Limiting
- 60 requests per minute per IP for authenticated requests
- 30 requests per minute per IP for unauthenticated requests

## Versioning
API versioning is done through the URL path (`/api/v1/...`).

## Testing
All endpoints are covered by automated tests with 100% test coverage.
