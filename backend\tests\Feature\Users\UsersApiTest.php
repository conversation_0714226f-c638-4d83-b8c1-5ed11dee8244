<?php

namespace Tests\Feature\Users;

use Tests\TestCase;
use Tests\Support\CreatesOrgUser;
use Illuminate\Foundation\Testing\RefreshDatabase;

class UsersApiTest extends TestCase
{
    use RefreshDatabase, CreatesOrgUser;

    public function test_users_crud_and_extras(): void
    {
        $org = $this->createOrganization();
        $user = $this->createUserInOrg($org);
        $this->actingAs($user);
        // grant permissions
        $this->grantPermission($user, 'users.create');
        $this->grantPermission($user, 'users.update');
        $this->grantPermission($user, 'users.delete');

        // index
        $this->get('/api/v1/users')->assertStatus(200);

        // store
        $payload = [
            'organization_id' => $org->id,
            'name' => 'Alice',
            'email' => '<EMAIL>',
            'password' => 'SecurePass123!'
        ];
        $create = $this->postJson('/api/v1/users', $payload)->assertStatus(201)->json('id');
        $this->assertNotEmpty($create);

        // show
        $this->getJson('/api/v1/users/'.$create)->assertStatus(200)->assertJsonFragment(['email' => '<EMAIL>']);

        // update
        $this->patchJson('/api/v1/users/'.$create, ['name' => 'Alice Updated'])->assertStatus(200)->assertJsonFragment(['name' => 'Alice Updated']);

        // activity (empty initially)
        $this->getJson('/api/v1/users/'.$create.'/activity')->assertStatus(200);

        // status change
        $this->patchJson('/api/v1/users/'.$create.'/status', ['status' => 'inactive'])->assertStatus(200)->assertJsonFragment(['status' => 'inactive']);

        // bulk import
        $this->postJson('/api/v1/users/bulk-import', [
            'users' => [
                ['name' => 'B1', 'email' => '<EMAIL>', 'password' => 'Password1!'],
                ['name' => 'B2', 'email' => '<EMAIL>', 'password' => 'Password1!'],
            ],
        ])->assertStatus(201)->assertJsonFragment(['count' => 2]);

        // destroy
        $this->deleteJson('/api/v1/users/'.$create)->assertStatus(200)->assertJsonFragment(['deleted' => true]);
    }
}
