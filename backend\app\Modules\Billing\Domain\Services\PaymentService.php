<?php

namespace App\Modules\Billing\Domain\Services;

use App\Modules\Shared\Domain\Services\BaseService;
use App\Modules\Billing\Domain\Models\Payment;
use App\Modules\Billing\Domain\Models\Subscription;
use App\Modules\Billing\Domain\Models\Invoice;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class PaymentService extends BaseService
{
    public function processPayment(array $data): Payment
    {
        return DB::transaction(function () use ($data) {
            $payment = Payment::create([
                'organization_id' => $data['organization_id'] ?? $this->getCurrentOrganizationId(),
                'subscription_id' => $data['subscription_id'] ?? null,
                'invoice_id' => $data['invoice_id'] ?? null,
                'payment_method_id' => $data['payment_method_id'] ?? null,
                'amount' => $data['amount'],
                'currency' => $data['currency'] ?? config('app.currency', 'USD'),
                'status' => 'completed',
                'provider' => $data['provider'] ?? 'manual',
                'provider_transaction_id' => $data['provider_transaction_id'] ?? null,
                'paid_at' => now(),
                'metadata' => $data['metadata'] ?? [],
            ]);

            if (!empty($data['invoice_id'])) {
                Invoice::where('id', $data['invoice_id'])->update(['status' => 'paid', 'paid_at' => now()]);
            }

            return $payment;
        });
    }

    public function retryPayment(Payment $payment): Payment
    {
        // Simplified retry logic
        $payment->update(['status' => 'completed', 'paid_at' => now()]);
        return $payment;
    }

    public function refundPayment(Payment $payment, float $amount = null): Payment
    {
        $payment->update(['status' => 'refunded']);
        return $payment;
    }

    public function getPaymentById(string $id): ?Payment
    {
        return Payment::find($id);
    }

    public function listPayments(array $filters = []): Collection
    {
        $q = Payment::query()->where('organization_id', $filters['organization_id'] ?? $this->getCurrentOrganizationId());
        if (isset($filters['status'])) {
            $q->whereIn('status', (array) $filters['status']);
        }
        return $q->get();
    }

    public function handleWebhook(array $payload): void
    {
        // Stub for gateway webhook handling
        $this->logAction('Payment webhook received', ['payload' => $payload]);
    }

    public function reconcilePayments(): void
    {
        // Stub: reconcile with provider
        $this->logAction('Payment reconciliation executed');
    }
}
