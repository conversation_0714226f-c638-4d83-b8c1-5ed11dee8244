<?php

namespace Database\Factories\Modules\RolesPermissions\Domain\Models;

use App\Modules\RolesPermissions\Domain\Models\RoleAssignment;
use App\Modules\RolesPermissions\Domain\Models\Role;
use App\Modules\Users\Domain\Models\User;
use App\Modules\Organizations\Domain\Models\Organization;
use Illuminate\Database\Eloquent\Factories\Factory;

class RoleAssignmentFactory extends Factory
{
    protected $model = RoleAssignment::class;

    public function definition(): array
    {
        return [
            'id' => $this->faker->uuid(),
            'organization_id' => Organization::factory(),
            'user_id' => User::factory(),
            'role_id' => Role::factory(),
            'assigned_by' => User::factory(),
            'assigned_at' => now(),
            'expires_at' => null,
            'scope' => 'global',
            'scope_id' => null,
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}
