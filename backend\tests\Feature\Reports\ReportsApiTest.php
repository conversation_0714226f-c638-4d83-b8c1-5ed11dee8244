<?php

namespace Tests\Feature\Reports;

use Tests\TestCase;
use Tests\Support\CreatesOrgUser;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ReportsApiTest extends TestCase
{
    use RefreshDatabase, CreatesOrgUser;

    public function test_reports_endpoints(): void
    {
        $org = $this->createOrganization();
        $user = $this->createUserInOrg($org);
        $this->actingAs($user);

        $this->getJson('/api/v1/reports/subscriptions')->assertStatus(200);
        $this->getJson('/api/v1/reports/payments')->assertStatus(200);
        $this->getJson('/api/v1/reports/revenue')->assertStatus(200);
        $this->getJson('/api/v1/reports/users')->assertStatus(200);
        $this->getJson('/api/v1/reports/approvals')->assertStatus(200);
    }
}
