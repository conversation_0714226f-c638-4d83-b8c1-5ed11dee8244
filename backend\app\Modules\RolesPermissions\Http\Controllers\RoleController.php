<?php

namespace App\Modules\RolesPermissions\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\RolesPermissions\Domain\Services\RoleService;
use App\Modules\RolesPermissions\Domain\Models\Role;
use App\Modules\RolesPermissions\Domain\Models\Permission;
use App\Modules\RolesPermissions\Http\Requests\CreateRoleRequest;
use App\Modules\RolesPermissions\Http\Requests\UpdateRoleRequest;
use App\Modules\RolesPermissions\Http\Resources\RoleResource;
use Illuminate\Http\Request;

class RoleController extends Controller
{
    public function __construct(private readonly RoleService $service) {}

    public function index()
    {
        return response()->json(RoleResource::collection($this->service->listRoles()));
    }

    public function store(CreateRoleRequest $request)
    {
        $role = $this->service->createRole($request->validated());
        return response()->json(new RoleResource($role), 201);
    }

    public function show(string $id)
    {
        $role = $this->service->getRoleById($id);
        abort_if(!$role, 404);
        return response()->json(new RoleResource($role));
    }

    public function update(UpdateRoleRequest $request, string $id)
    {
        $role = $this->service->getRoleById($id);
        abort_if(!$role, 404);
        $updated = $this->service->updateRole($role, $request->validated());
        return response()->json(new RoleResource($updated));
    }

    public function destroy(string $id)
    {
        $role = $this->service->getRoleById($id);
        abort_if(!$role, 404);
        return response()->json(['deleted' => $this->service->deleteRole($role)]);
    }

    public function attachPermission(string $id, string $permissionId)
    {
        $role = Role::findOrFail($id);
        $this->authorize('update', $role);
        $perm = Permission::findOrFail($permissionId);
        $this->service->attachPermission($role, $perm);
        return response()->json(['attached' => true]);
    }

    public function detachPermission(string $id, string $permissionId)
    {
        $role = Role::findOrFail($id);
        $this->authorize('update', $role);
        $perm = Permission::findOrFail($permissionId);
        $this->service->detachPermission($role, $perm);
        return response()->json(['detached' => true]);
    }
}
