<?php

namespace App\Http\Middleware;

use App\Modules\Users\Domain\Models\User;
use App\Modules\Users\Domain\Models\UserSession;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class AuthenticateByToken
{
    public function handle(Request $request, Closure $next): Response
    {
        $token = $request->bearerToken();
        if ($token) {
            $session = UserSession::query()
                ->where('token', $token)
                ->where('is_active', true)
                ->where('expires_at', '>', now())
                ->first();

            if ($session) {
                $user = User::find($session->user_id);
                if ($user) {
                    $request->setUserResolver(fn () => $user);
                    $session->update(['last_activity' => now()]);
                }
            }
        }

        return $next($request);
    }
}
