<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('organization_id')->comment('Organization this user belongs to');
            $table->string('name')->comment('User full name');
            $table->string('email')->comment('User email address');
            $table->string('mobile', 20)->nullable()->comment('User mobile number');
            $table->string('password')->comment('Hashed password');
            $table->enum('status', ['active', 'inactive', 'suspended', 'pending', 'locked'])->default('pending');
            $table->uuid('approval_request_id')->nullable()->comment('Link to approval request if pending');
            $table->timestamp('email_verified_at')->nullable();
            $table->timestamp('mobile_verified_at')->nullable();
            $table->boolean('mfa_enabled')->default(false)->comment('Multi-factor authentication enabled');
            $table->string('mfa_secret')->nullable()->comment('MFA secret key');
            $table->integer('failed_login_attempts')->default(0);
            $table->timestamp('locked_until')->nullable();
            $table->timestamp('last_login_at')->nullable();
            $table->string('last_login_ip', 45)->nullable();
            $table->string('avatar')->nullable();
            $table->string('timezone', 50)->default('UTC');
            $table->string('language', 10)->default('en');
            $table->json('preferences')->nullable()->comment('User preferences');
            $table->rememberToken();
            $table->timestamps();
            $table->softDeletes();
            
            // Unique constraint with organization scoping
            $table->unique(['organization_id', 'email']);
            
            // Indexes
            $table->index('organization_id');
            $table->index('email');
            $table->index('status');
            $table->index('approval_request_id');
            $table->index('created_at');
            
            // Foreign key
            $table->foreign('organization_id')
                  ->references('id')
                  ->on('organizations')
                  ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
    }
};
