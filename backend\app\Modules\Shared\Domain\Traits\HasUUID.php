<?php

namespace App\Modules\Shared\Domain\Traits;

use Illuminate\Support\Str;

trait HasUUID
{
    public static function bootHasUUID(): void
    {
        static::creating(function ($model) {
            if (empty($model->{$model->getKeyName()})) {
                $model->{$model->getKeyName()} = (string) Str::uuid();
            }
        });
    }

    public function initializeHasUUID(): void
    {
        $this->keyType = 'string';
        $this->incrementing = false;
    }
}
