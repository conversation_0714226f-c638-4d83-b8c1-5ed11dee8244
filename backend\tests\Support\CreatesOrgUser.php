<?php

namespace Tests\Support;

use App\Modules\Organizations\Domain\Models\Organization;
use App\Modules\RolesPermissions\Domain\Models\Permission;
use App\Modules\RolesPermissions\Domain\Models\UserPermission;
use App\Modules\Users\Domain\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

trait CreatesOrgUser
{
    protected function createOrganization(array $overrides = []): Organization
    {
        $id = (string) \Illuminate\Support\Str::uuid();
        $now = now();
        $data = array_merge([
            'id' => $id,
            'name' => 'Test Org',
            'code' => strtoupper(uniqid('ORG')),
            'type' => 'organization',
            'status' => 'active',
            'timezone' => 'UTC',
            'currency' => 'USD',
            'language' => 'en',
            'created_at' => $now,
            'updated_at' => $now,
        ], $overrides);
        DB::table('organizations')->insert($data);
        return Organization::findOrFail($id);
    }

    protected function createUserInOrg(Organization $org, array $overrides = []): User
    {
        return User::create(array_merge([
            'organization_id' => $org->id,
            'name' => 'John Doe',
            'email' => uniqid('user').'@example.com',
            'password' => Hash::make('password123'),
            'status' => 'active',
        ], $overrides));
    }

    protected function grantPermission(User $user, string $slug): void
    {
        $perm = Permission::firstOrCreate([
            'organization_id' => $user->organization_id,
            'slug' => $slug,
        ], [
            'name' => ucfirst(str_replace('.', ' ', $slug)),
            'module' => explode('.', $slug)[0] ?? 'general',
            'description' => $slug,
        ]);

        UserPermission::create([
            'organization_id' => $user->organization_id,
            'user_id' => $user->id,
            'permission_id' => $perm->id,
            'granted_by' => $user->id,
            'granted_at' => now(),
        ]);
    }

    protected function createPlan($org, array $overrides = []): \App\Modules\Billing\Domain\Models\Plan
    {
        $id = (string) \Illuminate\Support\Str::uuid();
        $now = now();
        $data = array_merge([
            'id' => $id,
            'organization_id' => $org->id,
            'name' => 'Plan',
            'slug' => 'plan-'.strtolower(\Illuminate\Support\Str::random(5)),
            'price' => 10.00,
            'created_at' => $now,
            'updated_at' => $now,
        ], $overrides);
        DB::table('plans')->insert($data);
        return \App\Modules\Billing\Domain\Models\Plan::findOrFail($id);
    }
}
