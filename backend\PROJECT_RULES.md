# PROJECT ARCHITECTURE & RULES

**Last Updated**: January 29, 2025  
**Status**: MANDATORY - ALL DEVELOPMENT MUST FOLLOW THESE RULES

---

## 🎯 PROJECT OVERVIEW

This is a **Multi-Tenant SaaS Portal** for ERP/Management systems where:
- **Portal Owner (YOU)** provides the platform
- **Tenants** subscribe and pay to use your portal
- **Each Tenant** can create unlimited sub-organizations (branches/divisions)
- **Portal Owner** has FULL CONTROL over tenant access, features, and billing

---

## 🏗️ ORGANIZATIONAL HIERARCHY

### 4-Level Structure:

```
Level 1: Portal Owner (Root)
└── id: 1, type: "portal_owner", parent_id: null

Level 2: Tenant Organizations (Customers)
├── ABC Retailers (id: 2, type: "tenant", parent_id: 1)
├── XYZ Manufacturing (id: 3, type: "tenant", parent_id: 1)
└── PQR Hospital (id: 4, type: "tenant", parent_id: 1)

Level 3+: Sub-Organizations (Tenant's Branches/Divisions)
ABC Retailers
├── Delhi Branch (id: 5, type: "sub_organization", parent_id: 2)
├── Mumbai Branch (id: 6, type: "sub_organization", parent_id: 2)
└── Warehouse (id: 7, type: "sub_organization", parent_id: 2)
    └── Warehouse A (id: 8, type: "sub_organization", parent_id: 7)
```

**Key Rules**:
- Only ONE portal owner organization (type: "portal_owner", parent_id: null)
- Tenant organizations have parent_id = portal_owner_id
- Sub-organizations nest infinitely
- Use closure table for hierarchy queries

---

## 💰 BILLING & SUBSCRIPTION MODEL

### Subscription Structure: HYBRID

**Base Subscription (At Tenant Level)**:
```
Tenant subscribes to: "Pro Plan" (₹10,000/month)
Includes:
- Users: 25
- Sub-Organizations: 5
- Storage: 50 GB
- Hierarchy Depth: 5 levels
- Modules: Inventory + Accounting
```

**Add-Ons (Per Resource)**:
```
Extra user: ₹500/user/month
Extra sub-org: ₹1,500-₹3,000/org/month (varies by plan)
Extra storage: ₹100/GB/month
Extra hierarchy level (beyond limit): ₹2,000/level/month
```

**Billing Rules**:
- One subscription per tenant organization
- Sub-organizations inherit parent's subscription
- Add-ons tracked separately in `subscription_add_ons` table
- Monthly invoice = base price + all active add-ons
- Proration applied for mid-cycle changes

---

## 🔄 APPROVAL WORKFLOW (CRITICAL)

### Rule: ALL limit overages require Portal Owner approval

**What Requires Approval**:
1. Creating users beyond plan limit
2. Creating sub-organizations beyond plan limit
3. Storage usage beyond plan limit
4. Increasing hierarchy depth beyond limit
5. Enabling additional modules
6. Plan upgrades/downgrades

**Approval Flow**:
```
Tenant Action (Exceeds Limit)
    ↓
Resource Created with Status: PENDING_APPROVAL
    ↓
Approval Request Created
    ↓
Notification to Portal Owner
    ↓
Portal Owner Reviews in Dashboard
    ↓
[APPROVE] → Resource Activated + Add-on Applied + Invoice Updated
[REJECT] → Resource Deleted + Tenant Notified
```

**Status Values**:
- **Users**: `status = 'pending'` + `approval_request_id` set
- **Organizations**: `status = 'pending_approval'` + `approval_request_id` set

---

## 📊 RESOURCE LIMITS & TRACKING

### Plan Limits:
- `user_limit`: Number of users (0 = unlimited)
- `sub_org_limit`: Number of sub-organizations (0 = unlimited)
- `storage_limit`: Storage in GB (0 = unlimited)
- `hierarchy_depth_limit`: Max nesting levels (0 = unlimited)
- `api_calls_limit`: API calls per day (0 = unlimited)
- `modules`: JSON array of enabled module names

### Subscription Usage Tracking:
- `user_count`: Current total users across all sub-orgs
- `sub_org_count`: Current number of sub-organizations
- `storage_used`: Current storage in bytes
- `hierarchy_depth`: Current maximum hierarchy depth
- `add_ons`: JSON array of active add-ons

### Usage Calculation Rules:
- **Users**: Count ALL users in tenant + all sub-orgs (entire hierarchy)
- **Sub-Orgs**: Count ALL descendants of tenant organization
- **Storage**: Sum of all file uploads across tenant + sub-orgs
- **Hierarchy**: Max depth from tenant to deepest sub-org

---

## 🎛️ CONTROL & PERMISSIONS

### Portal Owner Can:
✅ Create/delete tenant organizations
✅ Assign subscription plans
✅ Enable/disable modules for tenants
✅ Set resource limits
✅ Approve/reject resource requests
✅ Generate invoices
✅ View all tenant data (admin access)
✅ Suspend tenant accounts

### Tenant Can:
✅ Configure company details (name, logo, GST, etc.)
✅ Create sub-organizations (subject to limit + approval)
✅ Create users (subject to limit + approval)
✅ Assign roles and permissions to users
✅ Enable/disable features for specific sub-orgs (if portal owner allows)
✅ Manage their business data

### Tenant CANNOT:
❌ Choose or change subscription plan
❌ Enable modules not in their plan
❌ Exceed resource limits without approval
❌ Access other tenants' data
❌ Modify billing/payment settings

---

## 🔐 DATA ACCESS & RBAC

### Organizational Scope Rules:

**Portal Owner Users**:
- See ALL data from ALL tenants
- Full system admin access

**Tenant Organization Admin**:
- See ALL data from their organization + ALL sub-orgs
- Can manage users and sub-orgs
- Can configure organization settings

**Sub-Organization Manager**:
- See ONLY data from their sub-org
- Can manage users in their sub-org only
- Cannot see sibling sub-orgs

**Sub-Organization Employee**:
- See ONLY data from their sub-org
- Limited by role permissions
- Cannot manage users

### Query Scoping (MANDATORY):
```php
// Every query must filter by organization_id or hierarchy
$query = Model::where('organization_id', auth()->user()->organization_id)
    ->orWhereIn('organization_id', auth()->user()->accessibleOrganizationIds());
```

---

## 💡 RESOURCE-BASED PRICING STRATEGY

### VPS Infrastructure Costs:
- **Users**: More DB connections, CPU, RAM
- **Sub-Orgs**: More complex queries (JOINs, recursion)
- **Hierarchy Depth**: Expensive recursive queries
- **Storage**: Disk space + backups

### Pricing Formula:
```
Monthly Cost = Base Plan + Active Add-Ons

Example:
Pro Plan: ₹10,000
+ 5 extra users: 5 × ₹500 = ₹2,500
+ 2 extra sub-orgs: 2 × ₹2,000 = ₹4,000
+ 10 GB extra storage: 10 × ₹100 = ₹1,000
= Total: ₹17,500/month
```

### Add-On Pricing by Plan:
```
Basic Plan:
- User: ₹500/user
- Sub-Org: ₹1,500/org
- Storage: ₹100/GB

Pro Plan:
- User: ₹500/user
- Sub-Org: ₹2,000/org
- Storage: ₹100/GB

Enterprise Plan:
- User: ₹400/user
- Sub-Org: ₹2,500/org
- Storage: ₹75/GB
```

---

## 🚨 CRITICAL IMPLEMENTATION RULES

### 1. User Creation:
```php
// BEFORE creating user:
1. Get user's organization
2. Find tenant (walk up hierarchy to type='tenant')
3. Check tenant's subscription user_count vs user_limit
4. If exceeded:
   - Create user with status='pending'
   - Create approval request
   - Link user.approval_request_id
   - Notify portal owner
   - Return user (but inactive)
5. If within limit:
   - Create user with status='active'
   - Increment subscription.user_count
   - Send welcome email
```

### 2. Sub-Organization Creation:
```php
// BEFORE creating sub-org:
1. Get parent organization
2. Find tenant (walk up hierarchy to type='tenant')
3. Count current sub-orgs under tenant
4. Check count vs subscription.plan.sub_org_limit
5. If exceeded:
   - Create org with status='pending_approval'
   - Create approval request
   - Link org.approval_request_id
   - Insert closure table entries
   - Notify portal owner
   - Return org (but inactive)
6. If within limit:
   - Create org with status='active'
   - Insert closure table entries
   - Increment subscription.sub_org_count
```

### 3. Storage Upload:
```php
// BEFORE accepting file upload:
1. Get user's organization
2. Find tenant
3. Check subscription.storage_used vs plan.storage_limit
4. If at 100%:
   - Auto-create approval request for +10GB
   - Allow upload up to 105% (grace period)
5. If at 105%:
   - Block upload
   - Show error message
   - Wait for portal owner approval
```

### 4. Hierarchy Depth:
```php
// BEFORE creating deeply nested sub-org:
1. Calculate depth from tenant to new org
2. Check depth vs plan.hierarchy_depth_limit
3. If exceeded:
   - Block creation
   - Create approval request
   - Notify portal owner
```

---

## 📋 DATABASE SCHEMA RULES

### Organizations Table:
```sql
status ENUM('active', 'inactive', 'suspended', 'pending_approval')
approval_request_id UUID NULLABLE (links to approval_requests)
type ENUM('portal_owner', 'tenant', 'sub_organization')
```

### Users Table:
```sql
status ENUM('active', 'inactive', 'suspended', 'pending', 'locked')
approval_request_id UUID NULLABLE (links to approval_requests)
```

### Plans Table:
```sql
user_limit INT (0 = unlimited)
sub_org_limit INT (0 = unlimited)
storage_limit INT (GB, 0 = unlimited)
hierarchy_depth_limit INT (0 = unlimited)
api_calls_limit INT (per day, 0 = unlimited)
modules JSON (array of module names)
```

### Subscriptions Table:
```sql
user_count INT (current count)
sub_org_count INT (current count)
storage_used BIGINT (bytes)
hierarchy_depth INT (current depth)
add_ons JSON (active add-ons)
```

### Approval Requests Table:
```sql
resource_type ENUM('user', 'sub_org', 'storage', 'hierarchy_level', 'module', 'plan_change')
current_limit INT
requested_limit INT
billing_impact DECIMAL (monthly cost)
urgency ENUM('low', 'normal', 'high', 'urgent')
```

---

## 🔍 TESTING REQUIREMENTS

### Every Feature Must Test:
1. ✅ Within limit scenario (should work)
2. ✅ At limit scenario (should work)
3. ✅ Exceeded limit scenario (should create approval request)
4. ✅ Approval flow (approve/reject)
5. ✅ Add-on creation and billing
6. ✅ Proration calculation
7. ✅ Invoice generation
8. ✅ Data isolation (tenant cannot see other tenant's data)

---

## 📝 CODING STANDARDS

### Service Layer:
- One service per aggregate root
- Services handle business logic, not controllers
- Use transactions for multi-step operations
- Return models, not arrays

### Repository Layer:
- One repository per model
- Handle only data access
- No business logic in repositories

### Controller Layer:
- Thin controllers
- Delegate to services
- Return JSON responses
- Use Form Requests for validation

### Model Layer:
- Use traits (HasUUID, HasOrganizationId, HasAudit)
- Define relationships
- Add helper methods (isActive(), isPendingApproval())
- Cast dates and JSON fields

---

## 🚀 DEPLOYMENT RULES

### Before Deployment:
1. ✅ All migrations run successfully
2. ✅ Portal owner organization seeded (id: 1)
3. ✅ Default plans created (Basic, Pro, Enterprise)
4. ✅ Super admin user created
5. ✅ All tests passing
6. ✅ ENV variables set correctly

### Environment Variables:
```
PORTAL_OWNER_ORG_ID=<uuid-of-portal-owner-org>
DEFAULT_PLAN_ID=<uuid-of-basic-plan>
```

---

## ⚠️ IMPORTANT REMINDERS

1. **ALWAYS** check resource limits before creating users/sub-orgs
2. **ALWAYS** use organizational scoping in queries
3. **ALWAYS** create approval requests for limit overages
4. **ALWAYS** update subscription usage counts
5. **ALWAYS** calculate prorated charges for mid-cycle changes
6. **NEVER** allow tenants to modify their own subscription
7. **NEVER** allow tenants to see other tenants' data
8. **NEVER** skip the approval workflow
9. **NEVER** forget to notify portal owner
10. **NEVER** hard-code pricing (use plan-based pricing)

---

## 📚 REFERENCE DOCUMENTS

1. **PROJECT_RULES.md** (this file) - Architecture & implementation rules
2. **ARCHITECTURE_UNDERSTANDING.md** - Detailed system architecture
3. **APPROVAL_WORKFLOW_AND_RESOURCE_PRICING.md** - Approval flow & pricing details
4. **README.md** - Setup and installation instructions

---

**THIS IS THE SINGLE SOURCE OF TRUTH FOR ALL DEVELOPMENT**
