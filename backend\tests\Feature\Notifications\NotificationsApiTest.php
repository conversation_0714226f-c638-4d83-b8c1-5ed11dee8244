<?php

namespace Tests\Feature\Notifications;

use Tests\TestCase;
use Tests\Support\CreatesOrgUser;
use Illuminate\Foundation\Testing\RefreshDatabase;

class NotificationsApiTest extends TestCase
{
    use RefreshDatabase, CreatesOrgUser;

    public function test_notifications_index_show_mark_read(): void
    {
        $org = $this->createOrganization();
        $user = $this->createUserInOrg($org);
        $this->actingAs($user);

        // Create one via service
        $notification = \App\Modules\Notifications\Domain\Models\Notification::create([
            'organization_id' => $org->id,
            'user_id' => $user->id,
            'type' => 'in_app',
            'channel' => 'in_app',
            'subject' => 'Hello',
            'message' => 'World',
            'status' => 'sent',
            'sent_at' => now(),
        ]);

        $this->getJson('/api/v1/notifications')->assertStatus(200);
        $this->getJson('/api/v1/notifications/'.$notification->id)->assertStatus(200);
        $this->patchJson('/api/v1/notifications/'.$notification->id.'/read')->assertStatus(200)->assertJsonFragment(['read' => true]);
    }
}
