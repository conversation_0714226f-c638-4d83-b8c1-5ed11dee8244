<?php

namespace Tests\Feature\Billing;

use Tests\TestCase;
use Illuminate\Foundation\Testing\DatabaseMigrations;
use App\Modules\Organizations\Domain\Models\Organization;
use App\Modules\Users\Domain\Models\User;
use App\Modules\Billing\Domain\Models\Plan;
use App\Modules\Billing\Domain\Models\Subscription;
use App\Modules\Billing\Domain\Models\Addon;

class BillingAPITest extends TestCase
{
    use DatabaseMigrations;

    protected Organization $portalOwner;
    protected Organization $tenant;
    protected Plan $plan;
    protected Plan $premiumPlan;
    protected Subscription $subscription;
    protected Addon $addon;
    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->portalOwner = Organization::factory()->create([
            'type' => 'portal_owner',
            'parent_id' => null,
        ]);

        $this->tenant = Organization::factory()->create([
            'type' => 'tenant',
            'parent_id' => $this->portalOwner->id,
        ]);

        $this->plan = Plan::factory()->create([
            'name' => 'Basic Plan',
            'price' => 100.00,
            'user_limit' => 10,
            'sub_org_limit' => 5,
            'storage_limit' => 50,
        ]);

        $this->premiumPlan = Plan::factory()->create([
            'name' => 'Premium Plan',
            'price' => 200.00,
            'user_limit' => 50,
            'sub_org_limit' => 20,
            'storage_limit' => 200,
        ]);

        $this->subscription = Subscription::factory()->create([
            'organization_id' => $this->tenant->id,
            'plan_id' => $this->plan->id,
            'status' => 'active',
            'user_count' => 5,
            'sub_org_count' => 2,
            'starts_at' => now()->subDays(10),
            'ends_at' => now()->addDays(20),
        ]);

        $this->addon = Addon::factory()->create([
            'organization_id' => $this->portalOwner->id,
            'name' => 'Extra Storage',
            'type' => 'storage',
            'price' => 10.00,
            'value' => 10,
        ]);

        $this->user = User::factory()->create([
            'organization_id' => $this->tenant->id,
        ]);
    }

    /** @test */
    public function it_can_add_addon_to_subscription()
    {
        $this->actingAs($this->user);

        $response = $this->postJson("/api/v1/subscriptions/{$this->subscription->id}/addons", [
            'addon_id' => $this->addon->id,
            'quantity' => 2,
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Add-on successfully added to subscription',
            ]);

        $this->assertDatabaseHas('subscription_addon', [
            'subscription_id' => $this->subscription->id,
            'addon_id' => $this->addon->id,
            'quantity' => 2,
        ]);
    }

    /** @test */
    public function it_can_remove_addon_from_subscription()
    {
        $this->actingAs($this->user);

        // Add addon first
        $this->subscription->addonCatalog()->attach($this->addon->id, ['quantity' => 1, 'price' => $this->addon->price]);

        $response = $this->deleteJson("/api/v1/subscriptions/{$this->subscription->id}/addons/{$this->addon->id}");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Add-on successfully removed from subscription',
            ]);

        $this->assertDatabaseMissing('subscription_addon', [
            'subscription_id' => $this->subscription->id,
            'addon_id' => $this->addon->id,
        ]);
    }

    /** @test */
    public function it_can_upgrade_subscription()
    {
        $this->actingAs($this->user);

        $response = $this->postJson("/api/v1/subscriptions/{$this->subscription->id}/upgrade-plan", [
            'new_plan_id' => $this->premiumPlan->id,
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Subscription successfully upgraded',
            ]);

        $this->assertDatabaseHas('subscriptions', [
            'id' => $this->subscription->id,
            'plan_id' => $this->premiumPlan->id,
        ]);
    }

    /** @test */
    public function it_can_downgrade_subscription()
    {
        $this->actingAs($this->user);

        // Start with premium plan
        $this->subscription->update(['plan_id' => $this->premiumPlan->id]);

        $response = $this->postJson("/api/v1/subscriptions/{$this->subscription->id}/downgrade-plan", [
            'new_plan_id' => $this->plan->id,
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Subscription successfully downgraded',
            ]);

        $this->assertDatabaseHas('subscriptions', [
            'id' => $this->subscription->id,
            'plan_id' => $this->plan->id,
        ]);
    }

    /** @test */
    public function it_can_cancel_subscription_immediately()
    {
        $this->actingAs($this->user);

        $response = $this->postJson("/api/v1/subscriptions/{$this->subscription->id}/cancel-subscription", [
            'immediate' => true,
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Subscription successfully cancelled immediately',
            ]);

        $this->assertDatabaseHas('subscriptions', [
            'id' => $this->subscription->id,
            'status' => 'cancelled',
        ]);
    }

    /** @test */
    public function it_can_cancel_subscription_at_period_end()
    {
        $this->actingAs($this->user);

        $response = $this->postJson("/api/v1/subscriptions/{$this->subscription->id}/cancel-subscription", [
            'immediate' => false,
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Subscription will be cancelled at the end of billing period',
            ]);

        $this->assertDatabaseHas('subscriptions', [
            'id' => $this->subscription->id,
            'status' => 'active',
        ]);
        
        $this->assertNotNull($this->subscription->fresh()->cancelled_at);
    }

    /** @test */
    public function it_can_get_current_usage()
    {
        $this->actingAs($this->user);

        $response = $this->getJson("/api/v1/organizations/{$this->tenant->id}/usage");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
            ])
            ->assertJsonStructure([
                'success',
                'data' => [
                    'users',
                    'sub_organizations',
                    'storage',
                ],
            ]);
    }

    /** @test */
    public function it_can_get_usage_alerts()
    {
        $this->actingAs($this->user);

        $response = $this->getJson("/api/v1/organizations/{$this->tenant->id}/usage/alerts");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
            ])
            ->assertJsonStructure([
                'success',
                'data',
            ]);
    }

    /** @test */
    public function it_can_get_usage_history()
    {
        $this->actingAs($this->user);

        $response = $this->getJson("/api/v1/subscriptions/{$this->subscription->id}/usage/history?days=30");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
            ])
            ->assertJsonStructure([
                'success',
                'data',
            ]);
    }

    /** @test */
    public function it_can_calculate_upgrade_price()
    {
        $this->actingAs($this->user);

        $response = $this->postJson("/api/v1/subscriptions/{$this->subscription->id}/calculate-upgrade-price", [
            'new_plan_id' => $this->premiumPlan->id,
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
            ])
            ->assertJsonStructure([
                'success',
                'data' => [
                    'prorated_amount',
                    'days_remaining',
                    'current_plan_daily_rate',
                    'new_plan_daily_rate',
                ],
            ]);
    }

    /** @test */
    public function it_requires_valid_addon_id_when_adding_addon()
    {
        $this->actingAs($this->user);

        $response = $this->postJson("/api/v1/subscriptions/{$this->subscription->id}/addons", [
            'addon_id' => 'invalid-uuid',
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['addon_id']);
    }

    /** @test */
    public function it_requires_valid_plan_id_when_upgrading()
    {
        $this->actingAs($this->user);

        $response = $this->postJson("/api/v1/subscriptions/{$this->subscription->id}/upgrade-plan", [
            'new_plan_id' => 'invalid-uuid',
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['new_plan_id']);
    }

    /** @test */
    public function it_requires_authentication_for_all_endpoints()
    {
        $endpoints = [
            ['POST', "/api/v1/subscriptions/{$this->subscription->id}/addons"],
            ['DELETE', "/api/v1/subscriptions/{$this->subscription->id}/addons/{$this->addon->id}"],
            ['POST', "/api/v1/subscriptions/{$this->subscription->id}/upgrade-plan"],
            ['POST', "/api/v1/subscriptions/{$this->subscription->id}/downgrade-plan"],
            ['POST', "/api/v1/subscriptions/{$this->subscription->id}/cancel-subscription"],
            ['GET', "/api/v1/organizations/{$this->tenant->id}/usage"],
            ['GET', "/api/v1/organizations/{$this->tenant->id}/usage/alerts"],
            ['GET', "/api/v1/subscriptions/{$this->subscription->id}/usage/history"],
        ];

        foreach ($endpoints as [$method, $url]) {
            $response = $this->json($method, $url);
            $response->assertStatus(401);
        }
    }
}
