<?php

namespace App\Modules\Users\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Users\Domain\Services\UserService;
use App\Modules\Users\Domain\Models\User;
use App\Modules\Users\Domain\Models\LoginAuditTrail;
use App\Modules\Users\Http\Requests\UpdateProfileRequest;
use App\Modules\Users\Http\Requests\CreateUserRequest;
use App\Modules\Users\Http\Requests\UpdateUserRequest;
use App\Modules\Users\Http\Requests\ChangeUserStatusRequest;
use App\Modules\Users\Http\Resources\UserResource;
use Illuminate\Http\Request;

class UserController extends Controller
{
    public function __construct(private readonly UserService $users) {}

    public function me(Request $request)
    {
        return response()->json(new UserResource($request->user()));
    }

    public function updateMe(UpdateProfileRequest $request)
    {
        $user = $request->user();
        $updated = $this->users->updateUser($user, $request->validated());
        return response()->json(new UserResource($updated));
    }

    public function index(Request $request)
    {
        return response()->json(UserResource::collection($this->users->listUsers()));
    }

    public function store(CreateUserRequest $request)
    {
        $user = $this->users->createUser($request->validated());
        return response()->json(new UserResource($user), 201);
    }

    public function show(string $id)
    {
        $user = $this->users->getUserById($id);
        abort_if(!$user, 404);
        return response()->json(new UserResource($user));
    }

    public function update(UpdateUserRequest $request, string $id)
    {
        $user = $this->users->getUserById($id);
        abort_if(!$user, 404);
        $updated = $this->users->updateUser($user, $request->validated());
        return response()->json(new UserResource($updated));
    }

    public function destroy(string $id)
    {
        $user = $this->users->getUserById($id);
        abort_if(!$user, 404);
        $this->users->deleteUser($user);
        return response()->json(['deleted' => true]);
    }

    public function bulkImport(Request $request)
    {
        $data = $request->validate([
            'users' => ['required','array','min:1'],
            'users.*.name' => ['required','string','max:100'],
            'users.*.email' => ['required','email','max:255'],
            'users.*.password' => ['required','string','min:8'],
        ]);
        $created = [];
        foreach ($data['users'] as $u) {
            $u['organization_id'] = $request->user()->organization_id;
            $created[] = $this->users->createUser($u);
        }
        return response()->json(['count' => count($created)], 201);
    }

    public function activity(Request $request, string $id)
    {
        $user = $this->users->getUserById($id);
        abort_if(!$user, 404);
        $logs = LoginAuditTrail::where('organization_id', $request->user()->organization_id)
            ->where('user_id', $id)
            ->latest()->paginate(15);
        return response()->json($logs);
    }

    public function changeStatus(ChangeUserStatusRequest $request, string $id)
    {
        $user = $this->users->getUserById($id);
        abort_if(!$user, 404);
        $updated = $this->users->updateUser($user, ['status' => $request->validated()['status']]);
        return response()->json(new UserResource($updated));
    }
}
