<?php

use Illuminate\Support\Facades\Route;

Route::middleware('auth')->group(function () {
    // Notifications
    Route::get('notifications', [\App\Modules\Notifications\Http\Controllers\NotificationController::class, 'index']);
    Route::get('notifications/{id}', [\App\Modules\Notifications\Http\Controllers\NotificationController::class, 'show']);
    Route::patch('notifications/{id}/read', [\App\Modules\Notifications\Http\Controllers\NotificationController::class, 'markAsRead']);
});
