import React, { useState } from 'react'
import {
  CButton,
  CModal,
  CModalBody,
  CModalFooter,
  CModalHeader,
  CModalTitle,
  CAlert,
  CSpinner,
} from '@coreui/react'
import CIcon from '@coreui/icons-react'
import { cilWarning } from '@coreui/icons'
import { accountAPI } from '../../api/account'

const LockAccountModal = ({ visible, onClose, onLocked, onPinRequired }) => {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)

  const handleLock = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await accountAPI.lockAccount()
      const data = await response.json()

      if (response.ok) {
        onLocked()
      } else if (response.status === 400 && (data.setup_required || data.pin_required)) {
        // PIN not set, need to show setup modal
        onClose() // Close this modal
        if (onPinRequired) {
          onPinRequired() // Trigger PIN setup modal
        }
      } else {
        setError(data.message || data.error || 'Failed to lock account')
      }
    } catch (err) {
      setError(err.message || 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    setError(null)
    onClose()
  }

  return (
    <CModal visible={visible} onClose={handleClose} backdrop="static" keyboard={false}>
      <CModalHeader closeButton={!loading}>
        <CModalTitle>Lock Your Account</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div className="d-flex align-items-start mb-3">
          <CIcon icon={cilWarning} size="xl" className="text-warning me-3 mt-1" />
          <div>
            <p className="mb-2">
              <strong>Are you sure you want to lock your account?</strong>
            </p>
            <p className="text-body-secondary mb-0">
              Your account will be temporarily locked. You'll need your PIN to unlock it. The lock will automatically expire after 30 minutes.
            </p>
          </div>
        </div>

        {error && (
          <CAlert color="danger" className="mb-3" onClose={() => setError(null)} dismissible>
            <strong>Error!</strong> {error}
          </CAlert>
        )}

        <div className="p-3 rounded" style={{ backgroundColor: 'var(--cui-secondary-bg, #f8f9fa)' }}>
          <strong className="d-block mb-2">What happens when locked:</strong>
          <ul className="mb-0 ps-3">
            <li>You won't be able to access any features</li>
            <li>You can unlock using your PIN</li>
            <li>You can logout to end the session</li>
            <li>Lock expires automatically after 30 minutes</li>
          </ul>
        </div>
      </CModalBody>
      <CModalFooter>
        <CButton color="secondary" onClick={onClose} disabled={loading}>
          Cancel
        </CButton>
        <CButton color="danger" onClick={handleLock} disabled={loading}>
          {loading ? (
            <>
              <CSpinner size="sm" className="me-2" />
              Locking...
            </>
          ) : (
            'Lock Account'
          )}
        </CButton>
      </CModalFooter>
    </CModal>
  )
}

export default LockAccountModal
