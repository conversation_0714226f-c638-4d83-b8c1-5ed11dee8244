# COMPLETE ARCHITECTURE UNDERSTANDING

**Date**: January 29, 2025  
**Status**: CONFIRMED BY USER - THIS IS THE CORRECT ARCHITECTURE

---

## 🎯 SYSTEM OVERVIEW

This is a **Multi-Tenant SaaS Portal** where:
- **YOU (Portal Owner)** provide a configurable ERP/Management platform
- **Multiple Tenants (Organizations)** subscribe and pay to use your portal
- **Each Tenant** can create unlimited sub-organizations for their internal structure
- **Portal Owner** has FULL CONTROL over tenant access, features, and billing

---

## 🏗️ ORGANIZATIONAL HIERARCHY

### Level 1: Portal Owner (Root)
```
Portal Owner Organization
├── id: 1
├── name: "Your Company Pvt Ltd"
├── type: "portal_owner"
├── parent_id: null
└── Special privileges: Full system access, billing management, tenant management
```

### Level 2: Tenant Organizations (Customers)
```
Tenant Organizations (Direct children of Portal Owner)
├── Organization A: "ABC Retailers Pvt Ltd"
│   ├── id: 2
│   ├── parent_id: 1 (Portal Owner)
│   ├── type: "tenant"
│   └── Has subscription/billing relationship with Portal Owner
│
├── Organization B: "XYZ Manufacturing Co"
│   ├── id: 3
│   ├── parent_id: 1 (Portal Owner)
│   ├── type: "tenant"
│   └── Has subscription/billing relationship with Portal Owner
│
└── Organization C: "PQR Hospital Group"
    ├── id: 4
    ├── parent_id: 1 (Portal Owner)
    ├── type: "tenant"
    └── Has subscription/billing relationship with Portal Owner
```

### Level 3+: Sub-Organizations (Tenant's Internal Structure)
```
ABC Retailers (Tenant)
├── Delhi Branch
│   ├── id: 5
│   ├── parent_id: 2 (ABC Retailers)
│   ├── type: "sub_organization"
│   └── Inherits parent's subscription
│
├── Mumbai Branch
│   ├── id: 6
│   ├── parent_id: 2 (ABC Retailers)
│   ├── type: "sub_organization"
│   └── Inherits parent's subscription
│
└── Warehouse Division
    ├── id: 7
    ├── parent_id: 2 (ABC Retailers)
    ├── type: "sub_organization"
    └── Inherits parent's subscription
        │
        └── Warehouse A (Level 4)
            ├── id: 8
            ├── parent_id: 7 (Warehouse Division)
            ├── type: "sub_organization"
            └── Inherits parent's subscription
```

**Key Point**: Sub-organizations can nest infinitely (Level 3, 4, 5... unlimited depth).

---

## 💰 BILLING & SUBSCRIPTION MODEL

### Who Pays Whom?
- **Tenants (Organizations)** pay **Portal Owner**
- Sub-organizations do NOT pay separately (covered by parent's subscription)

### Subscription Structure: HYBRID MODEL

#### Base Subscription (At Tenant Level)
```
ABC Retailers subscribes to: "Pro Plan"
├── Monthly Fee: ₹10,000/month
├── Includes:
│   ├── Modules: Inventory + Accounting
│   ├── Users: 20 users (total across all sub-orgs)
│   ├── Storage: 50 GB
│   ├── Sub-Organizations: 2 branches included
│   └── Features: All Pro features
└── Billing: Recurring monthly invoice
```

#### Add-Ons (Per Sub-Organization)
```
ABC Retailers creates 3rd branch (Warehouse Division)
├── Base plan included: 2 branches
├── Additional branch fee: ₹3,000/month per extra branch
├── Creates 4th branch: Another ₹3,000/month
└── Total bill: ₹10,000 (base) + ₹6,000 (2 extra branches) = ₹16,000/month
```

#### Other Possible Add-Ons:
- Extra users beyond plan limit: ₹500/user/month
- Extra storage beyond plan limit: ₹100/GB/month
- Premium features not in plan: ₹2,000/month
- API access: ₹5,000/month

### Invoice Example
```
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
INVOICE #INV-2025-001
Date: January 31, 2025

From:
Your Company Pvt Ltd (Portal Owner)
GSTIN: 07XXXXX1234X1Z5
Address: Delhi, India

To:
ABC Retailers Pvt Ltd (Tenant)
GSTIN: 07YYYYY5678Y1Z5
Address: Mumbai, India
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

ITEM                                QTY    RATE       AMOUNT
────────────────────────────────────────────────────────
Pro Plan Subscription              1    ₹10,000    ₹10,000
(Inventory + Accounting modules)

Additional Sub-Organizations       2    ₹3,000     ₹6,000
(Warehouse Division, Factory Unit)

Extra Users (5 users)              5    ₹500       ₹2,500

Extra Storage (10 GB)              10   ₹100       ₹1,000
                                              ──────────
                                   Subtotal: ₹19,500
                                   GST 18%:  ₹3,510
                                              ──────────
                                   TOTAL:    ₹23,010
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
Payment Due: February 10, 2025
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
```

---

## 🎛️ CONTROL & CONFIGURATION

### Portal Owner Controls (YOU):
✅ **Full Control Over Everything**

**Tenant Management**:
- Create/delete tenant organizations
- Assign/change subscription plans
- Enable/disable tenant accounts
- Set billing terms and pricing

**Plan & Feature Management**:
- Create subscription plans (Basic, Pro, Enterprise)
- Define what modules each plan includes
- Set limits (users, storage, sub-orgs, etc.)
- Configure pricing and add-on rates

**Module Assignment**:
- Assign modules to tenants (Inventory, Accounting, etc.)
- Tenants CANNOT choose modules themselves
- You decide what each tenant gets access to

**Billing Control**:
- Generate invoices
- Track payments
- Handle overdue accounts
- Apply discounts/penalties

**System Configuration**:
- Global settings and policies
- Platform-wide features
- Security and compliance settings

### Tenant Controls (Organization Admin):
❌ **Limited Configuration Only**

**Organization Settings**:
- Company name, logo, contact details
- GST/tax registration details
- Business addresses
- Notification preferences

**User Management** (within limits):
- Create/invite users (up to subscription limit)
- Assign roles and permissions
- Manage user access
- CANNOT exceed user limit without portal owner approval

**Sub-Organization Management**:
- Create sub-organizations (branches/divisions)
- Structure their internal hierarchy
- Each additional sub-org may incur extra charges

**Feature Configuration** (within enabled modules):
- Configure settings within allowed modules
- Customize workflows
- Set up internal approval processes
- Enable/disable features for sub-orgs (if portal owner allows)

**Data Management**:
- Enter and manage their business data
- Create products, customers, transactions, etc.
- Generate reports from their data

**❌ CANNOT DO**:
- Change subscription plan (must contact portal owner)
- Enable modules not in their plan
- Exceed user/storage/sub-org limits
- Access other tenants' data
- Modify billing/payment settings

---

## 👥 USER LIMIT CALCULATION

### Rule: Users counted TOTAL across entire organization hierarchy

**Example**:
```
ABC Retailers - Pro Plan (20 users limit)
├── ABC Retailers HQ: 5 users
├── Delhi Branch: 8 users
├── Mumbai Branch: 4 users
└── Warehouse: 3 users
                 ─────────
    TOTAL USERS: 20 users ✅ (at limit)
```

**If they try to add 21st user**:
```
❌ ERROR: User limit exceeded

Your current plan allows 20 users.
You currently have 20 users.

To add more users:
- Upgrade to a higher plan, OR
- Purchase additional user add-ons (₹500/user/month), OR
- Remove existing users

Contact support for assistance.
```

**Enforcement**:
- User count checked on EVERY user creation
- Count includes: Active users only (not deleted/suspended)
- Scope: Parent organization + ALL sub-organizations (entire hierarchy)

---

## 🔐 DATA ACCESS & PERMISSIONS (RBAC)

### Principle: Role-Based Access Control with Organizational Scope

### Access Model:

#### **Portal Owner Users**:
```
User: You (Super Admin)
├── Organization: Portal Owner (id: 1)
├── Role: Super Admin
└── Access: EVERYTHING
    ├── All tenants' data
    ├── All system settings
    ├── All billing information
    └── Full control over all operations
```

#### **Tenant Organization Admin**:
```
User: Rajesh Kumar (ABC Retailers CEO)
├── Organization: ABC Retailers (id: 2)
├── Role: Organization Admin
└── Access:
    ├── ✅ ALL data from ABC Retailers HQ
    ├── ✅ ALL data from Delhi Branch
    ├── ✅ ALL data from Mumbai Branch
    ├── ✅ ALL data from Warehouse
    ├── ✅ Aggregated reports across all branches
    ├── ✅ Can create/manage users in all sub-orgs
    ├── ✅ Can configure all sub-orgs
    └── ❌ CANNOT see XYZ Manufacturing's data
```

#### **Branch Manager (Sub-Organization)**:
```
User: Priya Sharma (Delhi Branch Manager)
├── Organization: Delhi Branch (id: 5)
├── Role: Branch Manager
└── Access:
    ├── ✅ ALL data from Delhi Branch ONLY
    ├── ✅ Can manage users in Delhi Branch
    ├── ✅ Can configure Delhi Branch settings
    ├── ❌ CANNOT see Mumbai Branch data
    ├── ❌ CANNOT see Warehouse data
    └── ❌ CANNOT see ABC Retailers HQ data
```

#### **Branch Employee**:
```
User: Amit Singh (Delhi Branch Inventory Staff)
├── Organization: Delhi Branch (id: 5)
├── Role: Inventory Staff
└── Access:
    ├── ✅ Can view/edit inventory in Delhi Branch
    ├── ✅ Can create stock entries
    ├── ❌ CANNOT access accounting module
    ├── ❌ CANNOT see other branches
    └── ❌ CANNOT manage users
```

#### **Multi-Branch User (Finance Manager)**:
```
User: Neha Verma (Finance Manager at HQ)
├── Organization: ABC Retailers HQ (id: 2)
├── Role: Finance Manager
└── Access (via role permissions):
    ├── ✅ View financial data from ALL branches
    ├── ✅ Generate consolidated financial reports
    ├── ✅ Access accounting module in all sub-orgs
    ├── ❌ CANNOT manage inventory
    └── ❌ CANNOT manage users
```

### Data Scoping Rules:

1. **Base Rule**: User sees data from their assigned organization
2. **Hierarchy Rule**: User with proper role can see data from:
   - Their organization
   - All child organizations (sub-orgs below them)
3. **Permission Rule**: Role permissions determine what modules/features they can access
4. **Isolation Rule**: Users NEVER see data from:
   - Other tenant organizations
   - Sibling sub-organizations (unless role allows)
   - Parent organizations (unless role allows)

### Implementation:
```php
// Every database query automatically scoped
$query = Inventory::where('organization_id', auth()->user()->organization_id)
    ->orWhereIn('organization_id', auth()->user()->accessibleOrganizationIds());

// accessibleOrganizationIds() returns:
// - Current org
// - All child orgs (if role has permission)
// - Specific orgs (if role has multi-branch access)
```

---

## 🎨 MODULE/FEATURE ACCESS CONTROL

### Rule: Features assigned at tenant level, inherited by all sub-orgs

### Scenario:
```
ABC Retailers subscribes to "Pro Plan"
├── Enabled Modules:
│   ├── ✅ Inventory Management
│   ├── ✅ Accounting System
│   └── ❌ HR Management (not in plan)
│
└── All sub-organizations inherit these modules:
    ├── Delhi Branch: Can use Inventory + Accounting
    ├── Mumbai Branch: Can use Inventory + Accounting
    └── Warehouse: Can use Inventory + Accounting
```

### Optional: Tenant Admin Can Restrict Features for Sub-Orgs

**If Portal Owner enables this flexibility**:
```
ABC Retailers (has Inventory + Accounting)
├── Delhi Branch:
│   ├── ✅ Inventory: Enabled by tenant admin
│   └── ✅ Accounting: Enabled by tenant admin
│
├── Mumbai Branch:
│   ├── ✅ Inventory: Enabled by tenant admin
│   └── ❌ Accounting: Disabled by tenant admin
│       (Mumbai doesn't need accounting access)
│
└── Warehouse:
    ├── ✅ Inventory: Enabled by tenant admin
    └── ❌ Accounting: Disabled by tenant admin
        (Warehouse only tracks stock, not finances)
```

**Implementation**:
```php
// Check if module is enabled for user's organization
if (auth()->user()->organization->hasModuleEnabled('inventory')) {
    // Show inventory features
}

// hasModuleEnabled() checks:
// 1. Is module in tenant's subscription?
// 2. Is module enabled for this specific sub-org (if admin disabled it)?
```

### Module Assignment Process:

1. **Portal Owner**: Creates plans with modules
   ```
   Basic Plan: Inventory only
   Pro Plan: Inventory + Accounting
   Enterprise Plan: Inventory + Accounting + HR + CRM
   ```

2. **Portal Owner**: Assigns plan to tenant
   ```
   ABC Retailers → Pro Plan
   (Gets Inventory + Accounting)
   ```

3. **Tenant**: CANNOT change modules
   - Must contact portal owner to upgrade/downgrade
   - Portal owner changes plan assignment
   - Billing adjusts automatically

4. **Tenant Admin**: Optionally disable features for sub-orgs
   ```
   Settings > Sub-Organizations > Mumbai Branch
   └── Disable "Accounting Module" for this branch
   ```

---

## 📊 PRACTICAL COMPLETE EXAMPLE

### Setup:

**Portal Owner**: "TechSolutions India Pvt Ltd" (You)
- Organization ID: 1
- Manages the entire platform
- Has 3 tenants currently

**Tenant**: "ABC Retailers Pvt Ltd"
- Organization ID: 2
- Parent ID: 1 (Portal Owner)
- Subscription: Pro Plan
- Monthly Base Fee: ₹10,000

**Pro Plan Includes**:
- Modules: Inventory + Accounting
- Users: 20
- Storage: 50 GB
- Sub-Organizations: 2 included (₹3,000/month for each additional)
- Support: Email support

---

### Month 1: Initial Setup

**Day 1**: ABC Retailers signs up
```
✅ Organization created: ABC Retailers (id: 2)
✅ Plan assigned: Pro Plan
✅ Modules enabled: Inventory, Accounting
✅ Admin user created: <EMAIL>
✅ First invoice generated: ₹10,000 + ₹1,800 GST = ₹11,800
```

**Day 5**: ABC Retailers creates branches
```
✅ Sub-org created: Delhi Branch (id: 5, parent: 2)
✅ Sub-org created: Mumbai Branch (id: 6, parent: 2)

Billing: 2 branches included in base plan → No extra charge
```

**Day 10**: ABC Retailers adds users
```
✅ HQ: 5 users created
✅ Delhi Branch: 7 users created
✅ Mumbai Branch: 6 users created

Total users: 18/20 (within limit ✅)
```

**Day 15**: ABC Retailers creates 3rd branch
```
✅ Sub-org created: Warehouse Division (id: 7, parent: 2)

⚠️ Billing adjustment:
- Base plan includes: 2 branches
- Current branches: 3
- Extra branches: 1 × ₹3,000 = ₹3,000/month

📧 Email sent to tenant:
"You've created an additional branch. Your next invoice will include
an extra charge of ₹3,000/month for this branch."
```

---

### Month 1: Invoice Generation (January 31)

```
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
INVOICE #INV-2025-001
Date: January 31, 2025
Period: January 1 - January 31, 2025

From:
TechSolutions India Pvt Ltd
GSTIN: 07AABCT1234E1Z5
Delhi, India

To:
ABC Retailers Pvt Ltd
GSTIN: 27AACCA5678F1Z1
Mumbai, India
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

DESCRIPTION                      RATE      AMOUNT
────────────────────────────────────────────────
Pro Plan Subscription          ₹10,000   ₹10,000
(Billing Period: Monthly)

Additional Sub-Organization         -         -
(Prorated: 16 days)           ₹1,548    ₹1,548
                                       ─────────
                             Subtotal: ₹11,548
                          CGST @ 9%:   ₹1,039
                          SGST @ 9%:   ₹1,039
                                       ─────────
                        TOTAL AMOUNT: ₹13,626
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

SUBSCRIPTION DETAILS:
• Plan: Pro Plan
• Modules: Inventory Management, Accounting
• Users: 18/20
• Storage: 32 GB/50 GB
• Sub-Organizations: 3 (2 included + 1 additional)

Payment Due: February 10, 2025
Payment Method: Bank Transfer / UPI
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
```

---

### Month 2: Usage Growth

**February 5**: Tries to add 21st user
```
❌ Error: User limit exceeded

Current usage: 20/20 users

Options:
1. Upgrade to Enterprise Plan (50 users) - ₹20,000/month
2. Purchase additional user slots - ₹500/user/month
3. Remove inactive users

Contact support: <EMAIL>
```

**February 6**: Tenant contacts portal owner
```
Tenant request: "We need 5 more users"

Portal Owner action:
✅ Add-on applied: 5 extra users @ ₹500/user/month
✅ Billing updated: +₹2,500/month

📧 Confirmation email sent
```

**February 10**: Storage limit approaching
```
⚠️ Warning email:
"Your storage usage: 48 GB / 50 GB (96%)

You're approaching your storage limit.
Upgrade or purchase additional storage to avoid service disruption."
```

**February 20**: Creates 4th branch
```
✅ Sub-org created: Factory Unit (id: 8, parent: 2)

⚠️ Billing adjustment:
- Extra branches: 2 × ₹3,000 = ₹6,000/month

📧 Email sent with prorated charges for current month
```

---

### Month 2: Invoice (February 28)

```
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
INVOICE #INV-2025-002
Date: February 28, 2025

To: ABC Retailers Pvt Ltd
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

DESCRIPTION                      RATE      AMOUNT
────────────────────────────────────────────────
Pro Plan Subscription          ₹10,000   ₹10,000

Additional Sub-Organizations
(2 branches)                    ₹6,000    ₹6,000

Additional Users
(5 users @ ₹500/user)           ₹2,500    ₹2,500
                                       ─────────
                             Subtotal: ₹18,500
                          CGST @ 9%:   ₹1,665
                          SGST @ 9%:   ₹1,665
                                       ─────────
                        TOTAL AMOUNT: ₹21,830
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

CURRENT USAGE:
• Users: 25/25 (20 plan + 5 add-on)
• Storage: 48 GB/50 GB
• Sub-Organizations: 4

Payment Due: March 10, 2025
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
```

---

### Data Access Example:

**Users at ABC Retailers**:

1. **Rajesh Kumar** (CEO at HQ)
   - Role: Organization Admin
   - Access: ALL branches, ALL modules, ALL data
   ```
   Dashboard shows:
   ├── Consolidated inventory from all 4 locations
   ├── Consolidated financial reports
   ├── User management for entire organization
   └── Settings for all branches
   ```

2. **Priya Sharma** (Delhi Branch Manager)
   - Role: Branch Manager
   - Organization: Delhi Branch (id: 5)
   - Access: Delhi Branch ONLY
   ```
   Dashboard shows:
   ├── Inventory from Delhi Branch only
   ├── Financial data from Delhi Branch only
   ├── Can manage Delhi Branch users
   └── ❌ Cannot see Mumbai/Warehouse/Factory data
   ```

3. **Neha Verma** (Finance Manager at HQ)
   - Role: Finance Manager
   - Organization: HQ (id: 2)
   - Permissions: View financial data across all branches
   ```
   Dashboard shows:
   ├── ✅ Financial reports from ALL 4 branches
   ├── ✅ Accounting module access in all branches
   ├── ❌ Cannot access inventory module
   └── ❌ Cannot manage users
   ```

4. **Amit Singh** (Warehouse Staff)
   - Role: Inventory Staff
   - Organization: Warehouse Division (id: 7)
   - Permissions: Inventory module only in Warehouse
   ```
   Dashboard shows:
   ├── ✅ Warehouse inventory only
   ├── ✅ Can create stock entries
   ├── ❌ Cannot see other branches
   └── ❌ Cannot access accounting
   ```

---

### Feature Configuration Example:

**ABC Retailers Admin** decides:

```
Factory Unit (new branch) → Only needs Inventory

Action:
Settings > Sub-Organizations > Factory Unit
└── Modules Configuration:
    ├── ✅ Inventory Management: Enabled
    └── ❌ Accounting System: Disabled

Result:
- Factory Unit users see only Inventory module
- Accounting features hidden for Factory Unit
- ABC Retailers still pays for Accounting (included in plan)
- Just restricting access for this specific branch
```

---

## ✅ COMPLETE ARCHITECTURE SUMMARY

### Organizational Structure:
1. **Portal Owner** (id: 1) → Root level, owns the platform
2. **Tenant Organizations** (parent_id: 1) → Your customers, have subscriptions
3. **Sub-Organizations** (parent_id: tenant_id) → Tenant's branches/divisions, unlimited nesting

### Billing Model:
- **Base subscription** at tenant level (monthly/yearly)
- **Add-ons** for extras: sub-orgs, users, storage, features
- **Single invoice** to tenant covering all charges
- **Prorated billing** for mid-cycle changes

### Control:
- **Portal Owner**: Full control over tenants, plans, features, billing
- **Tenant**: Limited to org settings, user management, sub-org creation (within limits)
- **Tenants CANNOT**: Choose modules, change plans, exceed limits without approval

### Features:
- Assigned at **tenant level** by portal owner
- **Inherited** by all sub-organizations
- **Optional**: Tenant admin can disable features for specific sub-orgs

### Users:
- **Counted** across entire organization hierarchy (parent + all sub-orgs)
- **Enforced** at tenant's subscription limit
- **Billed** via add-ons if exceeded

### Data Access (RBAC):
- **Users** assigned to specific organization/sub-org
- **Roles** determine permissions (what they can do)
- **Scope** determined by role and organizational hierarchy
- **Higher-level roles** can access child organizations
- **Branch-level users** see only their branch
- **Complete isolation** between different tenants

---

## 🚀 IMPLEMENTATION PRIORITIES

Based on this architecture, the Billing module enhancements should focus on:

1. ✅ **Subscription management** (Base plans + Add-ons)
2. ✅ **Prorated billing** (Mid-cycle changes)
3. ✅ **Usage tracking** (Users, sub-orgs, storage)
4. ✅ **Automated invoicing** (Recurring + Add-ons)
5. ✅ **Limit enforcement** (Users, storage, sub-orgs)
6. ✅ **Indian compliance** (GST, invoicing formats)
7. ✅ **Payment tracking** (Due dates, overdue handling)
8. ✅ **Trial management** (Trial periods, conversions)
9. ✅ **Dunning** (Failed payments, account suspension)
10. ✅ **Analytics** (MRR, churn, usage metrics)

---

**THIS DOCUMENT**: Reference for ALL billing and subscription development.
