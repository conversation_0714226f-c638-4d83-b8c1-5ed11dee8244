<?php

namespace App\Policies;

use App\Modules\Users\Domain\Models\User;
use App\Modules\Organizations\Domain\Models\Organization;

class OrganizationPolicy
{
    public function view(User $user, Organization $organization): bool
    {
        return $user->organization_id === $organization->id;
    }

    public function update(User $user, Organization $organization): bool
    {
        return $user->organization_id === $organization->id;
    }
}
