import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { CCard, CCardBody, CCardHeader, CCol, CRow, CForm, CFormLabel, CFormInput, CFormTextarea, CButton, CSpinner, CAlert } from '@coreui/react'
import { webhooksAPI } from '../../api/webhooks'

const WebhookDetail = () => {
  const { id } = useParams()
  const navigate = useNavigate()
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [formData, setFormData] = useState({ url: '', events: '', active: true })
  const [saving, setSaving] = useState(false)

  useEffect(() => {
    if (id && id !== 'new') {
      loadWebhook()
    } else {
      setLoading(false)
    }
  }, [id])

  const loadWebhook = async () => {
    try {
      setLoading(true)
      const response = await webhooksAPI.getWebhook(id)
      // Backend returns object directly from JsonResource
      setFormData(response.data)
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to load webhook')
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    try {
      setSaving(true)
      if (id && id !== 'new') {
        await webhooksAPI.updateWebhook(id, formData)
      } else {
        await webhooksAPI.createWebhook(formData)
      }
      navigate('/webhooks')
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to save webhook')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return <CRow><CCol xs={12} className="text-center"><CSpinner color="primary" /></CCol></CRow>
  }

  return (
    <CRow>
      <CCol xs={12} md={8}>
        <CCard>
          <CCardHeader>
            <strong>{id && id !== 'new' ? 'Edit Webhook' : 'Add Webhook'}</strong>
          </CCardHeader>
          <CCardBody>
            {error && <CAlert color="danger">{error}</CAlert>}
            <CForm onSubmit={handleSubmit}>
              <div className="mb-3">
                <CFormLabel htmlFor="url">Webhook URL</CFormLabel>
                <CFormInput id="url" name="url" type="url" value={formData.url} onChange={handleChange} required />
              </div>
              <div className="mb-3">
                <CFormLabel htmlFor="events">Events (comma separated)</CFormLabel>
                <CFormTextarea id="events" name="events" value={formData.events} onChange={handleChange} rows="3" />
              </div>
              <div className="mb-3">
                <div className="form-check">
                  <input
                    className="form-check-input"
                    type="checkbox"
                    id="active"
                    name="active"
                    checked={formData.active}
                    onChange={handleChange}
                  />
                  <label className="form-check-label" htmlFor="active">
                    Active
                  </label>
                </div>
              </div>
              <div className="d-grid gap-2">
                <CButton type="submit" color="primary" disabled={saving}>
                  {saving ? <CSpinner size="sm" className="me-2" /> : null}
                  {id && id !== 'new' ? 'Update Webhook' : 'Add Webhook'}
                </CButton>
                <CButton type="button" color="secondary" onClick={() => navigate('/webhooks')}>
                  Cancel
                </CButton>
              </div>
            </CForm>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  )
}

export default WebhookDetail
