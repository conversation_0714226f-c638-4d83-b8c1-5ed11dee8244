<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('resource_usage_logs', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('tenant_organization_id')->comment('Tenant being tracked');
            $table->uuid('subscription_id')->nullable()->comment('Related subscription if applicable');
            $table->enum('resource_type', [
                'users',
                'sub_orgs',
                'storage',
                'hierarchy_depth',
                'api_calls'
            ])->comment('Type of resource');
            $table->bigInteger('usage_value')->comment('Current usage amount');
            $table->bigInteger('limit_value')->comment('Current limit');
            $table->decimal('usage_percentage', 5, 2)->comment('Percentage used');
            $table->timestamp('recorded_at')->comment('When this was recorded');
            $table->boolean('alert_sent')->default(false)->comment('Has alert been sent');
            $table->enum('alert_level', [
                'normal',
                'warning',
                'critical',
                'exceeded'
            ])->default('normal')->comment('Alert severity level');
            
            // Indexes
            $table->index('tenant_organization_id', 'rul_tenant_org_idx');
            $table->index('resource_type', 'rul_resource_type_idx');
            $table->index('recorded_at', 'rul_recorded_at_idx');
            $table->index('alert_level', 'rul_alert_level_idx');
            $table->index(['tenant_organization_id', 'resource_type'], 'rul_tenant_resource_idx');
            $table->index(['tenant_organization_id', 'resource_type', 'recorded_at'], 'rul_tenant_res_date_idx');
            
            // Foreign keys
            $table->foreign('tenant_organization_id')
                  ->references('id')
                  ->on('organizations')
                  ->onDelete('cascade');
            
            $table->foreign('subscription_id')
                  ->references('id')
                  ->on('subscriptions')
                  ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('resource_usage_logs');
    }
};
