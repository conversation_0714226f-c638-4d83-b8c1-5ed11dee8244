import React, { useState, useEffect } from 'react'
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom'
import {
  CCard,
  CCardBody,
  CCardHeader,
  CCol,
  CRow,
  CForm,
  CFormLabel,
  CFormInput,
  CFormSelect,
  CButton,
  CSpinner,
  CAlert,
} from '@coreui/react'
import { usersAPI } from '../../api/users'

const UserDetail = () => {
  const { id } = useParams()
  const navigate = useNavigate()
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    status: 'active',
  })
  const [saving, setSaving] = useState(false)

  useEffect(() => {
    if (id && id !== 'new') {
      loadUser()
    } else {
      setLoading(false)
    }
  }, [id])

  const loadUser = async () => {
    try {
      setLoading(true)
      const response = await usersAPI.getUser(id)
      setUser(response.data.data)
      setFormData(response.data.data)
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to load user')
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    try {
      setSaving(true)
      if (id && id !== 'new') {
        await usersAPI.updateUser(id, formData)
      } else {
        await usersAPI.createUser(formData)
      }
      navigate('/users')
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to save user')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <CRow>
        <CCol xs={12} className="text-center">
          <CSpinner color="primary" />
        </CCol>
      </CRow>
    )
  }

  return (
    <CRow>
      <CCol xs={12} md={8}>
        <CCard>
          <CCardHeader>
            <strong>{id && id !== 'new' ? 'Edit User' : 'Create User'}</strong>
          </CCardHeader>
          <CCardBody>
            {error && <CAlert color="danger">{error}</CAlert>}
            <CForm onSubmit={handleSubmit}>
              <div className="mb-3">
                <CFormLabel htmlFor="name">Name</CFormLabel>
                <CFormInput
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                />
              </div>
              <div className="mb-3">
                <CFormLabel htmlFor="email">Email</CFormLabel>
                <CFormInput
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleChange}
                  required
                  disabled={id && id !== 'new'}
                />
              </div>
              <div className="mb-3">
                <CFormLabel htmlFor="phone">Phone</CFormLabel>
                <CFormInput
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleChange}
                />
              </div>
              <div className="mb-3">
                <CFormLabel htmlFor="status">Status</CFormLabel>
                <CFormSelect
                  id="status"
                  name="status"
                  value={formData.status}
                  onChange={handleChange}
                >
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                  <option value="suspended">Suspended</option>
                </CFormSelect>
              </div>
              <div className="d-grid gap-2">
                <CButton type="submit" color="primary" disabled={saving}>
                  {saving ? <CSpinner size="sm" className="me-2" /> : null}
                  {id && id !== 'new' ? 'Update User' : 'Create User'}
                </CButton>
                <CButton type="button" color="secondary" onClick={() => navigate('/users')}>
                  Cancel
                </CButton>
              </div>
            </CForm>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  )
}

export default UserDetail
