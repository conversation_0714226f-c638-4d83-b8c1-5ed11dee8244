<?php

use Illuminate\Support\Facades\Route;

Route::middleware('auth')->group(function () {
    // Roles & Permissions
Route::get('roles', [\App\Modules\RolesPermissions\Http\Controllers\RoleController::class, 'index'])->middleware('check.permission:roles.view');
Route::post('roles', [\App\Modules\RolesPermissions\Http\Controllers\RoleController::class, 'store'])->middleware('check.permission:roles.create');
    Route::get('roles/{id}', [\App\Modules\RolesPermissions\Http\Controllers\RoleController::class, 'show']);
Route::patch('roles/{id}', [\App\Modules\RolesPermissions\Http\Controllers\RoleController::class, 'update'])->middleware('check.permission:roles.update');
Route::delete('roles/{id}', [\App\Modules\RolesPermissions\Http\Controllers\RoleController::class, 'destroy'])->middleware('check.permission:roles.delete');
Route::post('roles/{id}/permissions/{permissionId}', [\App\Modules\RolesPermissions\Http\Controllers\RoleController::class, 'attachPermission'])->middleware('check.permission:roles.update');
Route::delete('roles/{id}/permissions/{permissionId}', [\App\Modules\RolesPermissions\Http\Controllers\RoleController::class, 'detachPermission'])->middleware('check.permission:roles.update');

Route::get('permissions', [\App\Modules\RolesPermissions\Http\Controllers\PermissionController::class, 'index'])->middleware('check.permission:permissions.view');
Route::post('permissions', [\App\Modules\RolesPermissions\Http\Controllers\PermissionController::class, 'store'])->middleware('check.permission:permissions.create');
    Route::get('permissions/{id}', [\App\Modules\RolesPermissions\Http\Controllers\PermissionController::class, 'show']);
Route::patch('permissions/{id}', [\App\Modules\RolesPermissions\Http\Controllers\PermissionController::class, 'update'])->middleware('check.permission:permissions.update');
Route::delete('permissions/{id}', [\App\Modules\RolesPermissions\Http\Controllers\PermissionController::class, 'destroy'])->middleware('check.permission:permissions.delete');

    // Role Assignments
    Route::get('role-assignments', [\App\Modules\RolesPermissions\Http\Controllers\RoleAssignmentController::class, 'index']);
    Route::post('role-assignments', [\App\Modules\RolesPermissions\Http\Controllers\RoleAssignmentController::class, 'store']);
    Route::get('role-assignments/{id}', [\App\Modules\RolesPermissions\Http\Controllers\RoleAssignmentController::class, 'show']);
    Route::patch('role-assignments/{id}', [\App\Modules\RolesPermissions\Http\Controllers\RoleAssignmentController::class, 'update']);
    Route::delete('role-assignments/{id}', [\App\Modules\RolesPermissions\Http\Controllers\RoleAssignmentController::class, 'destroy']);
});
