<?php

namespace App\Modules\Organizations\Domain\Services;

use App\Modules\Shared\Domain\Services\BaseService;
use App\Modules\Organizations\Domain\Models\Organization;
use App\Modules\Organizations\Domain\Models\OrgUnitClosure;
use App\Modules\Organizations\Domain\Repositories\OrganizationRepository;
use App\Modules\Users\Domain\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Str;

class OrganizationService extends BaseService
{
    public function __construct(private readonly OrganizationRepository $orgs)
    {
    }

    public function createOrganization(array $data): Organization
    {
        return DB::transaction(function () use ($data) {
            if (empty($data['code']) && !empty($data['name'])) {
                $base = strtoupper(preg_replace('/[^A-Z0-9]/', '', Str::slug($data['name'], '')));
                $data['code'] = substr($base, 0, 8) . '-' . strtoupper(Str::random(4));
            }
            $data['type'] = $data['type'] ?? 'organization';
            $data['status'] = $data['status'] ?? 'active';

            $org = $this->orgs->create($data);
            $this->insertClosureForNewNode($org);
            return $org;
        });
    }

    public function updateOrganization(Organization $org, array $data): Organization
    {
        return $this->orgs->update($org, $data);
    }

    public function deleteOrganization(Organization $org): bool
    {
        return $this->orgs->delete($org);
    }

    public function getOrganizationById(string $id): ?Organization
    {
        return $this->orgs->findById($id);
    }

    public function listOrganizations(array $filters = []): Collection
    {
        return $this->orgs->all($filters);
    }

    public function getHierarchy(string $orgId): Collection
    {
        return Organization::query()
            ->join('org_unit_closure as c', 'c.descendant_id', '=', 'organizations.id')
            ->where('c.ancestor_id', $orgId)
            ->orderBy('c.depth')
            ->select('organizations.*', 'c.depth')
            ->get();
    }

    public function getMembers(string $orgId): Collection
    {
        return User::query()->where('organization_id', $orgId)->get();
    }

    public function moveOrganization(Organization $org, ?string $newParentId): void
    {
        DB::transaction(function () use ($org, $newParentId) {
            $org->update(['parent_id' => $newParentId]);
            $this->rebuildClosureForSubtree($org);
        });
    }

    public function maintainClosureTable(): void
    {
        // Rebuild all closure entries for safety
        DB::transaction(function () {
            DB::table('org_unit_closure')->delete();
            $all = Organization::all();
            foreach ($all as $node) {
                $this->insertClosureForNewNode($node);
            }
        });
    }

    public function getAncestors(string $orgId): Collection
    {
        return Organization::query()
            ->join('org_unit_closure as c', 'c.ancestor_id', '=', 'organizations.id')
            ->where('c.descendant_id', $orgId)
            ->orderByDesc('c.depth')
            ->select('organizations.*', 'c.depth')
            ->get();
    }

    public function getDescendants(string $orgId): Collection
    {
        return Organization::query()
            ->join('org_unit_closure as c', 'c.descendant_id', '=', 'organizations.id')
            ->where('c.ancestor_id', $orgId)
            ->orderBy('c.depth')
            ->select('organizations.*', 'c.depth')
            ->get();
    }

    private function insertClosureForNewNode(Organization $org): void
    {
        // Self reference
        OrgUnitClosure::create([
            'ancestor_id' => $org->id,
            'descendant_id' => $org->id,
            'depth' => 0,
        ]);

        if ($org->parent_id) {
            // For each ancestor of parent, add rows
            $ancestors = DB::table('org_unit_closure')
                ->where('descendant_id', $org->parent_id)
                ->get(['ancestor_id', 'depth']);

            foreach ($ancestors as $row) {
                OrgUnitClosure::create([
                    'ancestor_id' => $row->ancestor_id,
                    'descendant_id' => $org->id,
                    'depth' => $row->depth + 1,
                ]);
            }
        }
    }

    private function rebuildClosureForSubtree(Organization $root): void
    {
        // Delete closure entries for subtree
        $descendants = $this->getDescendants($root->id)->pluck('id')->push($root->id);
        DB::table('org_unit_closure')->whereIn('descendant_id', $descendants)->delete();

        // Re-insert for each node in subtree
        $nodes = Organization::whereIn('id', $descendants)->orderBy('level')->get();
        foreach ($nodes as $node) {
            $this->insertClosureForNewNode($node);
        }
    }
}
