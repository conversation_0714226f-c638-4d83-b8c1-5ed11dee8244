import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { CCard, CCardBody, CCardHeader, CCol, CRow, CButton, CTable, CTableBody, CTableDataCell, CTableHead, CTableHeaderCell, CTableRow, CSpinner, CAlert, CBadge } from '@coreui/react'
import CIcon from '@coreui/icons-react'
import { cilPlus, cilPencil, cilTrash, cilWarning } from '@coreui/icons'
import { billingAPI } from '../../api/billing'

const Payments = () => {
  const navigate = useNavigate()
  const [payments, setPayments] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => { loadPayments() }, [])

  const loadPayments = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await billingAPI.getPayments()
      // Backend returns array directly from JsonResource::collection()
      setPayments(response.data || [])
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to load payments')
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async (id) => {
    if (window.confirm('Are you sure?')) {
      try {
        await billingAPI.deletePayment(id)
        setPayments(payments.filter((p) => p.id !== id))
      } catch (err) {
        setError(err.response?.data?.message || 'Failed to delete')
      }
    }
  }

  return (
    <CRow>
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardHeader>
            <div className="d-flex justify-content-between align-items-center">
              <strong>Payments</strong>
              <CButton color="primary" size="sm" onClick={() => navigate('/payments/new')}>
                <CIcon icon={cilPlus} className="me-2" />
                Record Payment
              </CButton>
            </div>
          </CCardHeader>
          <CCardBody>
            {error && <CAlert color="danger">{error}</CAlert>}
            {loading ? (
              <div className="text-center"><CSpinner color="primary" /></div>
            ) : (
              <CTable hover responsive>
                <CTableHead>
                  <CTableRow>
                    <CTableHeaderCell scope="col">#</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Amount</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Method</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Status</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Date</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Actions</CTableHeaderCell>
                  </CTableRow>
                </CTableHead>
                <CTableBody>
                  {payments.length === 0 ? (
                    <CTableRow>
                      <CTableDataCell colSpan="6" className="text-center text-muted py-5">
                        <CIcon icon={cilWarning} size="xl" className="mb-2" />
                        <div>No payments found</div>
                        <small className="text-muted">Record a payment to get started</small>
                      </CTableDataCell>
                    </CTableRow>
                  ) : (
                    payments.map((payment, idx) => (
                      <CTableRow key={payment.id}>
                        <CTableDataCell>{idx + 1}</CTableDataCell>
                        <CTableDataCell>${payment.amount || 0}</CTableDataCell>
                        <CTableDataCell>{payment.payment_method || '-'}</CTableDataCell>
                        <CTableDataCell><CBadge color={payment.status === 'completed' ? 'success' : 'warning'}>{payment.status}</CBadge></CTableDataCell>
                        <CTableDataCell>{payment.created_at || '-'}</CTableDataCell>
                        <CTableDataCell>
                          <CButton color="info" size="sm" className="me-2" onClick={() => navigate(`/payments/${payment.id}`)}><CIcon icon={cilPencil} /></CButton>
                          <CButton color="danger" size="sm" onClick={() => handleDelete(payment.id)}><CIcon icon={cilTrash} /></CButton>
                        </CTableDataCell>
                      </CTableRow>
                    ))
                  )}
                </CTableBody>
              </CTable>
            )}
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  )
}

export default Payments
