<?php

namespace App\Modules\Billing\Domain\Services;

use App\Modules\Shared\Domain\Services\BaseService;
use App\Modules\Billing\Domain\Models\Invoice;
use App\Modules\Billing\Domain\Models\Subscription;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class InvoiceService extends BaseService
{
    public function generateInvoice(array $data): Invoice
    {
        return DB::transaction(function () use ($data) {
            $subscription = isset($data['subscription_id']) ? Subscription::find($data['subscription_id']) : null;

            $lineItems = $data['line_items'] ?? [];
            $subtotal = $data['subtotal'] ?? $this->calculateSubtotal($lineItems);
            $discount = $data['discount'] ?? 0;
            $tax = $data['tax'] ?? 0;
            $total = $data['total'] ?? (($subtotal - $discount) + $tax);

            $invoice = Invoice::create([
                'organization_id' => $data['organization_id'] ?? $this->getCurrentOrganizationId(),
                'subscription_id' => $subscription?->id,
                'invoice_number' => $data['invoice_number'] ?? $this->generateInvoiceNumber(),
                'status' => 'sent',
                'subtotal' => $subtotal,
                'tax' => $tax,
                'discount' => $discount,
                'total' => $total,
                'currency' => $data['currency'] ?? config('app.currency', 'USD'),
                'issue_date' => $data['issue_date'] ?? now()->toDateString(),
                'due_date' => $data['due_date'] ?? now()->addDays(7)->toDateString(),
                'sent_at' => now(),
                'line_items' => $lineItems,
                'notes' => $data['notes'] ?? null,
                'metadata' => $data['metadata'] ?? [],
            ]);

            return $invoice;
        });
    }

    public function sendInvoice(Invoice $invoice): void
    {
        // Stub: email sending
        $invoice->update(['status' => 'sent', 'sent_at' => now()]);
    }

    public function generatePdf(Invoice $invoice): string
    {
        // Stub: return path or base64 of a generated PDF
        return 'PDF_GENERATED_PLACEHOLDER';
    }

    public function getInvoiceById(string $id): ?Invoice
    {
        return Invoice::find($id);
    }

    public function listInvoices(array $filters = []): Collection
    {
        $q = Invoice::query()->where('organization_id', $filters['organization_id'] ?? $this->getCurrentOrganizationId());
        if (isset($filters['status'])) {
            $q->whereIn('status', (array) $filters['status']);
        }
        return $q->get();
    }

    public function markAsPaid(Invoice $invoice): Invoice
    {
        $invoice->update(['status' => 'paid', 'paid_at' => now()]);
        return $invoice;
    }

    private function generateInvoiceNumber(): string
    {
        return 'INV-' . now()->format('Ymd-His') . '-' . substr(uniqid('', true), -6);
    }

    private function calculateSubtotal(array $lineItems): float
    {
        $sum = 0.0;
        foreach ($lineItems as $item) {
            if (isset($item['amount'])) {
                $sum += (float) $item['amount'];
            } else {
                $qty = isset($item['quantity']) ? (float) $item['quantity'] : 1.0;
                $unit = isset($item['unit_price']) ? (float) $item['unit_price'] : 0.0;
                $sum += $qty * $unit;
            }
        }
        return round($sum, 2);
    }
}
