<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('organizations', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('parent_id')->nullable()->comment('Parent organization for hierarchy');
            $table->string('type', 50)->comment('Organization type: portal, tenant, organization, branch, department, team');
            $table->string('name')->comment('Organization name');
            $table->string('code', 50)->unique()->comment('Unique organization code');
            $table->text('description')->nullable();
            $table->string('email')->nullable();
            $table->string('phone', 20)->nullable();
            $table->text('address')->nullable();
            $table->string('city', 100)->nullable();
            $table->string('state', 100)->nullable();
            $table->string('country', 100)->nullable();
            $table->string('postal_code', 20)->nullable();
            $table->string('timezone', 50)->default('UTC');
            $table->string('currency', 10)->default('USD');
            $table->string('language', 10)->default('en');
            
            // GST and Billing Information
            $table->string('gstin', 15)->nullable()->comment('GST Identification Number');
            $table->string('legal_name')->nullable()->comment('Legal business name for invoicing');
            $table->text('billing_address')->nullable();
            $table->string('billing_city', 100)->nullable();
            $table->string('billing_state', 100)->nullable();
            $table->string('billing_postal_code', 20)->nullable();
            $table->string('billing_country', 100)->nullable()->default('India');
            
            $table->enum('status', ['active', 'inactive', 'suspended', 'pending_approval'])->default('active');
            $table->uuid('approval_request_id')->nullable()->comment('Link to approval request if pending');
            $table->json('settings')->nullable()->comment('Organization-specific settings');
            $table->timestamps();
            $table->softDeletes();
            
            // Indexes
            $table->index('parent_id');
            $table->index('type');
            $table->index('status');
            $table->index('approval_request_id');
            $table->index('gstin');
            $table->index('created_at');
            
            // Foreign keys
            $table->foreign('parent_id')
                  ->references('id')
                  ->on('organizations')
                  ->onDelete('cascade');
            
            // Note: approval_request_id foreign key will be added after approval_requests table exists
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('organizations');
    }
};
