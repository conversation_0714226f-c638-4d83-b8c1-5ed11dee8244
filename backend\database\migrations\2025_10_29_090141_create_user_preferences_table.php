<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_preferences', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('organization_id');
            $table->uuid('user_id');
            $table->string('key')->comment('Preference key');
            $table->text('value')->nullable()->comment('Preference value');
            $table->string('type', 50)->default('string')->comment('string, boolean, integer, json');
            $table->timestamps();
            
            $table->unique(['organization_id', 'user_id', 'key']);
            
            $table->index('organization_id');
            $table->index('user_id');
            $table->index('key');
            
            $table->foreign('organization_id')->references('id')->on('organizations')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_preferences');
    }
};
