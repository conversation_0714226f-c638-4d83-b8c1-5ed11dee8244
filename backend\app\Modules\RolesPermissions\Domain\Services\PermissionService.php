<?php

namespace App\Modules\RolesPermissions\Domain\Services;

use App\Modules\Shared\Domain\Services\BaseService;
use App\Modules\RolesPermissions\Domain\Models\Permission;
use App\Modules\RolesPermissions\Domain\Repositories\PermissionRepository;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Str;

class PermissionService extends BaseService
{
    public function __construct(private readonly PermissionRepository $permissions)
    {
    }

    public function createPermission(array $data): Permission
    {
        if (empty($data['organization_id'])) {
            $data['organization_id'] = $this->getCurrentOrganizationId();
        }
        if (empty($data['module']) && !empty($data['slug'])) {
            $data['module'] = Str::before($data['slug'], '.');
        }
        return $this->permissions->create($data);
    }

    public function updatePermission(Permission $permission, array $data): Permission
    {
        if (empty($data['module']) && !empty($data['slug'])) {
            $data['module'] = Str::before($data['slug'], '.');
        }
        return $this->permissions->update($permission, $data);
    }

    public function deletePermission(Permission $permission): bool
    {
        return $this->permissions->delete($permission);
    }

    public function getPermissionById(string $id): ?Permission
    {
        return $this->permissions->findById($id);
    }

    public function listPermissions(array $filters = []): Collection
    {
        return $this->permissions->all($filters);
    }
}
