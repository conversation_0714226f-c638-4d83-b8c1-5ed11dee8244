<?php

namespace App\Modules\Billing\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateSubscriptionRequest extends FormRequest
{
    public function authorize(): bool { return true; }
    public function rules(): array
    {
        return [
            'plan_id' => ['sometimes','uuid'],
            'billing_period' => ['sometimes','in:monthly,yearly'],
            'auto_renew' => ['sometimes','boolean'],
            'status' => ['sometimes','in:active,cancelled,expired,suspended'],
        ];
    }
}
