<?php

namespace App\Modules\Billing\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Billing\Domain\Services\SubscriptionService;
use App\Modules\Billing\Domain\Models\Plan;
use App\Modules\Billing\Http\Requests\CreateSubscriptionRequest;
use App\Modules\Billing\Http\Requests\UpdateSubscriptionRequest;
use App\Modules\Billing\Http\Resources\SubscriptionResource;
use Illuminate\Http\Request;

class SubscriptionController extends Controller
{
    public function __construct(private readonly SubscriptionService $service) {}

    public function index(Request $request)
    {
        return response()->json(SubscriptionResource::collection($this->service->listSubscriptions()));
    }

    public function store(CreateSubscriptionRequest $request)
    {
        $sub = $this->service->createSubscription($request->validated());
        return response()->json(new SubscriptionResource($sub), 201);
    }

    public function show(string $id)
    {
        $sub = $this->service->getSubscriptionById($id);
        abort_if(!$sub, 404);
        return response()->json(new SubscriptionResource($sub));
    }

    public function update(UpdateSubscriptionRequest $request, string $id)
    {
        $sub = $this->service->getSubscriptionById($id);
        abort_if(!$sub, 404);
        if ($request->filled('plan_id')) {
            $plan = Plan::findOrFail($request->input('plan_id'));
            $sub = $this->service->upgradeSubscription($sub, $plan);
        }
        return response()->json(new SubscriptionResource($sub));
    }

    public function destroy(string $id)
    {
        $sub = $this->service->getSubscriptionById($id);
        abort_if(!$sub, 404);
        $this->service->cancelSubscription($sub);
        return response()->json(['cancelled' => true]);
    }

    public function upgrade(string $id, Request $request)
    {
        $sub = $this->service->getSubscriptionById($id);
        abort_if(!$sub, 404);
        $plan = Plan::findOrFail($request->input('plan_id'));
        return response()->json(new SubscriptionResource($this->service->upgradeSubscription($sub, $plan)));
    }

    public function downgrade(string $id, Request $request)
    {
        $sub = $this->service->getSubscriptionById($id);
        abort_if(!$sub, 404);
        $plan = Plan::findOrFail($request->input('plan_id'));
        return response()->json(new SubscriptionResource($this->service->downgradeSubscription($sub, $plan)));
    }

    public function cancel(string $id)
    {
        $sub = $this->service->getSubscriptionById($id);
        abort_if(!$sub, 404);
        return response()->json(new SubscriptionResource($this->service->cancelSubscription($sub)));
    }
}
