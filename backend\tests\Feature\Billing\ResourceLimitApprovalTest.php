<?php

namespace Tests\Feature\Billing;

use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Modules\Organizations\Domain\Models\Organization;
use App\Modules\Users\Domain\Models\User;
use App\Modules\Billing\Domain\Models\Plan;
use App\Modules\Billing\Domain\Models\Subscription;
use App\Modules\Billing\Domain\Models\SubscriptionAddOn;
use App\Modules\Approvals\Domain\Models\ApprovalRequest;

class ResourceLimitApprovalTest extends TestCase
{
    use RefreshDatabase;

    protected Organization $portalOwner;
    protected Organization $tenant;
    protected User $portalOwnerUser;
    protected User $tenantUser;
    protected Plan $plan;
    protected Subscription $subscription;

    protected function setUp(): void
    {
        parent::setUp();

        // Create portal owner organization
        $this->portalOwner = Organization::factory()->create([
            'type' => 'portal_owner',
            'name' => 'Portal Owner',
            'parent_id' => null,
        ]);

        // Create portal owner user
        $this->portalOwnerUser = User::factory()->create([
            'organization_id' => $this->portalOwner->id,
            'status' => 'active',
        ]);

        // Create plan with limits
        $this->plan = Plan::factory()->create([
            'name' => 'Test Plan',
            'slug' => 'test',
            'price' => 10000,
            'user_limit' => 5,
            'sub_org_limit' => 2,
            'storage_limit' => 10, // GB
            'hierarchy_depth_limit' => 3,
            'api_calls_limit' => 1000,
            'modules' => ['inventory', 'accounting'],
        ]);

        // Create tenant organization
        $this->tenant = Organization::factory()->create([
            'type' => 'tenant',
            'name' => 'Test Tenant',
            'parent_id' => $this->portalOwner->id,
        ]);

        // Create subscription for tenant
        $this->subscription = Subscription::factory()->create([
            'organization_id' => $this->tenant->id,
            'plan_id' => $this->plan->id,
            'status' => 'active',
            'price' => $this->plan->price,
            'user_count' => 0,
            'sub_org_count' => 0,
            'storage_used' => 0,
            'hierarchy_depth' => 1,
        ]);

        // Create tenant user
        $this->tenantUser = User::factory()->create([
            'organization_id' => $this->tenant->id,
            'status' => 'active',
        ]);
    }
    #[Test]
    public function plan_has_resource_limit_fields()
    {
        $this->assertNotNull($this->plan->user_limit);
        $this->assertNotNull($this->plan->sub_org_limit);
        $this->assertNotNull($this->plan->storage_limit);
        $this->assertNotNull($this->plan->hierarchy_depth_limit);
        $this->assertNotNull($this->plan->api_calls_limit);
        $this->assertEquals(['inventory', 'accounting'], $this->plan->modules);
    }
    #[Test]
    public function subscription_has_usage_tracking_fields()
    {
        $this->assertEquals(0, $this->subscription->user_count);
        $this->assertEquals(0, $this->subscription->sub_org_count);
        $this->assertEquals(0, $this->subscription->storage_used);
        $this->assertEquals(1, $this->subscription->hierarchy_depth);
    }
    #[Test]
    public function subscription_can_check_if_user_limit_exceeded()
    {
        $this->subscription->update(['user_count' => 6]);
        $this->assertTrue($this->subscription->isUserLimitExceeded());

        $this->subscription->update(['user_count' => 5]);
        $this->assertFalse($this->subscription->isUserLimitExceeded());
    }
    #[Test]
    public function subscription_can_check_if_sub_org_limit_exceeded()
    {
        $this->subscription->update(['sub_org_count' => 3]);
        $this->assertTrue($this->subscription->isSubOrgLimitExceeded());

        $this->subscription->update(['sub_org_count' => 2]);
        $this->assertFalse($this->subscription->isSubOrgLimitExceeded());
    }
    #[Test]
    public function subscription_can_check_if_storage_limit_exceeded()
    {
        // 15 GB in bytes
        $this->subscription->update(['storage_used' => 15 * 1024 * 1024 * 1024]);
        $this->assertTrue($this->subscription->isStorageLimitExceeded());

        // 5 GB in bytes
        $this->subscription->update(['storage_used' => 5 * 1024 * 1024 * 1024]);
        $this->assertFalse($this->subscription->isStorageLimitExceeded());
    }
    #[Test]
    public function subscription_can_calculate_usage_percentages()
    {
        $this->subscription->update(['user_count' => 3]);
        $this->assertEquals(60, $this->subscription->getUserUsagePercentage());

        $this->subscription->update(['sub_org_count' => 1]);
        $this->assertEquals(50, $this->subscription->getSubOrgUsagePercentage());
    }
    #[Test]
    public function organization_can_be_pending_approval()
    {
        $subOrg = Organization::factory()->create([
            'parent_id' => $this->tenant->id,
            'type' => 'sub_organization',
            'status' => 'pending_approval',
        ]);

        $this->assertTrue($subOrg->isPendingApproval());
        $this->assertFalse($this->tenant->isPendingApproval());
    }
    #[Test]
    public function user_can_be_pending_approval()
    {
        $approvalRequest = ApprovalRequest::factory()->create([
            'organization_id' => $this->tenant->id,
            'requester_id' => $this->tenantUser->id,
            'type' => 'resource_limit',
            'resource_type' => 'user',
            'status' => 'pending',
        ]);

        $pendingUser = User::factory()->create([
            'organization_id' => $this->tenant->id,
            'status' => 'pending',
            'approval_request_id' => $approvalRequest->id,
        ]);

        $this->assertTrue($pendingUser->isPendingApproval());
        $this->assertFalse($this->tenantUser->isPendingApproval());
    }
    #[Test]
    public function organization_has_tenant_and_portal_owner_helpers()
    {
        $this->assertTrue($this->portalOwner->isPortalOwner());
        $this->assertFalse($this->portalOwner->isTenant());

        $this->assertTrue($this->tenant->isTenant());
        $this->assertFalse($this->tenant->isPortalOwner());

        $subOrg = Organization::factory()->create([
            'parent_id' => $this->tenant->id,
            'type' => 'sub_organization',
        ]);

        $this->assertTrue($subOrg->isSubOrganization());
        $this->assertFalse($subOrg->isTenant());
    }
    #[Test]
    public function plan_can_check_if_module_is_enabled()
    {
        $this->assertTrue($this->plan->hasModule('inventory'));
        $this->assertTrue($this->plan->hasModule('accounting'));
        $this->assertFalse($this->plan->hasModule('hr'));
    }
    #[Test]
    public function plan_has_add_on_pricing_methods()
    {
        $this->assertEquals(500, $this->plan->getUserAddOnPrice());
        $this->assertEquals(100, $this->plan->getStorageAddOnPrice());
        $this->assertEquals(2000, $this->plan->getHierarchyLevelAddOnPrice());
        $this->assertEquals(3000, $this->plan->getModuleAddOnPrice());
        $this->assertEquals(5000, $this->plan->getApiCallsAddOnPrice());
        $this->assertIsInt($this->plan->getSubOrgAddOnPrice());
    }
    #[Test]
    public function subscription_add_on_can_be_created()
    {
        $addOn = SubscriptionAddOn::create([
            'subscription_id' => $this->subscription->id,
            'add_on_type' => 'user',
            'quantity' => 5,
            'unit_price' => 500,
            'total_price' => 2500,
            'starts_at' => now(),
            'ends_at' => null,
        ]);

        $this->assertDatabaseHas('subscription_add_ons', [
            'subscription_id' => $this->subscription->id,
            'add_on_type' => 'user',
            'quantity' => 5,
        ]);

        $this->assertTrue($addOn->isActive());
    }
    #[Test]
    public function subscription_add_on_is_not_active_if_not_started()
    {
        $addOn = SubscriptionAddOn::create([
            'subscription_id' => $this->subscription->id,
            'add_on_type' => 'storage',
            'quantity' => 10,
            'unit_price' => 100,
            'total_price' => 1000,
            'starts_at' => now()->addDays(5),
            'ends_at' => null,
        ]);

        $this->assertFalse($addOn->isActive());
    }
    #[Test]
    public function subscription_add_on_is_not_active_if_ended()
    {
        $addOn = SubscriptionAddOn::create([
            'subscription_id' => $this->subscription->id,
            'add_on_type' => 'storage',
            'quantity' => 10,
            'unit_price' => 100,
            'total_price' => 1000,
            'starts_at' => now()->subDays(10),
            'ends_at' => now()->subDay(),
        ]);

        $this->assertFalse($addOn->isActive());
    }
    #[Test]
    public function subscription_can_calculate_total_monthly_price_with_add_ons()
    {
        // Create active add-ons
        SubscriptionAddOn::create([
            'subscription_id' => $this->subscription->id,
            'add_on_type' => 'user',
            'quantity' => 5,
            'unit_price' => 500,
            'total_price' => 2500,
            'starts_at' => now(),
            'ends_at' => null,
        ]);

        SubscriptionAddOn::create([
            'subscription_id' => $this->subscription->id,
            'add_on_type' => 'storage',
            'quantity' => 10,
            'unit_price' => 100,
            'total_price' => 1000,
            'starts_at' => now(),
            'ends_at' => null,
        ]);

        // Base: 10000 + Users: 2500 + Storage: 1000 = 13500
        $this->assertEquals(13500, $this->subscription->getTotalMonthlyPrice());
    }
    #[Test]
    public function subscription_add_on_has_display_name_and_description()
    {
        $addOn = SubscriptionAddOn::create([
            'subscription_id' => $this->subscription->id,
            'add_on_type' => 'user',
            'quantity' => 5,
            'unit_price' => 500,
            'total_price' => 2500,
            'starts_at' => now(),
        ]);

        $this->assertEquals('Additional Users', $addOn->getAddOnName());
        $this->assertEquals('5 extra users', $addOn->getAddOnDescription());
    }
    #[Test]
    public function subscription_add_on_can_calculate_proration()
    {
        $addOn = SubscriptionAddOn::create([
            'subscription_id' => $this->subscription->id,
            'add_on_type' => 'user',
            'quantity' => 5,
            'unit_price' => 500,
            'total_price' => 3000,
            'starts_at' => now(),
        ]);

        $daysInMonth = now()->daysInMonth;
        $expectedProration = (3000 / $daysInMonth) * 15;
        $this->assertEquals($expectedProration, $addOn->calculateProrated(15));
    }
    #[Test]
    public function approval_request_has_resource_approval_fields()
    {
        $approvalRequest = ApprovalRequest::create([
            'organization_id' => $this->tenant->id,
            'requester_id' => $this->tenantUser->id,
            'type' => 'resource_limit',
            'resource_type' => 'user',
            'status' => 'pending',
            'urgency' => 'normal',
            'current_limit' => 5,
            'requested_limit' => 10,
            'billing_impact' => 2500,
            'submitted_at' => now(),
        ]);

        $this->assertDatabaseHas('approval_requests', [
            'resource_type' => 'user',
            'urgency' => 'normal',
            'current_limit' => 5,
            'requested_limit' => 10,
        ]);
    }
    #[Test]
    public function organization_has_gst_fields()
    {
        $this->tenant->update([
            'gstin' => '27AABCT1234E1Z1',
            'legal_name' => 'Test Tenant Pvt Ltd',
            'billing_address' => '123 Test Street',
            'billing_city' => 'Mumbai',
            'billing_state' => 'Maharashtra',
            'billing_postal_code' => '400001',
            'billing_country' => 'India',
        ]);

        $this->assertDatabaseHas('organizations', [
            'id' => $this->tenant->id,
            'gstin' => '27AABCT1234E1Z1',
            'legal_name' => 'Test Tenant Pvt Ltd',
        ]);
    }
    #[Test]
    public function unlimited_limits_return_false_for_exceeded_checks()
    {
        // Create plan with unlimited limits
        $unlimitedPlan = Plan::factory()->create([
            'name' => 'Unlimited Plan',
            'slug' => 'unlimited',
            'price' => 50000,
            'user_limit' => 0, // 0 means unlimited
            'sub_org_limit' => 0,
            'storage_limit' => 0,
        ]);

        $unlimitedSubscription = Subscription::factory()->create([
            'organization_id' => $this->tenant->id,
            'plan_id' => $unlimitedPlan->id,
            'user_count' => 1000,
            'sub_org_count' => 100,
            'storage_used' => 500 * 1024 * 1024 * 1024, // 500 GB
        ]);

        $this->assertFalse($unlimitedSubscription->isUserLimitExceeded());
        $this->assertFalse($unlimitedSubscription->isSubOrgLimitExceeded());
        $this->assertFalse($unlimitedSubscription->isStorageLimitExceeded());
    }
}
