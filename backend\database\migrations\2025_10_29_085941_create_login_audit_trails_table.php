<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('login_audit_trails', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('organization_id');
            $table->uuid('user_id');
            $table->string('event', 50)->comment('login, logout, failed_login, lockout');
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            $table->string('location')->nullable()->comment('Geo location');
            $table->json('metadata')->nullable()->comment('Additional audit data');
            $table->boolean('is_successful')->default(true);
            $table->text('failure_reason')->nullable();
            $table->timestamps();
            
            $table->index('organization_id');
            $table->index('user_id');
            $table->index('event');
            $table->index('is_successful');
            $table->index('created_at');
            
            $table->foreign('organization_id')->references('id')->on('organizations')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('login_audit_trails');
    }
};
