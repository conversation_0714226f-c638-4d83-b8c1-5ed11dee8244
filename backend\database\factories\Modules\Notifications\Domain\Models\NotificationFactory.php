<?php

namespace Database\Factories\Modules\Notifications\Domain\Models;

use App\Modules\Notifications\Domain\Models\Notification;
use App\Modules\Users\Domain\Models\User;
use App\Modules\Organizations\Domain\Models\Organization;
use Illuminate\Database\Eloquent\Factories\Factory;

class NotificationFactory extends Factory
{
    protected $model = Notification::class;

    public function definition(): array
    {
        return [
            'id' => $this->faker->uuid(),
            'organization_id' => Organization::factory(),
            'user_id' => User::factory(),
            'type' => $this->faker->randomElement(['email', 'sms', 'in_app']),
            'channel' => $this->faker->randomElement(['email', 'sms', 'in_app']),
            'subject' => $this->faker->sentence(),
            'message' => $this->faker->paragraph(),
            'data' => [],
            'status' => 'sent',
            'sent_at' => now(),
            'read_at' => null,
            'error_message' => null,
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}
