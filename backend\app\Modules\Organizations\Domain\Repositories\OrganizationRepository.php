<?php

namespace App\Modules\Organizations\Domain\Repositories;

use App\Modules\Shared\Domain\Repositories\BaseRepository;
use App\Modules\Organizations\Domain\Models\Organization;
use Illuminate\Database\Eloquent\Builder;

class OrganizationRepository extends BaseRepository
{
    protected string $model = Organization::class;

    /**
     * Organizations are global and not scoped by organization_id.
     */
    protected function getBaseQuery(): Builder
    {
        return Organization::query();
    }
}
