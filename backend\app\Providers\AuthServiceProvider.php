<?php

namespace App\Providers;

use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Auth;
use App\Modules\Organizations\Domain\Models\Organization;
use App\Modules\RolesPermissions\Domain\Models\Role;
use App\Modules\RolesPermissions\Domain\Models\Permission;
use App\Policies\OrganizationPolicy;
use App\Policies\RolePolicy;
use App\Policies\PermissionPolicy;
use App\Modules\Users\Domain\Models\UserSession;
use App\Modules\Users\Domain\Models\User;

class AuthServiceProvider extends ServiceProvider
{
    protected $policies = [
        Organization::class => OrganizationPolicy::class,
        Role::class => RolePolicy::class,
        Permission::class => PermissionPolicy::class,
    ];

    public function boot(): void
    {
        $this->registerPolicies();

        Auth::viaRequest('api', function ($request) {
            $token = $request->bearerToken();
            if (!$token) {
                return null;
            }

            $session = UserSession::query()
                ->where('token', $token)
                ->where('is_active', true)
                ->where('expires_at', '>', now())
                ->first();

            return $session ? User::find($session->user_id) : null;
        });
    }
}
