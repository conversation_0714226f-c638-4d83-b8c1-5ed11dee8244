<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('approval_requests', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('organization_id');
            $table->uuid('requester_id');
            $table->string('type', 50)->comment('expense, leave, purchase, custom, resource_limit');
            $table->enum('resource_type', ['user', 'sub_org', 'storage', 'hierarchy_level', 'module', 'plan_change'])
                  ->nullable()->comment('Type of resource approval');
            $table->string('reference_type')->nullable()->comment('Polymorphic reference type');
            $table->uuid('reference_id')->nullable()->comment('Polymorphic reference ID');
            $table->string('status', 50)->default('pending')->comment('pending, approved, rejected, escalated');
            $table->enum('urgency', ['low', 'normal', 'high', 'urgent'])->default('normal');
            $table->text('description')->nullable();
            $table->decimal('amount', 10, 2)->nullable()->comment('For financial approvals');
            $table->integer('current_limit')->nullable()->comment('Current resource limit');
            $table->integer('requested_limit')->nullable()->comment('Requested resource limit');
            $table->decimal('billing_impact', 10, 2)->nullable()->comment('Monthly cost impact');
            $table->json('data')->nullable()->comment('Additional approval data');
            $table->integer('current_step')->default(1);
            $table->timestamp('submitted_at');
            $table->timestamp('completed_at')->nullable();
            $table->uuid('reviewed_by')->nullable()->comment('User who reviewed');
            $table->text('review_notes')->nullable();
            $table->timestamps();
            $table->softDeletes();
            
            $table->index('organization_id');
            $table->index('requester_id');
            $table->index('type');
            $table->index('resource_type');
            $table->index('status');
            $table->index('urgency');
            $table->index(['status', 'urgency']);
            $table->index(['reference_type', 'reference_id']);
            
            $table->foreign('organization_id')->references('id')->on('organizations')->onDelete('cascade');
            $table->foreign('requester_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('reviewed_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('approval_requests');
    }
};
