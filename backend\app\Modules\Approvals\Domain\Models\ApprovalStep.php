<?php

namespace App\Modules\Approvals\Domain\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Modules\Shared\Domain\Traits\HasUUID;
use App\Modules\Shared\Domain\Traits\HasOrganizationId;
use App\Modules\Organizations\Domain\Models\Organization;
use App\Modules\Users\Domain\Models\User;

class ApprovalStep extends Model
{
    use HasFactory, HasUUID, HasOrganizationId;

    protected $table = 'approval_steps';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'organization_id',
        'approval_request_id',
        'approver_id',
        'step_number',
        'status',
        'approval_type',
        'required_count',
        'approved_by',
        'approved_at',
        'rejected_by',
        'rejected_at',
        'comment',
        'actioned_at',
    ];

    protected $casts = [
        'step_number' => 'integer',
        'required_count' => 'integer',
        'approved_at' => 'datetime',
        'rejected_at' => 'datetime',
        'actioned_at' => 'datetime',
    ];

    // Relationships
    public function organization()
    {
        return $this->belongsTo(Organization::class, 'organization_id');
    }

    public function approvalRequest()
    {
        return $this->belongsTo(ApprovalRequest::class, 'approval_request_id');
    }

    public function approver()
    {
        return $this->belongsTo(User::class, 'approver_id');
    }

    // Helper methods
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    public function isApproved(): bool
    {
        return $this->status === 'approved';
    }

    public function isRejected(): bool
    {
        return $this->status === 'rejected';
    }
}
