<?php

namespace App\Modules\Users\Domain\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Modules\Shared\Domain\Traits\HasUUID;
use App\Modules\Shared\Domain\Traits\HasOrganizationId;

class OtpToken extends Model
{
    use HasFactory, HasUUID, HasOrganizationId;

    protected $table = 'otp_tokens';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'organization_id',
        'user_id',
        'token',
        'type',
        'purpose',
        'is_used',
        'expires_at',
        'used_at',
    ];

    protected $casts = [
        'is_used' => 'boolean',
        'expires_at' => 'datetime',
        'used_at' => 'datetime',
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
