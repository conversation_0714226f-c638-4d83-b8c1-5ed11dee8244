<?php

namespace Tests\Feature\Users;

use Tests\TestCase;
use App\Modules\Users\Domain\Models\User;
use App\Modules\Users\Domain\Services\AuthenticationService;
use App\Modules\Organizations\Domain\Models\Organization;
use Illuminate\Support\Facades\Hash;

class AuthenticationTest extends TestCase
{
    private AuthenticationService $authService;
    private Organization $organization;

    protected function setUp(): void
    {
        parent::setUp();
        $this->authService = app(AuthenticationService::class);
        $this->organization = Organization::factory()->create();
    }

    public function test_user_can_register()
    {
        $data = [
            'organization_id' => $this->organization->id,
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'password' => 'SecurePassword123!',
        ];

        $user = $this->authService->register($data);

        $this->assertNotNull($user);
        $this->assertEquals('<EMAIL>', $user->email);
        $this->assertEquals($this->organization->id, $user->organization_id);
        $this->assertTrue(Hash::check('SecurePassword123!', $user->password));
    }

    public function test_user_can_login()
    {
        $user = User::factory()->create([
            'organization_id' => $this->organization->id,
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
        ]);

        $result = $this->authService->login('<EMAIL>', 'password123');

        $this->assertNotNull($result['user']);
        $this->assertNotNull($result['token']);
        $this->assertNotNull($result['expires_at']);
        $this->assertEquals($user->id, $result['user']->id);
    }

    public function test_login_fails_with_invalid_credentials()
    {
        $this->expectException(\Illuminate\Validation\ValidationException::class);

        $this->authService->login('<EMAIL>', 'wrongpassword');
    }

    public function test_user_can_logout()
    {
        $user = User::factory()->create(['organization_id' => $this->organization->id]);
        $result = $this->authService->login($user->email, 'password');

        $this->authService->logout($user, $result['token']);

        $session = \App\Modules\Users\Domain\Models\UserSession::where('token', $result['token'])->first();
        $this->assertFalse($session->is_active);
    }

    public function test_user_can_request_otp()
    {
        $user = User::factory()->create(['organization_id' => $this->organization->id]);

        $otp = $this->authService->requestOtp($user, 'login');

        $this->assertNotNull($otp);
        $this->assertEquals($user->id, $otp->user_id);
        $this->assertFalse($otp->is_used);
    }

    public function test_user_can_verify_otp()
    {
        $user = User::factory()->create(['organization_id' => $this->organization->id]);
        $otp = $this->authService->requestOtp($user, 'login');

        $verified = $this->authService->verifyOtp($user, $otp->token, 'login');

        $this->assertTrue($verified);
        $otp->refresh();
        $this->assertTrue($otp->is_used);
    }

    public function test_invalid_otp_fails_verification()
    {
        $user = User::factory()->create(['organization_id' => $this->organization->id]);
        $this->authService->requestOtp($user, 'login');

        $verified = $this->authService->verifyOtp($user, '000000', 'login');

        $this->assertFalse($verified);
    }

    public function test_user_can_reset_password()
    {
        $user = User::factory()->create(['organization_id' => $this->organization->id]);
        $resetToken = $this->authService->resetPasswordRequest($user);

        $result = $this->authService->resetPassword($resetToken->token, 'NewPassword123!');

        $this->assertTrue($result);
        $user->refresh();
        $this->assertTrue(Hash::check('NewPassword123!', $user->password));
    }

    public function test_user_can_setup_mfa()
    {
        $user = User::factory()->create(['organization_id' => $this->organization->id]);
        $secret = 'JBSWY3DPEBLW64TMMQ======';

        $result = $this->authService->setupMFA($user, $secret);

        $this->assertTrue($result);
        $user->refresh();
        $this->assertTrue($user->mfa_enabled);
        $this->assertEquals($secret, $user->mfa_secret);
    }

    public function test_user_can_verify_mfa()
    {
        $user = User::factory()->create([
            'organization_id' => $this->organization->id,
            'mfa_enabled' => true,
            'mfa_secret' => 'JBSWY3DPEBLW64TMMQ======',
        ]);

        $result = $this->authService->verifyMFA($user, '123456');

        $this->assertTrue($result);
    }
}
