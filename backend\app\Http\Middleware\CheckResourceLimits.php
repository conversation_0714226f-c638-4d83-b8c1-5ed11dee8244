<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Modules\Billing\Application\Services\ResourceLimitService;
use Illuminate\Support\Facades\Auth;

class CheckResourceLimits
{
    public function __construct(
        protected ResourceLimitService $resourceLimitService
    ) {}

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, string $resourceType): Response
    {
        $user = Auth::user();
        
        if (!$user) {
            return response()->json([
                'error' => 'Unauthorized',
                'message' => 'User not authenticated.',
            ], 401);
        }

        $organization = $user->organization;
        
        if (!$organization) {
            return response()->json([
                'error' => 'No Organization',
                'message' => 'User does not belong to an organization.',
            ], 400);
        }

        // Check the appropriate limit based on resource type
        $check = match($resourceType) {
            'user' => $this->resourceLimitService->canCreateUser($organization),
            'sub_org' => $this->resourceLimitService->canCreateSubOrganization($organization),
            'storage' => $this->checkStorageLimit($request, $organization),
            'module' => $this->checkModuleAccess($request, $organization),
            default => ['allowed' => true],
        };

        if (!$check['allowed']) {
            return response()->json([
                'error' => 'Resource Limit Exceeded',
                'message' => $check['message'] ?? 'Resource limit has been reached.',
                'limit' => $check['limit'] ?? null,
                'current' => $check['current'] ?? null,
                'requires_approval' => $check['requires_approval'] ?? false,
            ], 403);
        }

        return $next($request);
    }

    /**
     * Check storage limit for file upload
     */
    protected function checkStorageLimit(Request $request, $organization): array
    {
        $file = $request->file('file');
        
        if (!$file) {
            return ['allowed' => true];
        }

        $fileSize = $file->getSize();
        
        return $this->resourceLimitService->canUseStorage($organization, $fileSize);
    }

    /**
     * Check module access
     */
    protected function checkModuleAccess(Request $request, $organization): array
    {
        $moduleName = $request->route('module') ?? $request->input('module');
        
        if (!$moduleName) {
            return ['allowed' => true];
        }

        return $this->resourceLimitService->canAccessModule($organization, $moduleName);
    }
}
