{"name": "@coreui/coreui-free-react-admin-template", "version": "5.5.0", "description": "CoreUI Free React Admin Template", "homepage": ".", "bugs": {"url": "https://github.com/coreui/coreui-free-react-admin-template/issues"}, "repository": {"type": "git", "url": "**************:coreui/coreui-free-react-admin-template.git"}, "license": "MIT", "author": "The CoreUI Team (https://github.com/orgs/coreui/people)", "scripts": {"build": "vite build", "lint": "eslint", "serve": "vite preview", "start": "vite"}, "dependencies": {"@coreui/chartjs": "^4.1.0", "@coreui/coreui": "^5.4.1", "@coreui/icons": "^3.0.1", "@coreui/icons-react": "^2.3.0", "@coreui/react": "^5.7.1", "@coreui/react-chartjs": "^3.0.0", "@coreui/utils": "^2.0.2", "@popperjs/core": "^2.11.8", "@reduxjs/toolkit": "^1.9.7", "axios": "^1.6.2", "chart.js": "^4.5.0", "classnames": "^2.5.1", "core-js": "^3.45.0", "prop-types": "^15.8.1", "react": "^19.1.1", "react-calendar": "^6.0.0", "react-dom": "^19.1.1", "react-redux": "^9.2.0", "react-router-dom": "^7.7.1", "redux": "5.0.1", "simplebar-react": "^3.3.2"}, "devDependencies": {"@vitejs/plugin-react": "^5.0.0", "autoprefixer": "^10.4.21", "eslint": "^9.32.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "globals": "^16.3.0", "postcss": "^8.5.6", "prettier": "3.6.2", "sass": "^1.90.0", "vite": "^7.1.0"}}