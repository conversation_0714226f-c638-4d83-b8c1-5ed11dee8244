# FOUNDATION DEVELOPMENT RULES

**Date**: January 29, 2025  
**Status**: MANDATORY - ALL DEVELOPMENT MUST FOLLOW THESE RULES

---

## 🎯 CRITICAL RULE: PORTAL OWNER vs TENANT

### **This is a FOUNDATION/BASE for building different tenant-specific portals**

**NOT a multi-tenant SaaS platform**

---

## ✅ WHAT THIS IS

This codebase is a **reusable foundation** that YOU (portal owner) will use to:
1. Build DIFFERENT portals for DIFFERENT clients/tenants
2. Each tenant gets their OWN customized portal
3. Each portal will be DEPLOYED SEPARATELY for that tenant

**Example Use Cases**:
- Build a **Hospital Management Portal** for Hospital A
- Build an **Inventory Management Portal** for Retailer B  
- Build a **School Management Portal** for School C
- Build an **ERP Portal** for Manufacturing Company D

Each portal is **separate, independent, customized** for that specific tenant.

---

## ❌ WHAT THIS IS NOT

This is **NOT** a multi-tenant SaaS where:
- One deployment serves multiple tenants
- Tenants share the same database with org_id scoping
- You bill tenants for subscriptions
- Tenants log into YOUR portal

---

## 📋 DEVELOPMENT RULES

### Rule 1: ALL Code Must Be Portal-Owner Specific
```
✅ CORRECT: Features the portal owner needs to manage the portal
❌ WRONG: Features for tenants to use the portal
```

### Rule 2: Organizations Table = Organizational Hierarchy
The `organizations` table is for:
- **YOUR** company's organizational structure
- Internal departments, teams, branches
- NOT for external customers/tenants
- NOT for multi-tenancy

### Rule 3: Users Table = Portal Users
The `users` table is for:
- **YOUR** portal's users (admins, employees, etc.)
- People who manage/use this specific portal
- NOT for end-customers
- NOT for multi-tenant users

### Rule 4: Billing Module = NOT APPLICABLE
Since each tenant gets their own portal:
- NO subscription management
- NO recurring billing
- NO SaaS pricing
- You charge tenants via PROJECT CONTRACTS, not subscriptions

**Action**: REMOVE the entire Billing module from foundation.

### Rule 5: Approvals Module = Internal Workflows Only
- Approval workflows for **YOUR** internal processes
- NOT for tenant-specific approval flows
- Generic enough to work for any business

### Rule 6: Notifications Module = Generic Infrastructure
- Email/SMS/Push notification infrastructure
- NOT tenant-specific notification templates
- Reusable for any portal type

### Rule 7: Settings Module = Portal Configuration
- Settings for **THIS** portal instance
- Configuration options
- NOT tenant preferences

### Rule 8: Audit Module = Activity Logging
- Log what happens in **THIS** portal
- Security and compliance
- Generic for any portal type

### Rule 9: Reports Module = Either Remove or Make Generic
- If kept: Generic reporting FRAMEWORK only
- NO hardcoded business-specific reports
- OR: Remove completely (tenant decides what reports they need)

---

## 🏗️ CORRECT ARCHITECTURE

### Foundation Provides:
1. ✅ Authentication & Authorization (RBAC)
2. ✅ User Management
3. ✅ Role & Permission System
4. ✅ Organizational Hierarchy (for internal structure)
5. ✅ Audit Trails
6. ✅ Notification Infrastructure
7. ✅ Settings Management
8. ✅ Generic Approval Workflows
9. ✅ API Framework
10. ✅ Security Features

### Tenant Implements (Later):
- Their specific domain models (Patients, Products, Students, etc.)
- Their specific business logic
- Their specific reports
- Their specific workflows
- Their specific UI/UX

---

## 🔄 CORRECT BILLING UNDERSTANDING

Since you asked about "billing for portal owner":

### Scenario: You Build a Hospital Portal for Hospital A

**What YOU need**:
- Track PROJECT costs
- Track development hours
- Invoice Hospital A for the project
- Track payments from Hospital A

**What you DON'T need**:
- Subscription management (they own the portal forever)
- Recurring billing (one-time project or annual maintenance)
- Plan upgrades/downgrades
- SaaS metrics (MRR, churn, etc.)

### If Billing Module Needed:
It would be for:
- **Project Management** (tracking client projects)
- **Time Tracking** (developer hours)
- **Project Invoicing** (one-time or milestone-based)
- **Client Payments** (tracking project payments)

**NOT for SaaS subscriptions!**

---

## ✅ MODULES TO KEEP (Foundation)

| Module | Keep? | Reason |
|--------|-------|--------|
| Shared | ✅ YES | Base classes, traits - universal |
| Users | ✅ YES | User management - universal |
| Organizations | ✅ YES | Internal org structure - universal |
| RolesPermissions | ✅ YES | RBAC - universal |
| Audit | ✅ YES | Activity logs - universal |
| Settings | ✅ YES | Configuration - universal |
| Notifications | ✅ YES | Notification infrastructure - universal |
| Approvals | ✅ YES | Generic workflows - universal |
| Integration | ✅ YES | Webhook infrastructure - universal |
| **Billing** | ❌ **REMOVE** | SaaS-specific, not applicable |
| **Reports** | ❌ **REMOVE** | Tenant decides what reports they need |

---

## 🚀 IMMEDIATE ACTIONS

1. ✅ Keep GST service (for invoicing clients for projects)
2. ❌ Remove Subscription/Plan models
3. ❌ Remove recurring billing logic
4. ❌ Remove SaaS-specific features
5. ✅ Focus on generic, reusable infrastructure

---

## 📝 DEVELOPMENT CHECKLIST

Before implementing ANY feature, ask:

1. ❓ Is this needed by the **portal owner** (you)?
2. ❓ Is this generic enough for **any** business type?
3. ❓ Does this assume a specific business domain?
4. ❓ Is this infrastructure or business logic?

**If it's business logic → DON'T implement in foundation**  
**If it's infrastructure → Implement as generic/reusable**

---

## ✅ SUMMARY

**YOU are building**: A reusable foundation for creating custom portals  
**NOT building**: A multi-tenant SaaS platform  
**Organizations table**: YOUR internal structure, not tenants  
**Billing module**: NOT NEEDED (remove completely)  
**Focus**: Generic, reusable infrastructure ONLY  

---

**This Rule Document**: Keep this as reference for ALL development decisions.
