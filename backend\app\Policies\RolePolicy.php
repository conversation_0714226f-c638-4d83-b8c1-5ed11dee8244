<?php

namespace App\Policies;

use App\Modules\Users\Domain\Models\User;
use App\Modules\RolesPermissions\Domain\Models\Role;
use App\Modules\RolesPermissions\Domain\Services\AuthorizationService;

class RolePolicy
{
    public function __construct(private readonly AuthorizationService $authz) {}

    public function view(User $user, Role $role): bool
    {
        return $user->organization_id === $role->organization_id;
    }

    public function create(User $user): bool
    {
        return $this->authz->userHasPermission($user, 'roles.create');
    }

    public function update(User $user, Role $role): bool
    {
        return $user->organization_id === $role->organization_id && $this->authz->userHasPermission($user, 'roles.update');
    }

    public function delete(User $user, Role $role): bool
    {
        return $user->organization_id === $role->organization_id && $this->authz->userHasPermission($user, 'roles.delete');
    }
}
