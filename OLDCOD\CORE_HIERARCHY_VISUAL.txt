╔═══════════════════════════════════════════════════════════════════════════════╗
║                  ERP SYSTEM - CORE HIERARCHY STRUCTURE                        ║
║                                                                               ║
║              3 MANDATORY LEVELS WITH COMPLETE VISUALIZATION                  ║
╚═══════════════════════════════════════════════════════════════════════════════╝


═══════════════════════════════════════════════════════════════════════════════
                           LEVEL 1: PORTAL
═══════════════════════════════════════════════════════════════════════════════

                              ┌─────────────────────┐
                              │   SYSTEM PORTAL     │
                              │   (Mandatory)       │
                              │                     │
                              │  Scope: Global      │
                              │  parent_id: N/A     │
                              │  Type: portal       │
                              └──────────┬──────────┘
                                         │
                    ┌────────────────────┼────────────────────┐
                    │                    │                    │
                    ▼                    ▼                    ▼

            ┌──────────────┐    ┌──────────────┐    ┌──────────────┐
            │    ROLE 1    │    │    ROLE 2    │    │    ROLE 3    │
            │              │    │              │    │              │
            │ System Admin │    │ System Audit │    │ System Supp. │
            └──────────────┘    └──────────────┘    └──────────────┘
                    │                    │                    │
                    ▼                    ▼                    ▼

            ┌──────────────┐    ┌──────────────┐    ┌──────────────┐
            │   USER 1     │    │   USER 2     │    │   USER 3     │
            │              │    │              │    │              │
            │ admin@sys    │    │ auditor@sys  │    │ support@sys  │
            │              │    │              │    │              │
            │ Permissions: │    │ Permissions: │    │ Permissions: │
            │ • All system │    │ • View all   │    │ • Support    │
            │ • Manage all │    │ • Audit logs │    │ • User help  │
            └──────────────┘    └──────────────┘    └──────────────┘


═══════════════════════════════════════════════════════════════════════════════
                    LEVEL 2: ORGANIZATION (Root)
═══════════════════════════════════════════════════════════════════════════════

                              ┌─────────────────────┐
                              │   ORGANIZATION      │
                              │   (Mandatory)       │
                              │                     │
                              │ Name: Acme Corp     │
                              │ Code: ACME-001      │
                              │ Scope: org-001      │
                              │ parent_id: NULL     │
                              │ Type: organization  │
                              │ Status: active      │
                              └──────────┬──────────┘
                                         │
                    ┌────────────────────┼────────────────────┐
                    │                    │                    │
                    ▼                    ▼                    ▼

            ┌──────────────┐    ┌──────────────┐    ┌──────────────┐
            │    ROLE 1    │    │    ROLE 2    │    │    ROLE 3    │
            │              │    │              │    │              │
            │ Org Admin    │    │ Org Manager  │    │ Org Member   │
            │              │    │              │    │              │
            │ Scope:       │    │ Scope:       │    │ Scope:       │
            │ organization │    │ organization │    │ organization │
            │ scope_id:    │    │ scope_id:    │    │ scope_id:    │
            │ NULL         │    │ NULL         │    │ NULL         │
            └──────────────┘    └──────────────┘    └──────────────┘
                    │                    │                    │
        ┌───────────┼─────────┐  ┌──────┴──────┐  ┌─────────┼──────────┐
        │           │         │  │             │  │         │          │
        ▼           ▼         ▼  ▼             ▼  ▼         ▼          ▼

    ┌────────┐ ┌────────┐ ┌────────┐ ┌────────┐ ┌────────┐ ┌────────┐ ┌────────┐
    │ USER 1 │ │ USER 2 │ │ USER 3 │ │ USER 4 │ │ USER 5 │ │ USER 6 │ │ USER 7 │
    │        │ │        │ │        │ │        │ │        │ │        │ │        │
    │ alice@ │ │ bob@   │ │charlie │ │ diana@ │ │ eve@   │ │frank@  │ │grace@  │
    │ acme   │ │ acme   │ │ @acme  │ │ acme   │ │ acme   │ │ acme   │ │ acme   │
    │        │ │        │ │        │ │        │ │        │ │        │ │        │
    │ Role:  │ │ Role:  │ │ Role:  │ │ Role:  │ │ Role:  │ │ Role:  │ │ Role:  │
    │ Admin  │ │Manager │ │ Member │ │ Member │ │ Admin  │ │Manager │ │ Member │
    │        │ │        │ │        │ │        │ │        │ │        │ │        │
    │ org_id:│ │org_id: │ │org_id: │ │org_id: │ │org_id: │ │org_id: │ │org_id: │
    │ 001    │ │ 001    │ │ 001    │ │ 001    │ │ 001    │ │ 001    │ │ 001    │
    │        │ │        │ │        │ │        │ │        │ │        │ │        │
    │ Perms: │ │ Perms: │ │ Perms: │ │ Perms: │ │ Perms: │ │ Perms: │ │ Perms: │
    │ • All  │ │ • Mgmt │ │ • View │ │ • View │ │ • All  │ │ • Mgmt │ │ • View │
    │ • Mgmt │ │ • Team │ │ • Ops  │ │ • Ops  │ │ • Mgmt │ │ • Team │ │ • Ops  │
    │ • Ops  │ │ • Ops  │ │        │ │        │ │ • Ops  │ │ • Ops  │ │        │
    └────────┘ └────────┘ └────────┘ └────────┘ └────────┘ └────────┘ └────────┘

    ⚠️  ALL USERS HAVE organization_id = org-001 (MANDATORY - NOT NULL)


═══════════════════════════════════════════════════════════════════════════════
                    LEVEL 3: SUB-ORGANIZATION (Optional)
═══════════════════════════════════════════════════════════════════════════════

    ┌──────────────────────────────────────────────────────────────────────────┐
    │                                                                          │
    │  Parent Organization: Acme Corporation (org-001)                        │
    │                                                                          │
    │  ┌────────────────────────────────────────────────────────────────────┐ │
    │  │                                                                    │ │
    │  │  SUB-ORGANIZATION 1 (Optional)                                    │ │
    │  │  ┌────────────────────────────────────────────────────────────┐  │ │
    │  │  │ Name: Acme Sales Division                                 │  │ │
    │  │  │ Code: ACME-SALES-001                                      │  │ │
    │  │  │ parent_id: org-001 (references parent)                    │  │ │
    │  │  │ organization_id: org-001 (inherited from parent)          │  │ │
    │  │  │ Type: sub-organization                                    │  │ │
    │  │  │ Status: active                                            │  │ │
    │  │  │                                                            │  │ │
    │  │  │  ┌──────────────┐  ┌──────────────┐  ┌──────────────┐   │  │ │
    │  │  │  │    ROLE 1    │  │    ROLE 2    │  │    ROLE 3    │   │  │ │
    │  │  │  │              │  │              │  │              │   │  │ │
    │  │  │  │ Sub-Org Admin│  │ Sub-Org Mgr  │  │ Sub-Org Memb │   │  │ │
    │  │  │  │              │  │              │  │              │   │  │ │
    │  │  │  │ Scope:       │  │ Scope:       │  │ Scope:       │   │  │ │
    │  │  │  │ organization │  │ organization │  │ organization │   │  │ │
    │  │  │  │ scope_id:    │  │ scope_id:    │  │ scope_id:    │   │  │ │
    │  │  │  │ org-002      │  │ org-002      │  │ org-002      │   │  │ │
    │  │  │  └──────────────┘  └──────────────┘  └──────────────┘   │  │ │
    │  │  │         │                   │                   │        │  │ │
    │  │  │         ▼                   ▼                   ▼        │  │ │
    │  │  │                                                          │  │ │
    │  │  │  ┌──────────────┐  ┌──────────────┐  ┌──────────────┐  │  │ │
    │  │  │  │   USER 8     │  │   USER 9     │  │  USER 10     │  │  │ │
    │  │  │  │              │  │              │  │              │  │  │ │
    │  │  │  │ <EMAIL> │  │<EMAIL>│  │<EMAIL>│  │  │ │
    │  │  │  │              │  │              │  │              │  │  │ │
    │  │  │  │ Role:        │  │ Role:        │  │ Role:        │  │  │ │
    │  │  │  │ Sub-Org Admin│  │ Sub-Org Mgr  │  │ Sub-Org Memb │  │  │ │
    │  │  │  │              │  │              │  │              │  │  │ │
    │  │  │  │ org_id:      │  │ org_id:      │  │ org_id:      │  │  │ │
    │  │  │  │ 001          │  │ 001          │  │ 001          │  │  │ │
    │  │  │  │              │  │              │  │              │  │  │ │
    │  │  │  │ Perms:       │  │ Perms:       │  │ Perms:       │  │  │ │
    │  │  │  │ • Sub-Org    │  │ • Sub-Org    │  │ • Sub-Org    │  │  │ │
    │  │  │  │   Admin      │  │   Mgmt       │  │   Basic      │  │  │ │
    │  │  │  │ • Manage     │  │ • Manage     │  │ • View       │  │  │ │
    │  │  │  │   Sub-Org    │  │   Teams      │  │ • Ops        │  │  │ │
    │  │  │  └──────────────┘  └──────────────┘  └──────────────┘  │  │ │
    │  │  │                                                          │  │ │
    │  │  └────────────────────────────────────────────────────────┘  │ │
    │  │                                                                │ │
    │  ├────────────────────────────────────────────────────────────────┤ │
    │  │                                                                │ │
    │  │  SUB-ORGANIZATION 2 (Optional)                                │ │
    │  │  ┌────────────────────────────────────────────────────────┐  │ │
    │  │  │ Name: Acme Tech Division                              │  │ │
    │  │  │ Code: ACME-TECH-001                                   │  │ │
    │  │  │ parent_id: org-001                                    │  │ │
    │  │  │ organization_id: org-001 (inherited)                  │  │ │
    │  │  │                                                        │  │ │
    │  │  │  ┌──────────────┐  ┌──────────────┐  ┌──────────────┐ │  │ │
    │  │  │  │    ROLE 1    │  │    ROLE 2    │  │    ROLE 3    │ │  │ │
    │  │  │  │ Sub-Org Admin│  │ Sub-Org Mgr  │  │ Sub-Org Memb │ │  │ │
    │  │  │  │              │  │              │  │              │ │  │ │
    │  │  │  │ scope_id:    │  │ scope_id:    │  │ scope_id:    │ │  │ │
    │  │  │  │ org-003      │  │ org-003      │  │ org-003      │ │  │ │
    │  │  │  └──────────────┘  └──────────────┘  └──────────────┘ │  │ │
    │  │  │         │                   │                   │      │  │ │
    │  │  │         ▼                   ▼                   ▼      │  │ │
    │  │  │                                                        │  │ │
    │  │  │  ┌──────────────┐  ┌──────────────┐  ┌──────────────┐ │  │ │
    │  │  │  │  USER 11     │  │  USER 12     │  │  USER 13     │ │  │ │
    │  │  │  │              │  │              │  │              │ │  │ │
    │  │  │  │<EMAIL>│  │ <EMAIL>│  │<EMAIL> │ │  │ │
    │  │  │  │              │  │              │  │              │ │  │ │
    │  │  │  │ Role:        │  │ Role:        │  │ Role:        │ │  │ │
    │  │  │  │ Sub-Org Admin│  │ Sub-Org Mgr  │  │ Sub-Org Memb │ │  │ │
    │  │  │  │              │  │              │  │              │ │  │ │
    │  │  │  │ org_id:      │  │ org_id:      │  │ org_id:      │ │  │ │
    │  │  │  │ 001          │  │ 001          │  │ 001          │ │  │ │
    │  │  │  └──────────────┘  └──────────────┘  └──────────────┘ │  │ │
    │  │  │                                                        │  │ │
    │  │  └────────────────────────────────────────────────────────┘  │ │
    │  │                                                                │ │
    │  ├────────────────────────────────────────────────────────────────┤ │
    │  │                                                                │ │
    │  │  SUB-ORGANIZATION 3 (Optional)                                │ │
    │  │  ┌────────────────────────────────────────────────────────┐  │ │
    │  │  │ Name: Acme HR Division                                │  │ │
    │  │  │ Code: ACME-HR-001                                     │  │ │
    │  │  │ parent_id: org-001                                    │  │ │
    │  │  │ organization_id: org-001 (inherited)                  │  │ │
    │  │  │                                                        │  │ │
    │  │  │  ┌──────────────┐  ┌──────────────┐  ┌──────────────┐ │  │ │
    │  │  │  │    ROLE 1    │  │    ROLE 2    │  │    ROLE 3    │ │  │ │
    │  │  │  │ Sub-Org Admin│  │ Sub-Org Mgr  │  │ Sub-Org Memb │ │  │ │
    │  │  │  │              │  │              │  │              │ │  │ │
    │  │  │  │ scope_id:    │  │ scope_id:    │  │ scope_id:    │ │  │ │
    │  │  │  │ org-004      │  │ org-004      │  │ org-004      │ │  │ │
    │  │  │  └──────────────┘  └──────────────┘  └──────────────┘ │  │ │
    │  │  │         │                   │                   │      │  │ │
    │  │  │         ▼                   ▼                   ▼      │  │ │
    │  │  │                                                        │  │ │
    │  │  │  ┌──────────────┐  ┌──────────────┐  ┌──────────────┐ │  │ │
    │  │  │  │  USER 14     │  │  USER 15     │  │  USER 16     │ │  │ │
    │  │  │  │              │  │              │  │              │ │  │ │
    │  │  │  │ <EMAIL>│  │<EMAIL>│  │ <EMAIL>│ │  │ │
    │  │  │  │              │  │              │  │              │ │  │ │
    │  │  │  │ Role:        │  │ Role:        │  │ Role:        │ │  │ │
    │  │  │  │ Sub-Org Admin│  │ Sub-Org Mgr  │  │ Sub-Org Memb │ │  │ │
    │  │  │  │              │  │              │  │              │ │  │ │
    │  │  │  │ org_id:      │  │ org_id:      │  │ org_id:      │ │  │ │
    │  │  │  │ 001          │  │ 001          │  │ 001          │ │  │ │
    │  │  │  └──────────────┘  └──────────────┘  └──────────────┘ │  │ │
    │  │  │                                                        │  │ │
    │  │  └────────────────────────────────────────────────────────┘  │ │
    │  │                                                                │ │
    │  └────────────────────────────────────────────────────────────────┘ │
    │                                                                      │
    │  ⚠️  IMPORTANT NOTES:                                              │
    │  • All users still have organization_id = org-001 (parent)       │
    │  • Sub-organizations are OPTIONAL (can be created as needed)     │
    │  • Sub-organizations can be nested infinitely                    │
    │  • Each sub-org has its own roles and members                    │
    │                                                                      │
    └──────────────────────────────────────────────────────────────────────┘


═══════════════════════════════════════════════════════════════════════════════
                         COMPLETE HIERARCHY TREE
═══════════════════════════════════════════════════════════════════════════════

                              SYSTEM PORTAL
                         (Mandatory - Level 1)
                                  │
                                  │
                                  ▼
                          ┌─────────────────┐
                          │   ORGANIZATION  │
                          │  (Mandatory)    │
                          │  Acme Corp      │
                          │  org-001        │
                          └────────┬────────┘
                                   │
                    ┌──────────────┼──────────────┐
                    │              │              │
                    ▼              ▼              ▼
            ┌──────────────┐ ┌──────────────┐ ┌──────────────┐
            │ SUB-ORG 1    │ │ SUB-ORG 2    │ │ SUB-ORG 3    │
            │ (Optional)   │ │ (Optional)   │ │ (Optional)   │
            │ Sales Div    │ │ Tech Div     │ │ HR Div       │
            │ org-002      │ │ org-003      │ │ org-004      │
            └──────────────┘ └──────────────┘ └──────────────┘


═══════════════════════════════════════════════════════════════════════════════
                      ROLE HIERARCHY BY LEVEL
═══════════════════════════════════════════════════════════════════════════════

LEVEL 1: PORTAL ROLES
├─ System Administrator
│  └─ Permissions: Full system access
│  └─ Scope: Global
│  └─ Can: Manage all organizations, system settings
│
├─ System Auditor
│  └─ Permissions: View-only access
│  └─ Scope: Global
│  └─ Can: View all data, audit logs
│
└─ System Support
   └─ Permissions: Support operations
   └─ Scope: Global
   └─ Can: User support, limited system access


LEVEL 2: ORGANIZATION ROLES
├─ Organization Administrator
│  └─ Permissions: Full organization access
│  └─ Scope: organization (scope_id = NULL)
│  └─ Can: Manage org, users, roles, settings
│
├─ Organization Manager
│  └─ Permissions: Management access
│  └─ Scope: organization (scope_id = NULL)
│  └─ Can: Manage teams, projects, resources
│
└─ Organization Member
   └─ Permissions: Basic access
   └─ Scope: organization (scope_id = NULL)
   └─ Can: Execute tasks, view resources


LEVEL 3: SUB-ORGANIZATION ROLES
├─ Sub-Organization Administrator
│  └─ Permissions: Sub-org admin access
│  └─ Scope: organization (scope_id = sub-org-id)
│  └─ Can: Manage sub-org, users, settings
│
├─ Sub-Organization Manager
│  └─ Permissions: Sub-org management
│  └─ Scope: organization (scope_id = sub-org-id)
│  └─ Can: Manage teams, projects
│
└─ Sub-Organization Member
   └─ Permissions: Sub-org basic access
   └─ Scope: organization (scope_id = sub-org-id)
   └─ Can: Execute sub-org tasks


═══════════════════════════════════════════════════════════════════════════════
                    USER DISTRIBUTION BY LEVEL
═══════════════════════════════════════════════════════════════════════════════

LEVEL 1: PORTAL (3 Users)
├─ <EMAIL> (System Administrator)
├─ <EMAIL> (System Auditor)
└─ <EMAIL> (System Support)


LEVEL 2: ORGANIZATION (7 Users)
├─ <EMAIL> (Organization Administrator)
├─ <EMAIL> (Organization Manager)
├─ <EMAIL> (Organization Member)
├─ <EMAIL> (Organization Member)
├─ <EMAIL> (Organization Member)
├─ <EMAIL> (Organization Member)
└─ <EMAIL> (Organization Member)


LEVEL 3: SUB-ORGANIZATION (9 Users)
├─ <EMAIL> (Sales Division Admin)
├─ <EMAIL> (Sales Division Manager)
├─ <EMAIL> (Sales Division Member)
├─ <EMAIL> (Tech Division Admin)
├─ <EMAIL> (Tech Division Manager)
├─ <EMAIL> (Tech Division Member)
├─ <EMAIL> (HR Division Admin)
├─ <EMAIL> (HR Division Manager)
└─ <EMAIL> (HR Division Member)

⚠️  NOTE: Some users appear in multiple levels with different roles


═══════════════════════════════════════════════════════════════════════════════
                      DATABASE CONSTRAINTS
═══════════════════════════════════════════════════════════════════════════════

MANDATORY CONSTRAINTS (LEVEL 2 - ORGANIZATION):
┌─────────────────────────────────────────────────────────────┐
│ users.organization_id: NOT NULL                            │
│ └─ Every user MUST belong to an organization              │
│ └─ Cannot create user without organization_id             │
│ └─ Enforces mandatory organization level                  │
└─────────────────────────────────────────────────────────────┘

OPTIONAL CONSTRAINTS (LEVEL 3 - SUB-ORGANIZATION):
┌─────────────────────────────────────────────────────────────┐
│ organizations.parent_id: NULLABLE                          │
│ └─ Can be NULL (root organization)                        │
│ └─ Can be UUID (sub-organization)                         │
│ └─ Allows optional nesting                                │
└─────────────────────────────────────────────────────────────┘

OPTIONAL CONSTRAINTS (OPTIONAL LEVELS - DEPT/TEAM/USER):
┌─────────────────────────────────────────────────────────────┐
│ role_assignments.scope_id: NULLABLE                        │
│ └─ Can be NULL (organization-level role)                  │
│ └─ Can be UUID (department/team/user-level role)          │
│ └─ Allows optional scoping                                │
└─────────────────────────────────────────────────────────────┘


═══════════════════════════════════════════════════════════════════════════════
                      AUTHORIZATION FLOW
═══════════════════════════════════════════════════════════════════════════════

User Login:
    │
    ▼
1. Authenticate (email + password)
    │
    ▼
2. Load User from users table
    │
    ├─ Get organization_id (MANDATORY)
    │
    ▼
3. Load User's Roles from role_assignments
    │
    ├─ Get roles at organization level (scope_id = NULL)
    ├─ Get roles at sub-org level (scope_id = sub-org-id)
    ├─ Get roles at optional levels (scope_id = dept/team/user-id)
    │
    ▼
4. Load Permissions
    │
    ├─ From role_permissions (via roles)
    ├─ From user_permissions (direct)
    │
    ▼
5. Check Permission for Request
    │
    ├─ Is permission in effective permissions?
    ├─ Is scope match correct?
    │
    ▼
6. Allow or Deny Request
    │
    ├─ ✅ ALLOW (200 OK)
    └─ ❌ DENY (403 Forbidden)


═══════════════════════════════════════════════════════════════════════════════
                      KEY FINDINGS
═══════════════════════════════════════════════════════════════════════════════

✅ MANDATORY LEVELS (MUST EXIST):
   1. Portal (System-wide administration)
   2. Organization (Tenant/company - every user belongs to one)
   3. Sub-Organization (Optional nested organizations)

❌ OPTIONAL LEVELS (CAN BE CREATED AS NEEDED):
   • Department (via RoleAssignment.scope = 'department')
   • Team (via RoleAssignment.scope = 'team')
   • User (via RoleAssignment.scope = 'user')

🔐 ENFORCEMENT:
   • users.organization_id: NOT NULL (mandatory)
   • organizations.parent_id: NULLABLE (optional nesting)
   • role_assignments.scope_id: NULLABLE (optional scoping)

📊 DATA ISOLATION:
   • All users in organization have organization_id = org-id
   • Sub-org users inherit parent organization_id
   • Complete data isolation per organization


═══════════════════════════════════════════════════════════════════════════════
Document Generated: October 29, 2025
System: ERP Backend API
Analysis: Complete codebase verification
Status: ✅ VERIFIED - 3 Mandatory Levels Confirmed
═══════════════════════════════════════════════════════════════════════════════
